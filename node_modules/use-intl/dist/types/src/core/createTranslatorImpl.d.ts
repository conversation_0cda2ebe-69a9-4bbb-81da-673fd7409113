import AbstractIntlMessages from './AbstractIntlMessages';
import { InitializedIntlConfig } from './IntlConfig';
import { Formatters, IntlCache } from './formatters';
import NestedKeyOf from './utils/NestedKeyOf';
export type CreateTranslatorImplProps<Messages> = Omit<InitializedIntlConfig, 'messages'> & {
    namespace: string;
    messages: Messages;
    formatters: Formatters;
    cache: IntlCache;
};
export default function createTranslatorImpl<Messages extends AbstractIntlMessages, NestedKey extends NestedKeyOf<Messages>>({ messages, namespace, ...rest }: CreateTranslatorImplProps<Messages>, namespacePrefix: string): {
    <TargetKey extends import(".").MessageKeys<import(".").NestedValueOf<Messages, NestedKey>, NestedKeyOf<import(".").NestedValueOf<Messages, NestedKey>>>>(key: TargetKey, values?: import("./TranslationValues").default, formats?: import("./Formats").default): string;
    rich: (key: string, values?: import("./TranslationValues").RichTranslationValues, formats?: import("./Formats").default) => import("react").ReactNode;
    markup(key: Parameters<(key: string, values?: import("./TranslationValues").RichTranslationValues, formats?: import("./Formats").default) => import("react").ReactNode>[0], values: import("./TranslationValues").MarkupTranslationValues, formats?: Parameters<(key: string, values?: import("./TranslationValues").RichTranslationValues, formats?: import("./Formats").default) => import("react").ReactNode>[2]): string;
    raw(key: string): any;
    has(key: Parameters<(key: string, values?: import("./TranslationValues").RichTranslationValues, formats?: import("./Formats").default) => import("react").ReactNode>[0]): boolean;
};
