import { ReactNode } from 'react';
import AbstractIntlMessages from './AbstractIntlMessages';
import Formats from './Formats';
import { InitializedIntlConfig } from './IntlConfig';
import IntlError from './IntlError';
import TranslationValues, { MarkupTranslationValues, RichTranslationValues } from './TranslationValues';
import { Formatters, IntlCache } from './formatters';
import MessageKeys from './utils/MessageKeys';
import NestedKeyOf from './utils/NestedKeyOf';
import NestedValueOf from './utils/NestedValueOf';
export type CreateBaseTranslatorProps<Messages> = InitializedIntlConfig & {
    cache: IntlCache;
    formatters: Formatters;
    defaultTranslationValues?: RichTranslationValues;
    namespace?: string;
    messagesOrError: Messages | IntlError;
};
export default function createBaseTranslator<Messages extends AbstractIntlMessages, NestedKey extends NestedKeyOf<Messages>>(config: Omit<CreateBaseTranslatorProps<Messages>, 'messagesOrError'>): {
    <TargetKey extends MessageKeys<NestedValueOf<Messages, NestedKey>, NestedKeyOf<NestedValueOf<Messages, NestedKey>>>>(key: TargetKey, values?: TranslationValues, formats?: Formats): string;
    rich: (key: string, values?: RichTranslationValues, formats?: Formats) => ReactNode;
    markup(key: Parameters<(key: string, values?: RichTranslationValues, formats?: Formats) => ReactNode>[0], values: MarkupTranslationValues, formats?: Parameters<(key: string, values?: RichTranslationValues, formats?: Formats) => ReactNode>[2]): string;
    raw(key: string): any;
    has(key: Parameters<(key: string, values?: RichTranslationValues, formats?: Formats) => ReactNode>[0]): boolean;
};
