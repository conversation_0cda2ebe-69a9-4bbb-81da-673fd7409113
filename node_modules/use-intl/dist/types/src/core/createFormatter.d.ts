import { ReactElement } from 'react';
import DateTimeFormatOptions from './DateTimeFormatOptions';
import Formats from './Formats';
import IntlError from './IntlError';
import NumberFormatOptions from './NumberFormatOptions';
import RelativeTimeFormatOptions from './RelativeTimeFormatOptions';
import TimeZone from './TimeZone';
import { Formatters, IntlCache } from './formatters';
type Props = {
    locale: string;
    timeZone?: TimeZone;
    onError?(error: IntlError): void;
    formats?: Formats;
    now?: Date;
    /** @private */
    _formatters?: Formatters;
    /** @private */
    _cache?: IntlCache;
};
export default function createFormatter({ _cache: cache, _formatters: formatters, formats, locale, now: globalNow, onError, timeZone: globalTimeZone }: Props): {
    dateTime: (value: Date | number, formatOrOptions?: Extract<keyof IntlFormats["dateTime"], string> | DateTimeFormatOptions) => string;
    number: (value: number | bigint, formatOrOptions?: Extract<keyof IntlFormats["number"], string> | NumberFormatOptions) => string;
    relativeTime: (date: number | Date, nowOrOptions?: RelativeTimeFormatOptions["now"] | RelativeTimeFormatOptions) => string;
    list: <Value extends string | ReactElement<any, string | import("react").JSXElementConstructor<any>>>(value: Iterable<Value>, formatOrOptions?: Extract<keyof IntlFormats["list"], string> | Intl.ListFormatOptions) => Value extends string ? string : Iterable<ReactElement>;
    dateTimeRange: (start: Date | number, end: Date | number, formatOrOptions?: Extract<keyof IntlFormats["dateTime"], string> | DateTimeFormatOptions) => string;
};
export {};
