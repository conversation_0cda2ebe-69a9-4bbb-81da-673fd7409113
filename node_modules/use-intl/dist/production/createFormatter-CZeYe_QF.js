"use strict";var e=require("intl-messageformat"),t=require("react"),r=require("./initializeConfig-AbYTngyP.js");function n(e){return e&&e.__esModule?e:{default:e}}var o=n(e);function a(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let i=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}({});class s extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),a(this,"code",void 0),a(this,"originalMessage",void 0),this.code=e,t&&(this.originalMessage=t)}}function u(e,t){return e?Object.keys(e).reduce(((r,n)=>(r[n]={timeZone:t,...e[n]},r)),{}):e}function c(e,t,n,o){const a=r.joinPath(o,n);if(!t)throw new Error(a);let i=t;return n.split(".").forEach((t=>{const r=i[t];if(null==t||null==r)throw new Error(a+" (".concat(e,")"));i=r})),i}const l=60,m=60*l,f=24*m,g=7*f,E=f*(365/12),d=3*E,I=365*f,S={second:1,seconds:1,minute:l,minutes:l,hour:m,hours:m,day:f,days:f,week:g,weeks:g,month:E,months:E,quarter:d,quarters:d,year:I,years:I};exports.IntlError=s,exports.IntlErrorCode=i,exports.createBaseTranslator=function(e){const n=function(e,t,n){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:r.defaultOnError;try{if(!t)throw new Error(void 0);const r=n?c(e,t,n):t;if(!r)throw new Error(n);return r}catch(e){const t=new s(i.MISSING_MESSAGE,e.message);return o(t),t}}(e.locale,e.messages,e.namespace,e.onError);return function(e){let{cache:n,defaultTranslationValues:a,formats:l,formatters:m,getMessageFallback:f=r.defaultGetMessageFallback,locale:g,messagesOrError:E,namespace:d,onError:I,timeZone:S}=e;const y=E instanceof s;function T(e,t,r){const n=new s(t,r);return I(n),f({error:n,key:e,namespace:d})}function h(e,s,I){if(y)return f({error:E,key:e,namespace:d});const h=E;let M,N;try{M=c(g,h,e,d)}catch(t){return T(e,i.MISSING_MESSAGE,t.message)}if("object"==typeof M){let t,r;return t=Array.isArray(M)?i.INVALID_MESSAGE:i.INSUFFICIENT_PATH,T(e,t,r)}const A=function(e,t){if(t)return;const r=e.replace(/'([{}])/gi,"$1");return/<|{/.test(r)?void 0:r}(M,s);if(A)return A;m.getMessageFormat||(m.getMessageFormat=function(e,t){const n=r.memoFn((function(){return new o.default(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2],{formatters:t,...arguments.length<=3?void 0:arguments[3]})}),e.message);return n}(n,m));try{N=m.getMessageFormat(M,g,function(e,t){const r=t?{...e,dateTime:u(e.dateTime,t)}:e,n=o.default.formats.date,a=t?u(n,t):n,i=o.default.formats.time,s=t?u(i,t):i;return{...r,date:{...a,...r.dateTime},time:{...s,...r.dateTime}}}({...l,...I},S),{formatters:{...m,getDateTimeFormat:(e,t)=>m.getDateTimeFormat(e,{timeZone:S,...t})}})}catch(t){const r=t;return T(e,i.INVALID_MESSAGE,r.message)}try{const e=N.format(function(e){if(0===Object.keys(e).length)return;const r={};return Object.keys(e).forEach((n=>{let o=0;const a=e[n];let i;i="function"==typeof a?e=>{const r=a(e);return t.isValidElement(r)?t.cloneElement(r,{key:n+o++}):r}:a,r[n]=i})),r}({...a,...s}));if(null==e)throw new Error(void 0);return t.isValidElement(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(t){return T(e,i.FORMATTING_ERROR,t.message)}}function M(e,t,r){const n=h(e,t,r);return"string"!=typeof n?T(e,i.INVALID_MESSAGE,void 0):n}return M.rich=h,M.markup=(e,t,r)=>{const n=h(e,t,r);if("string"!=typeof n){const t=new s(i.FORMATTING_ERROR,void 0);return I(t),f({error:t,key:e,namespace:d})}return n},M.raw=e=>{if(y)return f({error:E,key:e,namespace:d});const t=E;try{return c(g,t,e,d)}catch(t){return T(e,i.MISSING_MESSAGE,t.message)}},M.has=e=>{if(y)return!1;try{return c(g,E,e,d),!0}catch(e){return!1}},M}({...e,messagesOrError:n})},exports.createFormatter=function(e){let{_cache:t=r.createCache(),_formatters:n=r.createIntlFormatters(t),formats:o,locale:a,now:u,onError:c=r.defaultOnError,timeZone:d}=e;function y(e){var t;return null!==(t=e)&&void 0!==t&&t.timeZone||(d?e={...e,timeZone:d}:c(new s(i.ENVIRONMENT_FALLBACK,void 0))),e}function T(e,t,r,n){let o;try{o=function(e,t){let r;if("string"==typeof t){if(r=null==e?void 0:e[t],!r){const e=new s(i.MISSING_FORMAT,void 0);throw c(e),e}}else r=t;return r}(t,e)}catch(e){return n()}try{return r(o)}catch(e){return c(new s(i.FORMATTING_ERROR,e.message)),n()}}function h(e,t){return T(t,null==o?void 0:o.dateTime,(t=>(t=y(t),n.getDateTimeFormat(a,t).format(e))),(()=>String(e)))}function M(){return u||(c(new s(i.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:h,number:function(e,t){return T(t,null==o?void 0:o.number,(t=>n.getNumberFormat(a,t).format(e)),(()=>String(e)))},relativeTime:function(e,t){try{let r,o;const i={};t instanceof Date||"number"==typeof t?r=new Date(t):t&&(r=null!=t.now?new Date(t.now):M(),o=t.unit,i.style=t.style,i.numberingSystem=t.numberingSystem),r||(r=M());const s=(new Date(e).getTime()-r.getTime())/1e3;o||(o=function(e){const t=Math.abs(e);return t<l?"second":t<m?"minute":t<f?"hour":t<g?"day":t<E?"week":t<I?"month":"year"}(s)),i.numeric="second"===o?"auto":"always";const u=function(e,t){return Math.round(e/S[t])}(s,o);return n.getRelativeTimeFormat(a,i).format(u,o)}catch(t){return c(new s(i.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t){const r=[],i=new Map;let s=0;for(const t of e){let e;"object"==typeof t?(e=String(s),i.set(e,t)):e=String(t),r.push(e),s++}return T(t,null==o?void 0:o.list,(e=>{const t=n.getListFormat(a,e).formatToParts(r).map((e=>"literal"===e.type?e.value:i.get(e.value)||e.value));return i.size>0?t:t.join("")}),(()=>String(e)))},dateTimeRange:function(e,t,r){return T(r,null==o?void 0:o.dateTime,(r=>(r=y(r),n.getDateTimeFormat(a,r).formatRange(e,t))),(()=>[h(e),h(t)].join(" – ")))}}},exports.resolveNamespace=function(e,t){return e===t?void 0:e.slice((t+".").length)};
