"use strict";var e=require("@formatjs/fast-memoize");function t(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(".")}function r(e){return t(e.namespace,e.key)}function a(e){console.error(e)}function n(t,r){return e.memoize(t,{cache:(a=r,{create:()=>({get:e=>a[e],set(e,t){a[e]=t}})}),strategy:e.strategies.variadic});var a}function s(e,t){return n((function(){for(var t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];return new e(...r)}),t)}exports.createCache=function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}},exports.createIntlFormatters=function(e){return{getDateTimeFormat:s(Intl.DateTimeFormat,e.dateTime),getNumberFormat:s(Intl.NumberFormat,e.number),getPluralRules:s(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:s(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:s(Intl.ListFormat,e.list),getDisplayNames:s(Intl.DisplayNames,e.displayNames)}},exports.defaultGetMessageFallback=r,exports.defaultOnError=a,exports.initializeConfig=function(e){let{getMessageFallback:t,messages:n,onError:s,...i}=e;return{...i,messages:n,onError:s||a,getMessageFallback:t||r}},exports.joinPath=t,exports.memoFn=n;
