"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("./createFormatter-CZeYe_QF.js"),r=require("./initializeConfig-AbYTngyP.js");require("intl-messageformat"),require("react"),require("@formatjs/fast-memoize"),exports.IntlError=e.IntlError,exports.IntlErrorCode=e.IntlErrorCode,exports.createFormatter=e.createFormatter,exports._createCache=r.createCache,exports._createIntlFormatters=r.createIntlFormatters,exports.initializeConfig=r.initializeConfig,exports.createTranslator=function(t){let{_cache:a=r.createCache(),_formatters:s=r.createIntlFormatters(a),getMessageFallback:o=r.defaultGetMessageFallback,messages:c,namespace:n,onError:i=r.defaultOnError,...l}=t;return function(r,t){let{messages:a,namespace:s,...o}=r;return a=a[t],s=e.resolveNamespace(s,t),e.createBaseTranslator({...o,messages:a,namespace:s})}({...l,onError:i,cache:a,formatters:s,getMessageFallback:o,messages:{"!":c},namespace:n?"!.".concat(n):"!"},"!")};
