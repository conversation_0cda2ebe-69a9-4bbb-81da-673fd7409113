"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("./_IntlProvider.js"),t=require("./_useLocale-CpTrqBDt.js"),r=require("react"),o=require("./createFormatter-CZeYe_QF.js");require("./initializeConfig-AbYTngyP.js"),require("@formatjs/fast-memoize"),require("./IntlContext-DcFt0tgW.js"),require("intl-messageformat");let n=!1;const s="undefined"==typeof window;function a(){return new Date}exports.IntlProvider=e.IntlProvider,exports.useLocale=t.useLocale,exports.useFormatter=function(){const{formats:e,formatters:n,locale:s,now:a,onError:u,timeZone:l}=t.useIntlContext();return r.useMemo((()=>o.createFormatter({formats:e,locale:s,now:a,onError:u,timeZone:l,_formatters:n})),[e,n,a,s,u,l])},exports.useMessages=function(){const e=t.useIntlContext();if(!e.messages)throw new Error(void 0);return e.messages},exports.useNow=function(e){const o=null==e?void 0:e.updateInterval,{now:n}=t.useIntlContext(),[s,u]=r.useState(n||a());return r.useEffect((()=>{if(!o)return;const e=setInterval((()=>{u(a())}),o);return()=>{clearInterval(e)}}),[n,o]),null==o&&n?n:s},exports.useTimeZone=function(){return t.useIntlContext().timeZone},exports.useTranslations=function(e){return function(e,a,u){const{cache:l,defaultTranslationValues:i,formats:c,formatters:m,getMessageFallback:f,locale:I,onError:d,timeZone:x}=t.useIntlContext(),p=e[u],v=o.resolveNamespace(a,u);return x||n||!s||(n=!0,d(new o.IntlError(o.IntlErrorCode.ENVIRONMENT_FALLBACK,void 0))),r.useMemo((()=>o.createBaseTranslator({cache:l,formatters:m,getMessageFallback:f,messages:p,defaultTranslationValues:i,namespace:v,onError:d,formats:c,locale:I,timeZone:x})),[l,m,f,p,i,v,d,c,I,x])}({"!":t.useIntlContext().messages},e?"!.".concat(e):"!","!")};
