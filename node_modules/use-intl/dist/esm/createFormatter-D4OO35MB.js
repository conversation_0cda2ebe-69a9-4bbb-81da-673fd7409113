import e from"intl-messageformat";import{isValidElement as t,cloneElement as n}from"react";import{I as r,a as o,j as a,d as s,e as i,m as c,b as u,c as m}from"./initializeConfig-D2A8plWf.js";function l(e,t){return e?Object.keys(e).reduce(((n,r)=>(n[r]={timeZone:t,...e[r]},n)),{}):e}function f(e,t,n,r){const o=a(r,n);if(!t)throw new Error("No messages available at `".concat(r,"`."));let s=t;return n.split(".").forEach((t=>{const n=s[t];if(null==t||null==n)throw new Error("Could not resolve `".concat(o,"` in messages for locale `").concat(e,"`."));s=n})),s}function g(u){const m=function(e,t,n){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:s;try{if(!t)throw new Error("No messages were configured on the provider.");const r=n?f(e,t,n):t;if(!r)throw new Error("No messages for namespace `".concat(n,"` found."));return r}catch(e){const t=new r(o.MISSING_MESSAGE,e.message);return a(t),t}}(u.locale,u.messages,u.namespace,u.onError);return function(s){let{cache:u,defaultTranslationValues:m,formats:g,formatters:d,getMessageFallback:h=i,locale:p,messagesOrError:y,namespace:v,onError:w,timeZone:E}=s;const T=y instanceof r;function S(e,t,n){const o=new r(t,n);return w(o),h({error:o,key:e,namespace:v})}function b(r,s,i){if(T)return h({error:y,key:r,namespace:v});const w=y;let b,M;try{b=f(p,w,r,v)}catch(e){return S(r,o.MISSING_MESSAGE,e.message)}if("object"==typeof b){let e,t;return Array.isArray(b)?(e=o.INVALID_MESSAGE,t="Message at `".concat(a(v,r),"` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages")):(e=o.INSUFFICIENT_PATH,t="Message at `".concat(a(v,r),"` resolved to an object, but only strings are supported. Use a `.` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages")),S(r,e,t)}const I=function(e,t){if(t)return;const n=e.replace(/'([{}])/gi,"$1");return/<|{/.test(n)?void 0:n}(b,s);if(I)return I;d.getMessageFormat||(d.getMessageFormat=function(t,n){const r=c((function(){return new e(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2],{formatters:n,...arguments.length<=3?void 0:arguments[3]})}),t.message);return r}(u,d));try{M=d.getMessageFormat(b,p,function(t,n){const r=n?{...t,dateTime:l(t.dateTime,n)}:t,o=e.formats.date,a=n?l(o,n):o,s=e.formats.time,i=n?l(s,n):s;return{...r,date:{...a,...r.dateTime},time:{...i,...r.dateTime}}}({...g,...i},E),{formatters:{...d,getDateTimeFormat:(e,t)=>d.getDateTimeFormat(e,{timeZone:E,...t})}})}catch(e){const t=e;return S(r,o.INVALID_MESSAGE,t.message+("originalMessage"in t?" (".concat(t.originalMessage,")"):""))}try{const e=M.format(function(e){if(0===Object.keys(e).length)return;const r={};return Object.keys(e).forEach((o=>{let a=0;const s=e[o];let i;i="function"==typeof s?e=>{const r=s(e);return t(r)?n(r,{key:o+a++}):r}:s,r[o]=i})),r}({...m,...s}));if(null==e)throw new Error("Unable to format `".concat(r,"` in ").concat(v?"namespace `".concat(v,"`"):"messages"));return t(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(e){return S(r,o.FORMATTING_ERROR,e.message)}}function M(e,t,n){const r=b(e,t,n);return"string"!=typeof r?S(e,o.INVALID_MESSAGE,"The message `".concat(e,"` in ").concat(v?"namespace `".concat(v,"`"):"messages"," didn't resolve to a string. If you want to format rich text, use `t.rich` instead.")):r}return M.rich=b,M.markup=(e,t,n)=>{const a=b(e,t,n);if("string"!=typeof a){const t=new r(o.FORMATTING_ERROR,"`t.markup` only accepts functions for formatting that receive and return strings.\n\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})");return w(t),h({error:t,key:e,namespace:v})}return a},M.raw=e=>{if(T)return h({error:y,key:e,namespace:v});const t=y;try{return f(p,t,e,v)}catch(t){return S(e,o.MISSING_MESSAGE,t.message)}},M.has=e=>{if(T)return!1;try{return f(p,y,e,v),!0}catch(e){return!1}},M}({...u,messagesOrError:m})}function d(e,t){return e===t?void 0:e.slice((t+".").length)}const h=60,p=60*h,y=24*p,v=7*y,w=y*(365/12),E=3*w,T=365*y,S={second:1,seconds:1,minute:h,minutes:h,hour:p,hours:p,day:y,days:y,week:v,weeks:v,month:w,months:w,quarter:E,quarters:E,year:T,years:T};function b(e){let{_cache:t=u(),_formatters:n=m(t),formats:a,locale:i,now:c,onError:l=s,timeZone:f}=e;function g(e){var t;return null!==(t=e)&&void 0!==t&&t.timeZone||(f?e={...e,timeZone:f}:l(new r(o.ENVIRONMENT_FALLBACK,"The `timeZone` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone"))),e}function d(e,t,n,a){let s;try{s=function(e,t){let n;if("string"==typeof t){const a=t;if(n=null==e?void 0:e[a],!n){const e=new r(o.MISSING_FORMAT,"Format `".concat(a,"` is not available. You can configure it on the provider or provide custom options."));throw l(e),e}}else n=t;return n}(t,e)}catch(e){return a()}try{return n(s)}catch(e){return l(new r(o.FORMATTING_ERROR,e.message)),a()}}function E(e,t){return d(t,null==a?void 0:a.dateTime,(t=>(t=g(t),n.getDateTimeFormat(i,t).format(e))),(()=>String(e)))}function b(){return c||(l(new r(o.ENVIRONMENT_FALLBACK,"The `now` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#now")),new Date)}return{dateTime:E,number:function(e,t){return d(t,null==a?void 0:a.number,(t=>n.getNumberFormat(i,t).format(e)),(()=>String(e)))},relativeTime:function(e,t){try{let r,o;const a={};t instanceof Date||"number"==typeof t?r=new Date(t):t&&(r=null!=t.now?new Date(t.now):b(),o=t.unit,a.style=t.style,a.numberingSystem=t.numberingSystem),r||(r=b());const s=(new Date(e).getTime()-r.getTime())/1e3;o||(o=function(e){const t=Math.abs(e);return t<h?"second":t<p?"minute":t<y?"hour":t<v?"day":t<w?"week":t<T?"month":"year"}(s)),a.numeric="second"===o?"auto":"always";const c=function(e,t){return Math.round(e/S[t])}(s,o);return n.getRelativeTimeFormat(i,a).format(c,o)}catch(t){return l(new r(o.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t){const r=[],o=new Map;let s=0;for(const t of e){let e;"object"==typeof t?(e=String(s),o.set(e,t)):e=String(t),r.push(e),s++}return d(t,null==a?void 0:a.list,(e=>{const t=n.getListFormat(i,e).formatToParts(r).map((e=>"literal"===e.type?e.value:o.get(e.value)||e.value));return o.size>0?t:t.join("")}),(()=>String(e)))},dateTimeRange:function(e,t,r){return d(r,null==a?void 0:a.dateTime,(r=>(r=g(r),n.getDateTimeFormat(i,r).formatRange(e,t))),(()=>[E(e),E(t)].join(" – ")))}}}export{g as a,b as c,d as r};
