import{b as e,c as r,e as t,d as s}from"./initializeConfig-D2A8plWf.js";export{I as IntlError,a as IntlErrorCode,i as initializeConfig}from"./initializeConfig-D2A8plWf.js";import{r as o,a as m}from"./createFormatter-D4OO35MB.js";export{c as createFormatter}from"./createFormatter-D4OO35MB.js";import"@formatjs/fast-memoize";import"intl-messageformat";import"react";function n(a){let{_cache:i=e(),_formatters:c=r(i),getMessageFallback:n=t,messages:f,namespace:l,onError:g=s,...p}=a;return function(e,a){let{messages:r,namespace:t,...s}=e;return r=r[a],t=o(t,a),m({...s,messages:r,namespace:t})}({...p,onError:g,cache:i,formatters:c,getMessageFallback:n,messages:{"!":f},namespace:l?"!.".concat(l):"!"},"!")}export{e as _createCache,r as _createIntlFormatters,n as createTranslator};
