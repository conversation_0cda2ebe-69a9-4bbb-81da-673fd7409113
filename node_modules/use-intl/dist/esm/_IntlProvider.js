import e,{use<PERSON>emo as a}from"react";import{b as o,c as t,i as r}from"./initializeConfig-D2A8plWf.js";import{I as s}from"./IntlContext-DoS4CDM3.js";import"@formatjs/fast-memoize";function l(l){let{children:n,defaultTranslationValues:i,formats:m,getMessageFallback:c,locale:f,messages:g,now:u,onError:p,timeZone:d}=l;const b=a((()=>o()),[f]),j=a((()=>t(b)),[b]),E=a((()=>({...r({locale:f,defaultTranslationValues:i,formats:m,getMessageFallback:c,messages:g,now:u,onError:p,timeZone:d}),formatters:j,cache:b})),[b,i,m,j,c,f,g,u,p,d]);return e.createElement(s.Provider,{value:E},n)}export{l as IntlProvider};
