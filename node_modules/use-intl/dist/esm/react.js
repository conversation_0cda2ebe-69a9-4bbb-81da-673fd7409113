export{IntlProvider}from"./_IntlProvider.js";import{a as e}from"./_useLocale-7W3qm3h_.js";export{u as useLocale}from"./_useLocale-7W3qm3h_.js";import{useMemo as t,useState as o,useEffect as r}from"react";import{r as n,a,c as s}from"./createFormatter-D4OO35MB.js";import{I as i,a as m}from"./initializeConfig-D2A8plWf.js";import"./IntlContext-DoS4CDM3.js";import"intl-messageformat";import"@formatjs/fast-memoize";let c=!1;const l="undefined"==typeof window;function f(o){return function(o,r,s){const{cache:f,defaultTranslationValues:u,formats:d,formatters:g,getMessageFallback:p,locale:v,onError:h,timeZone:w}=e(),I=o[s],j=n(r,s);return w||c||!l||(c=!0,h(new i(m.ENVIRONMENT_FALLBACK,"There is no `timeZone` configured, this can lead to markup mismatches caused by environment differences. Consider adding a global default: https://next-intl.dev/docs/configuration#time-zone"))),t((()=>a({cache:f,formatters:g,getMessageFallback:p,messages:I,defaultTranslationValues:u,namespace:j,onError:h,formats:d,locale:v,timeZone:w})),[f,g,p,I,u,j,h,d,v,w])}({"!":e().messages},o?"!.".concat(o):"!","!")}function d(){return new Date}function g(t){const n=null==t?void 0:t.updateInterval,{now:a}=e(),[s,i]=o(a||d());return r((()=>{if(!n)return;const e=setInterval((()=>{i(d())}),n);return()=>{clearInterval(e)}}),[a,n]),null==n&&a?a:s}function p(){return e().timeZone}function v(){const t=e();if(!t.messages)throw new Error("No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages");return t.messages}function h(){const{formats:o,formatters:r,locale:n,now:a,onError:i,timeZone:m}=e();return t((()=>s({formats:o,locale:n,now:a,onError:i,timeZone:m,_formatters:r})),[o,r,a,n,i,m])}export{h as useFormatter,v as useMessages,g as useNow,p as useTimeZone,f as useTranslations};
