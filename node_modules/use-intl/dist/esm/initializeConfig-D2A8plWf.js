import{memoize as e,strategies as t}from"@formatjs/fast-memoize";function n(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let r=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}({});class a extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),n(this,"code",void 0),n(this,"originalMessage",void 0),this.code=e,t&&(this.originalMessage=t)}}function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(Boolean).join(".")}function i(e){return o(e.namespace,e.key)}function s(e){console.error(e)}function l(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function u(n,r){return e(n,{cache:(a=r,{create:()=>({get:e=>a[e],set(e,t){a[e]=t}})}),strategy:t.variadic});var a}function c(e,t){return u((function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return new e(...n)}),t)}function m(e){return{getDateTimeFormat:c(Intl.DateTimeFormat,e.dateTime),getNumberFormat:c(Intl.NumberFormat,e.number),getPluralRules:c(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:c(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:c(Intl.ListFormat,e.list),getDisplayNames:c(Intl.DisplayNames,e.displayNames)}}function f(e,t,n){Object.entries(e).forEach((e=>{let[r,a]=e;if(r.includes(".")){let e=r;n&&(e+=" (at ".concat(n,")")),t.push(e)}null!=a&&"object"==typeof a&&f(a,t,o(n,r))}))}function I(e,t){const n=[];f(e,n),n.length>0&&t(new a(r.INVALID_KEY,'Namespace keys can not contain the character "." as this is used to express nesting. Please remove it or replace it with another character.\n\nInvalid '.concat(1===n.length?"key":"keys",": ").concat(n.join(", "),'\n\nIf you\'re migrating from a flat structure, you can convert your messages as follows:\n\nimport {set} from "lodash";\n\nconst input = {\n  "one.one": "1.1",\n  "one.two": "1.2",\n  "two.one.one": "2.1.1"\n};\n\nconst output = Object.entries(input).reduce(\n  (acc, [key, value]) => set(acc, key, value),\n  {}\n);\n\n// Output:\n//\n// {\n//   "one": {\n//     "one": "1.1",\n//     "two": "1.2"\n//   },\n//   "two": {\n//     "one": {\n//       "one": "2.1.1"\n//     }\n//   }\n// }\n')))}function g(e){let{getMessageFallback:t,messages:n,onError:r,...a}=e;const o=r||s,l=t||i;return n&&I(n,o),{...a,messages:n,onError:o,getMessageFallback:l}}export{a as I,r as a,l as b,m as c,s as d,i as e,g as i,o as j,u as m};
