'use strict';

var React = require('react');
var IntlContext = require('./IntlContext-BKfsnzBx.js');

function useIntlContext() {
  const context = React.useContext(IntlContext.IntlContext);
  if (!context) {
    throw new Error('No intl context found. Have you configured the provider? See https://next-intl.dev/docs/usage/configuration#client-server-components' );
  }
  return context;
}

function useLocale() {
  return useIntlContext().locale;
}

exports.useIntlContext = useIntlContext;
exports.useLocale = useLocale;
