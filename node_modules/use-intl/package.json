{"name": "use-intl", "version": "3.26.5", "sideEffects": false, "author": "<PERSON> <<EMAIL>>", "description": "Internationalization (i18n) for React", "license": "MIT", "homepage": "https://next-intl.dev", "repository": {"type": "git", "url": "https://github.com/amannn/next-intl/tree/main/packages/use-intl"}, "scripts": {"build": "rm -rf dist && rollup -c", "test": "TZ=Europe/Berlin vitest", "lint": "pnpm run lint:source && pnpm run lint:package", "lint:source": "eslint src test && tsc --noEmit && pnpm run lint:prettier", "lint:package": "publint && attw --pack", "lint:prettier": "prettier src --check", "prepublishOnly": "turbo build", "size": "size-limit"}, "main": "./dist/index.js", "module": "dist/esm/index.js", "typings": "./dist/types/src/index.d.ts", "exports": {".": {"types": "./dist/types/src/index.d.ts", "default": "./dist/index.js"}, "./core": {"types": "./core.d.ts", "default": "./dist/core.js"}, "./react": {"types": "./react.d.ts", "default": "./dist/react.js"}, "./_useLocale": {"types": "./_useLocale.d.ts", "default": "./dist/_useLocale.js"}, "./_IntlProvider": {"types": "./_IntlProvider.d.ts", "default": "./dist/_IntlProvider.js"}}, "files": ["dist", "core.d.ts", "react.d.ts", "_useLocale.d.ts", "_IntlProvider.d.ts"], "keywords": ["react", "intl", "i18n", "internationalization", "localization", "translate", "translation", "format", "formatting"], "dependencies": {"@formatjs/fast-memoize": "^2.2.0", "intl-messageformat": "^10.5.14"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc <19.0.0 || ^19.0.0"}, "gitHead": "663c539ea627e808e2b8ed432db123a9825e5845"}