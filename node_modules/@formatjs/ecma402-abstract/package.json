{"name": "@formatjs/ecma402-abstract", "description": "A collection of implementation for ECMAScript abstract operations", "version": "2.3.4", "license": "MIT", "author": "<PERSON> <<EMAIL>", "sideEffects": false, "types": "index.d.ts", "dependencies": {"decimal.js": "^10.4.3", "tslib": "^2.8.0", "@formatjs/fast-memoize": "2.2.7", "@formatjs/intl-localematcher": "0.6.1"}, "bugs": "https://github.com/formatjs/formatjs/issues", "gitHead": "a7842673d8ad205171ad7c8cb8bb2f318b427c0c", "homepage": "https://github.com/formatjs/formatjs", "keywords": ["abstract", "ecma262", "ecma402", "es", "format", "i18n", "intl", "javascript", "relative"], "main": "index.js", "module": "lib/index.js", "repository": "**************:formatjs/formatjs.git"}