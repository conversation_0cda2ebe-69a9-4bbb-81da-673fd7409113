{"name": "yet-another-react-lightbox", "version": "3.21.7", "description": "Modern React lightbox component", "author": "<PERSON>", "license": "MIT", "type": "module", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./core": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./styles.css": {"types": "./dist/styles.css.d.ts", "default": "./dist/styles.css"}, "./plugins": {"types": "./dist/plugins/index.d.ts", "default": "./dist/plugins/index.js"}, "./plugins/captions": {"types": "./dist/plugins/captions/index.d.ts", "default": "./dist/plugins/captions/index.js"}, "./plugins/captions.css": {"types": "./dist/plugins/captions/captions.css.d.ts", "default": "./dist/plugins/captions/captions.css"}, "./plugins/counter": {"types": "./dist/plugins/counter/index.d.ts", "default": "./dist/plugins/counter/index.js"}, "./plugins/counter.css": {"types": "./dist/plugins/counter/counter.css.d.ts", "default": "./dist/plugins/counter/counter.css"}, "./plugins/download": {"types": "./dist/plugins/download/index.d.ts", "default": "./dist/plugins/download/index.js"}, "./plugins/fullscreen": {"types": "./dist/plugins/fullscreen/index.d.ts", "default": "./dist/plugins/fullscreen/index.js"}, "./plugins/inline": {"types": "./dist/plugins/inline/index.d.ts", "default": "./dist/plugins/inline/index.js"}, "./plugins/share": {"types": "./dist/plugins/share/index.d.ts", "default": "./dist/plugins/share/index.js"}, "./plugins/slideshow": {"types": "./dist/plugins/slideshow/index.d.ts", "default": "./dist/plugins/slideshow/index.js"}, "./plugins/thumbnails": {"types": "./dist/plugins/thumbnails/index.d.ts", "default": "./dist/plugins/thumbnails/index.js"}, "./plugins/thumbnails.css": {"types": "./dist/plugins/thumbnails/thumbnails.css.d.ts", "default": "./dist/plugins/thumbnails/thumbnails.css"}, "./plugins/video": {"types": "./dist/plugins/video/index.d.ts", "default": "./dist/plugins/video/index.js"}, "./plugins/zoom": {"types": "./dist/plugins/zoom/index.d.ts", "default": "./dist/plugins/zoom/index.js"}}, "typesVersions": {"*": {"*": ["dist/index.d.ts"], "core": ["dist/index.d.ts"], "styles.css": ["dist/styles.css.d.ts"], "plugins": ["dist/plugins/index.d.ts"], "plugins/captions": ["dist/plugins/captions/index.d.ts"], "plugins/captions.css": ["dist/plugins/captions/captions.css.d.ts"], "plugins/counter": ["dist/plugins/counter/index.d.ts"], "plugins/counter.css": ["dist/plugins/counter/counter.css.d.ts"], "plugins/download": ["dist/plugins/download/index.d.ts"], "plugins/fullscreen": ["dist/plugins/fullscreen/index.d.ts"], "plugins/inline": ["dist/plugins/inline/index.d.ts"], "plugins/share": ["dist/plugins/share/index.d.ts"], "plugins/slideshow": ["dist/plugins/slideshow/index.d.ts"], "plugins/thumbnails": ["dist/plugins/thumbnails/index.d.ts"], "plugins/thumbnails.css": ["dist/plugins/thumbnails/thumbnails.css.d.ts"], "plugins/video": ["dist/plugins/video/index.d.ts"], "plugins/zoom": ["dist/plugins/zoom/index.d.ts"]}}, "files": ["dist"], "sideEffects": ["*.css"], "homepage": "https://yet-another-react-lightbox.com", "repository": {"type": "git", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>/yet-another-react-lightbox.git"}, "bugs": {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>/yet-another-react-lightbox/issues"}, "engines": {"node": ">=14"}, "publishConfig": {"access": "public", "provenance": true}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "keywords": ["react", "image", "photo", "lightbox", "react lightbox", "react image lightbox", "react photo lightbox"]}