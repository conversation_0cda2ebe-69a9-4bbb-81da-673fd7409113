{"name": "next-intl", "version": "3.26.5", "sideEffects": false, "author": "<PERSON> <<EMAIL>>", "funding": [{"type": "individual", "url": "https://github.com/sponsors/amannn"}], "description": "Internationalization (i18n) for Next.js", "license": "MIT", "homepage": "https://next-intl.dev", "repository": {"type": "git", "url": "https://github.com/amannn/next-intl"}, "scripts": {"build": "rm -rf dist && rollup -c", "test": "TZ=Europe/Berlin vitest", "lint": "pnpm run lint:source && pnpm run lint:package", "lint:source": "eslint src test && tsc --noEmit && pnpm run lint:prettier", "lint:package": "publint && attw --pack", "lint:prettier": "prettier src --check", "prepublishOnly": "turbo build && cp ../../README.md .", "postpublish": "git checkout . && rm ./README.md", "size": "size-limit"}, "main": "./dist/index.react-client.js", "module": "./dist/esm/index.react-client.js", "typings": "./dist/types/src/index.react-client.d.ts", "exports": {".": {"types": "./dist/types/src/index.react-client.d.ts", "react-server": "./dist/esm/index.react-server.js", "default": "./dist/index.react-client.js"}, "./server": {"types": "./server.d.ts", "react-server": "./dist/esm/server.react-server.js", "default": "./dist/server.react-client.js"}, "./config": {"types": "./config.d.ts", "default": "./dist/config.js"}, "./middleware": {"types": "./middleware.d.ts", "default": "./dist/middleware.js"}, "./navigation": {"types": "./navigation.d.ts", "react-server": "./dist/esm/navigation.react-server.js", "default": "./dist/navigation.react-client.js"}, "./routing": {"types": "./routing.d.ts", "default": "./dist/routing.js"}, "./plugin": {"types": "./plugin.d.ts", "default": "./dist/plugin.js"}}, "files": ["dist", "server.d.ts", "navigation.d.ts", "middleware.d.ts", "plugin.d.ts", "routing.d.ts", "config.d.ts"], "keywords": ["react", "intl", "i18n", "internationalization", "localization", "translate", "translation", "format", "formatting", "next", "next.js"], "dependencies": {"@formatjs/intl-localematcher": "^0.5.4", "negotiator": "^1.0.0", "use-intl": "^3.26.5"}, "peerDependencies": {"next": "^10.0.0 || ^11.0.0 || ^12.0.0 || ^13.0.0 || ^14.0.0 || ^15.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc <19.0.0 || ^19.0.0"}, "gitHead": "663c539ea627e808e2b8ed432db123a9825e5845"}