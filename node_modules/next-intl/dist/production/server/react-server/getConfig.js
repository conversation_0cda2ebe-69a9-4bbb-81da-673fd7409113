"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("next/navigation"),t=require("react"),r=require("use-intl/core"),n=require("../../shared/utils.js"),o=require("./RequestLocale.js"),a=require("./RequestLocaleLegacy.js");function c(e){return e&&e.__esModule?e:{default:e}}var i=c(require("next-intl/config"));const u=t.cache((function(){return new Date}));const s=t.cache((function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}));const l=t.cache((async function(t,r){const c={get locale(){return r||a.getRequestLocale()},get requestLocale(){return r?Promise.resolve(r):o.getRequestLocale()}};let i=t(c);n.isPromise(i)&&(i=await i);let l=i.locale;return l||(l=await c.requestLocale,l||e.notFound()),{...i,locale:l,now:i.now||u(),timeZone:i.timeZone||s()}})),q=t.cache(r._createIntlFormatters),f=t.cache(r._createCache);const d=t.cache((async function(e){const t=await l(i.default,e);return{...r.initializeConfig(t),_formatters:q(f())}}));exports.default=d;
