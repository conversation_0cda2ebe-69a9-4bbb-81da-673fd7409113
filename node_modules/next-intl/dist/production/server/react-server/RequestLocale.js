"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("next/headers"),t=require("react"),r=require("../../shared/constants.js"),s=require("../../shared/utils.js"),n=require("./RequestLocaleCache.js");const i=t.cache((async function(){const t=e.headers();return s.isPromise(t)?await t:t}));const a=t.cache((async function(){let e;try{e=(await i()).get(r.HEADER_LOCALE_NAME)||void 0}catch(e){if(e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest){const t=new Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e});throw t.digest=e.digest,t}throw e}return e}));exports.getRequestLocale=async function(){return n.getCachedRequestLocale()||await a()};
