"use strict";function t(t){return function(t){return"object"==typeof t?null==t.host&&null==t.hostname:!/^[a-z]+:/i.test(t)}(t)&&!function(t){const e="object"==typeof t?t.pathname:t;return null!=e&&!e.startsWith("/")}(t)}function e(t,e){let r;return"string"==typeof t?r=n(e,t):(r={...t},t.pathname&&(r.pathname=n(e,t.pathname))),r}function n(t,e){let n=t;return/^\/(\?.*)?$/.test(e)&&(e=e.slice(1)),n+=e,n}function r(t,e){return e===t||e.startsWith("".concat(t,"/"))}function o(t){const e=function(){try{return"true"===process.env._next_intl_trailing_slash}catch(t){return!1}}();if("/"!==t){const n=t.endsWith("/");e&&!n?t+="/":!e&&n&&(t=t.slice(0,-1))}return t}function i(t){return"/"+t}function u(t){const e=t.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return new RegExp("^".concat(e,"$"))}function s(t){return t.includes("[[...")}function c(t){return t.includes("[...")}function f(t){return t.includes("[")}function a(t,e){const n=t.split("/"),r=e.split("/"),o=Math.max(n.length,r.length);for(let t=0;t<o;t++){const e=n[t],o=r[t];if(!e&&o)return-1;if(e&&!o)return 1;if(e||o){if(!f(e)&&f(o))return-1;if(f(e)&&!f(o))return 1;if(!c(e)&&c(o))return-1;if(c(e)&&!c(o))return 1;if(!s(e)&&s(o))return-1;if(s(e)&&!s(o))return 1}}return 0}Object.defineProperty(exports,"__esModule",{value:!0}),exports.getLocaleAsPrefix=i,exports.getLocalePrefix=function(t,e){var n;return"never"!==e.mode&&(null===(n=e.prefixes)||void 0===n?void 0:n[t])||i(t)},exports.getSortedPathnames=function(t){return t.sort(a)},exports.hasPathnamePrefixed=r,exports.isLocalizableHref=t,exports.isPromise=function(t){return"function"==typeof t.then},exports.localizeHref=function(n,o){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:o,u=arguments.length>3?arguments[3]:void 0,s=arguments.length>4?arguments[4]:void 0;if(!t(n))return n;const c=o!==i,f=r(s,u);return(c||f)&&null!=s?e(n,s):n},exports.matchesPathname=function(t,e){const n=o(t),r=o(e);return u(n).test(r)},exports.normalizeTrailingSlash=o,exports.prefixHref=e,exports.prefixPathname=n,exports.templateToRegex=u,exports.unprefixPathname=function(t,e){return t.replace(new RegExp("^".concat(e)),"")||"/"};
