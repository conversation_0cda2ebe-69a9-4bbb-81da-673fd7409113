"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../../_virtual/_rollupPluginBabelHelpers.js"),a=require("next/navigation"),r=require("react"),t=require("../../routing/config.js"),n=require("../../shared/utils.js"),o=require("./BaseLink.js"),i=require("./utils.js");function l(e){return e&&e.__esModule?e:{default:e}}var u=l(r);exports.default=function(l,c){const s=t.receiveRoutingConfig(c||{}),d=s.pathnames,f="as-needed"===s.localePrefix.mode&&s.domains||void 0;function m(a,t){let i,c,m,{href:p,locale:q,...v}=a;"object"==typeof p?(i=p.pathname,m=p.query,c=p.params):i=p;const y=n.isLocalizableHref(p),P=l(),g=n.isPromise(P)?r.use(P):P,j=y?h({locale:q||g,href:null==d?i:{pathname:i,params:c}},null!=q||f||void 0):i;return u.default.createElement(o.default,e.extends({ref:t,defaultLocale:s.defaultLocale,href:"object"==typeof p?{...p,pathname:j}:j,locale:q,localeCookie:s.localeCookie,unprefixed:f&&y?{domains:s.domains.reduce(((e,a)=>(e[a.domain]=a.defaultLocale,e)),{}),pathname:h({locale:g,href:null==d?{pathname:i,query:m}:{pathname:i,query:m,params:c}},!1)}:void 0},v))}const p=r.forwardRef(m);function h(e,a){const{href:r,locale:t}=e;let n;return null==d?"object"==typeof r?(n=r.pathname,r.query&&(n+=i.serializeSearchParams(r.query))):n=r:n=i.compileLocalizedPathname({locale:t,...i.normalizeNameOrNameWithParams(r),pathnames:s.pathnames}),i.applyPathnamePrefix(n,t,s,e.domain,a)}function q(e){return function(a){for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];return e(h(a,a.domain?void 0:f),...t)}}const v=q(a.redirect),y=q(a.permanentRedirect);return{config:s,Link:p,redirect:v,permanentRedirect:y,getPathname:h}};
