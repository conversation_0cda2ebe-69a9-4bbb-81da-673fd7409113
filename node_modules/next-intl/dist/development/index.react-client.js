'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var index = require('./react-client/index.js');
var useLocale = require('./react-client/useLocale.js');
var NextIntlClientProvider = require('./shared/NextIntlClientProvider.js');
var useIntl = require('use-intl');



exports.useFormatter = index.useFormatter;
exports.useTranslations = index.useTranslations;
exports.useLocale = useLocale.default;
exports.NextIntlClientProvider = NextIntlClientProvider.default;
Object.keys(useIntl).forEach(function (k) {
	if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
		enumerable: true,
		get: function () { return useIntl[k]; }
	});
});
