"use client";
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _rollupPluginBabelHelpers = require('../_virtual/_rollupPluginBabelHelpers.js');
var React = require('react');
var _IntlProvider = require('use-intl/_IntlProvider');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var React__default = /*#__PURE__*/_interopDefault(React);

function NextIntlClientProvider(_ref) {
  let {
    locale,
    ...rest
  } = _ref;
  // TODO: We could call `useParams` here to receive a default value
  // for `locale`, but this would require dropping Next.js <13.

  if (!locale) {
    throw new Error('Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl.dev/docs/configuration#locale' );
  }
  return /*#__PURE__*/React__default.default.createElement(_IntlProvider.IntlProvider, _rollupPluginBabelHelpers.extends({
    locale: locale
  }, rest));
}

exports.default = NextIntlClientProvider;
