'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var utils = require('../../shared/utils.js');

// Minor false positive: A route that has both optional and
// required params will allow optional params.

// For `Link`

// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)

function normalizeNameOrNameWithParams(href) {
  return typeof href === 'string' ? {
    pathname: href
  } : href;
}
function serializeSearchParams(searchParams) {
  function serializeValue(value) {
    return String(value);
  }
  const urlSearchParams = new URLSearchParams();
  for (const [key, value] of Object.entries(searchParams)) {
    if (Array.isArray(value)) {
      value.forEach(cur => {
        urlSearchParams.append(key, serializeValue(cur));
      });
    } else {
      urlSearchParams.set(key, serializeValue(value));
    }
  }
  return '?' + urlSearchParams.toString();
}
function compileLocalizedPathname(_ref) {
  let {
    pathname,
    locale,
    params,
    pathnames,
    query
  } = _ref;
  function getNamedPath(value) {
    let namedPath = pathnames[value];
    if (!namedPath) {
      // Unknown pathnames
      namedPath = value;
    }
    return namedPath;
  }
  function compilePath(namedPath) {
    const template = typeof namedPath === 'string' ? namedPath : namedPath[locale];
    let compiled = template;
    if (params) {
      Object.entries(params).forEach(_ref2 => {
        let [key, value] = _ref2;
        let regexp, replacer;
        if (Array.isArray(value)) {
          regexp = "(\\[)?\\[...".concat(key, "\\](\\])?");
          replacer = value.map(v => String(v)).join('/');
        } else {
          regexp = "\\[".concat(key, "\\]");
          replacer = String(value);
        }
        compiled = compiled.replace(new RegExp(regexp, 'g'), replacer);
      });
    }

    // Clean up optional catch-all segments that were not replaced
    compiled = compiled.replace(/\[\[\.\.\..+\]\]/g, '');
    compiled = utils.normalizeTrailingSlash(compiled);
    if (compiled.includes('[')) {
      // Next.js throws anyway, therefore better provide a more helpful error message
      throw new Error("Insufficient params provided for localized pathname.\nTemplate: ".concat(template, "\nParams: ").concat(JSON.stringify(params)));
    }
    if (query) {
      compiled += serializeSearchParams(query);
    }
    return compiled;
  }
  if (typeof pathname === 'string') {
    const namedPath = getNamedPath(pathname);
    const compiled = compilePath(namedPath);
    return compiled;
  } else {
    const {
      pathname: href,
      ...rest
    } = pathname;
    const namedPath = getNamedPath(href);
    const compiled = compilePath(namedPath);
    const result = {
      ...rest,
      pathname: compiled
    };
    return result;
  }
}
function getRoute(locale, pathname, pathnames) {
  const sortedPathnames = utils.getSortedPathnames(Object.keys(pathnames));
  const decoded = decodeURI(pathname);
  for (const internalPathname of sortedPathnames) {
    const localizedPathnamesOrPathname = pathnames[internalPathname];
    if (typeof localizedPathnamesOrPathname === 'string') {
      const localizedPathname = localizedPathnamesOrPathname;
      if (utils.matchesPathname(localizedPathname, decoded)) {
        return internalPathname;
      }
    } else {
      if (utils.matchesPathname(localizedPathnamesOrPathname[locale], decoded)) {
        return internalPathname;
      }
    }
  }
  return pathname;
}
function getBasePath(pathname) {
  let windowPathname = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : window.location.pathname;
  if (pathname === '/') {
    return windowPathname;
  } else {
    return windowPathname.replace(pathname, '');
  }
}
function applyPathnamePrefix(pathname, locale, routing, domain, force) {
  const {
    mode
  } = routing.localePrefix;
  let shouldPrefix;
  if (force !== undefined) {
    shouldPrefix = force;
  } else if (utils.isLocalizableHref(pathname)) {
    if (mode === 'always') {
      shouldPrefix = true;
    } else if (mode === 'as-needed') {
      let defaultLocale = routing.defaultLocale;
      if (routing.domains) {
        const domainConfig = routing.domains.find(cur => cur.domain === domain);
        if (domainConfig) {
          defaultLocale = domainConfig.defaultLocale;
        } else {
          if (!domain) {
            console.error("You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\n\nSee: https://next-intl.dev/docs/routing#domains-localeprefix-asneeded");
          }
        }
      }
      shouldPrefix = defaultLocale !== locale;
    }
  }
  return shouldPrefix ? utils.prefixPathname(utils.getLocalePrefix(locale, routing.localePrefix), pathname) : pathname;
}
function validateReceivedConfig(config) {
  var _config$localePrefix;
  if (((_config$localePrefix = config.localePrefix) === null || _config$localePrefix === void 0 ? void 0 : _config$localePrefix.mode) === 'as-needed' && !('defaultLocale' in config)) {
    throw new Error("`localePrefix: 'as-needed' requires a `defaultLocale`.");
  }
}

exports.applyPathnamePrefix = applyPathnamePrefix;
exports.compileLocalizedPathname = compileLocalizedPathname;
exports.getBasePath = getBasePath;
exports.getRoute = getRoute;
exports.normalizeNameOrNameWithParams = normalizeNameOrNameWithParams;
exports.serializeSearchParams = serializeSearchParams;
exports.validateReceivedConfig = validateReceivedConfig;
