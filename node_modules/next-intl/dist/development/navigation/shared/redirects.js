'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var navigation = require('next/navigation');
var utils = require('../../shared/utils.js');

function createRedirectFn(redirectFn) {
  return function baseRedirect(params) {
    const prefix = utils.getLocalePrefix(params.locale, params.localePrefix);

    // This logic is considered legacy and is replaced by `applyPathnamePrefix`.
    // We keep it this way for now for backwards compatibility.
    const localizedPathname = params.localePrefix.mode === 'never' || !utils.isLocalizableHref(params.pathname) ? params.pathname : utils.prefixPathname(prefix, params.pathname);
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    return redirectFn(localizedPathname, ...args);
  };
}
const baseRedirect = createRedirectFn(navigation.redirect);
const basePermanentRedirect = createRedirectFn(navigation.permanentRedirect);

exports.basePermanentRedirect = basePermanentRedirect;
exports.baseRedirect = baseRedirect;
