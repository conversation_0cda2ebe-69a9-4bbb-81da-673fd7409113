'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var useLocale = require('../../react-client/useLocale.js');
var redirects = require('../shared/redirects.js');

function createRedirectFn(redirectFn) {
  return function clientRedirect(params) {
    let locale;
    try {
      // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render
      locale = useLocale.default();
    } catch (e) {
      {
        throw new Error('`redirect()` and `permanentRedirect()` can only be called during render. To redirect in an event handler or similar, you can use `useRouter()` instead.');
      }
    }
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    return redirectFn({
      ...params,
      locale
    }, ...args);
  };
}
const clientRedirect = createRedirectFn(redirects.baseRedirect);
const clientPermanentRedirect = createRedirectFn(redirects.basePermanentRedirect);

exports.clientPermanentRedirect = clientPermanentRedirect;
exports.clientRedirect = clientRedirect;
