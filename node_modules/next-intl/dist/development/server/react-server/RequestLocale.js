'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var headers = require('next/headers');
var React = require('react');
var constants = require('../../shared/constants.js');
var utils = require('../../shared/utils.js');
var RequestLocaleCache = require('./RequestLocaleCache.js');

async function getHeadersImpl() {
  const promiseOrValue = headers.headers();

  // Compatibility with Next.js <15
  return utils.isPromise(promiseOrValue) ? await promiseOrValue : promiseOrValue;
}
const getHeaders = React.cache(getHeadersImpl);
async function getLocaleFromHeaderImpl() {
  let locale;
  try {
    locale = (await getHeaders()).get(constants.HEADER_LOCALE_NAME) || undefined;
  } catch (error) {
    if (error instanceof Error && error.digest === 'DYNAMIC_SERVER_USAGE') {
      const wrappedError = new Error('Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering', {
        cause: error
      });
      wrappedError.digest = error.digest;
      throw wrappedError;
    } else {
      throw error;
    }
  }
  return locale;
}
const getLocaleFromHeader = React.cache(getLocaleFromHeaderImpl);
async function getRequestLocale() {
  return RequestLocaleCache.getCachedRequestLocale() || (await getLocaleFromHeader());
}

exports.getRequestLocale = getRequestLocale;
