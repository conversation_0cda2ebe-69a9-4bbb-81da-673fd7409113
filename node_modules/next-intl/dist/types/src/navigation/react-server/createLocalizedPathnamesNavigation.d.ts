import React from 'react';
import { RoutingConfigLocalizedNavigation } from '../../routing/config';
import { DomainsConfig, LocalePrefixMode, Locales, Pathnames } from '../../routing/types';
import { HrefOrHrefWithParams } from '../shared/utils';
export default function createLocalizedPathnamesNavigation<AppLocales extends Locales, AppLocalePrefixMode extends LocalePrefixMode = 'always', AppPathnames extends Pathnames<AppLocales> = never, AppDomains extends DomainsConfig<AppLocales> = never>(routing: RoutingConfigLocalizedNavigation<AppLocales, AppLocalePrefixMode, AppPathnames, AppDomains>): {
    Link: <Pathname extends keyof AppPathnames>({ href, locale, ...rest }: Omit<Omit<Omit<Omit<Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof {
        href: string | import("url").UrlObject;
        as?: string | import("url").UrlObject;
        replace?: boolean;
        scroll?: boolean;
        shallow?: boolean;
        passHref?: boolean;
        prefetch?: boolean | null;
        locale?: string | false;
        legacyBehavior?: boolean;
        onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>;
        onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>;
        onClick?: React.MouseEventHandler<HTMLAnchorElement>;
    }> & {
        href: string | import("url").UrlObject;
        as?: string | import("url").UrlObject;
        replace?: boolean;
        scroll?: boolean;
        shallow?: boolean;
        passHref?: boolean;
        prefetch?: boolean | null;
        locale?: string | false;
        legacyBehavior?: boolean;
        onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>;
        onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>;
        onClick?: React.MouseEventHandler<HTMLAnchorElement>;
    } & {
        children?: React.ReactNode;
    } & React.RefAttributes<HTMLAnchorElement>, "locale"> & {
        locale: string;
        prefix: string;
        localePrefixMode: LocalePrefixMode;
        localeCookie: import("../../routing/config").InitializedLocaleCookieConfig;
    }, "ref"> & React.RefAttributes<HTMLAnchorElement>, "locale" | "prefix" | "localePrefixMode"> & {
        locale?: string | undefined;
        localePrefix: {
            mode: "never";
        } | {
            mode: "always";
            prefixes?: Partial<Record<string, string>> | undefined;
        } | {
            mode: "as-needed";
            prefixes?: Partial<Record<string, string>> | undefined;
        };
    }, "href" | "name" | "localePrefix" | "localeCookie"> & {
        href: Pathname extends `${string}[[...${string}` ? Pathname | ({
            pathname: Pathname;
            params?: import("../shared/StrictParams").default<Pathname> | undefined;
        } & Omit<import("url").UrlObject, "pathname">) : Pathname extends `${string}[${string}` ? {
            pathname: Pathname;
            params: import("../shared/StrictParams").default<Pathname>;
        } & Omit<import("url").UrlObject, "pathname"> : Pathname | ({
            pathname: Pathname;
        } & Omit<import("url").UrlObject, "pathname">);
        locale?: AppLocales[number];
    }) => React.JSX.Element;
    redirect: <Pathname extends keyof AppPathnames>(href: HrefOrHrefWithParams<Pathname>, type?: import("next/navigation").RedirectType | undefined) => never;
    permanentRedirect: <Pathname extends keyof AppPathnames>(href: HrefOrHrefWithParams<Pathname>, type?: import("next/navigation").RedirectType | undefined) => never;
    getPathname: ({ href, locale }: {
        locale: AppLocales[number];
        href: HrefOrHrefWithParams<keyof AppPathnames>;
    }) => string;
    usePathname: () => never;
    useRouter: () => never;
};
