import{notFound as e}from"next/navigation";import{cache as t}from"react";import{_createIntlFormatters as n,_createCache as o,initializeConfig as r}from"use-intl/core";import{isPromise as i}from"../../shared/utils.js";import{getRequestLocale as a}from"./RequestLocale.js";import{getRequestLocale as l}from"./RequestLocaleLegacy.js";import s from"next-intl/config";let c=!1,u=!1;const f=t((function(){return new Date}));const d=t((function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}));const m=t((async function(t,n){if("function"!=typeof t)throw new Error("Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n");const o={get locale(){return u||(console.warn("\nThe `locale` parameter in `getRequestConfig` is deprecated, please switch to `await requestLocale`. See https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n"),u=!0),n||l()},get requestLocale(){return n?Promise.resolve(n):a()}};let r=t(o);i(r)&&(r=await r);let s=r.locale;return s||(c||(console.error("\nA `locale` is expected to be returned from `getRequestConfig`, but none was returned. This will be an error in the next major version of next-intl.\n\nSee: https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n"),c=!0),s=await o.requestLocale,s||(console.error("\nUnable to find `next-intl` locale because the middleware didn't run on this request and no `locale` was returned in `getRequestConfig`. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\n"),e())),{...r,locale:s,now:r.now||f(),timeZone:r.timeZone||d()}})),p=t(n),g=t(o);const w=t((async function(e){const t=await m(s,e);return{...r(t),_formatters:p(g())}}));export{w as default};
