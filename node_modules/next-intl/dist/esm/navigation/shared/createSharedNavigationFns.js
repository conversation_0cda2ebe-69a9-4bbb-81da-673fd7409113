import{extends as e}from"../../_virtual/_rollupPluginBabelHelpers.js";import{redirect as o,permanentRedirect as a}from"next/navigation";import t,{forwardRef as n,use as r}from"react";import{receiveRoutingConfig as l}from"../../routing/config.js";import{isLocalizableHref as i,isPromise as m}from"../../shared/utils.js";import c from"./BaseLink.js";import{validateReceivedConfig as u,serializeSearchParams as f,compileLocalizedPathname as s,applyPathnamePrefix as p,normalizeNameOrNameWithParams as d}from"./utils.js";function h(h,y){const j=l(y||{});u(j);const g=j.pathnames,v="as-needed"===j.localePrefix.mode&&j.domains||void 0;function q(o,a){let n,l,u,{href:f,locale:s,...p}=o;"object"==typeof f?(n=f.pathname,u=f.query,l=f.params):n=f;const d=i(f),y=h(),q=m(y)?r(y):y,x=d?L({locale:s||q,href:null==g?n:{pathname:n,params:l}},null!=s||v||void 0):n;return t.createElement(c,e({ref:a,defaultLocale:j.defaultLocale,href:"object"==typeof f?{...f,pathname:x}:x,locale:s,localeCookie:j.localeCookie,unprefixed:v&&d?{domains:j.domains.reduce(((e,o)=>(e[o.domain]=o.defaultLocale,e)),{}),pathname:L({locale:q,href:null==g?{pathname:n,query:u}:{pathname:n,query:u,params:l}},!1)}:void 0},p))}const x=n(q);function L(e,o){const{href:a,locale:t}=e;let n;return null==g?"object"==typeof a?(n=a.pathname,a.query&&(n+=f(a.query))):n=a:n=s({locale:t,...d(a),pathnames:j.pathnames}),p(n,t,j,e.domain,o)}function b(e){return function(o){for(var a=arguments.length,t=new Array(a>1?a-1:0),n=1;n<a;n++)t[n-1]=arguments[n];return e(L(o,o.domain?void 0:v),...t)}}const k=b(o),P=b(a);return{config:j,Link:x,redirect:k,permanentRedirect:P,getPathname:L}}export{h as default};
