{"version": 3, "file": "no-misused-spread.js", "sourceRoot": "", "sources": ["../../src/rules/no-misused-spread.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,sDAAwC;AACxC,+CAAiC;AAIjC,kCASiB;AAkBjB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,4EAA4E;YAC9E,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,qBAAqB,EACnB,sFAAsF;YACxF,gCAAgC,EAC9B,gIAAgI;YAClI,6BAA6B,EAC3B,+EAA+E;YACjF,wBAAwB,EACtB,2IAA2I;YAC7I,wBAAwB,EACtB,sFAAsF;YACxF,mBAAmB,EACjB,wIAAwI;YAC1I,uBAAuB,EACrB,uHAAuH;YACzH,cAAc,EACZ,mGAAmG;SACtG;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL,GAAG,gCAAyB,CAAC,UAAU,CAAC,KAAK;wBAC7C,WAAW,EACT,kEAAkE;qBACrE;iBACF;aACF;SACF;KACF;IAED,cAAc,EAAE;QACd;YACE,KAAK,EAAE,EAAE;SACV;KACF;IAED,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,SAAS,sBAAsB,CAAC,IAA4B;YAC1D,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEnE,IACE,CAAC,IAAA,+BAAwB,EAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC;gBAChE,QAAQ,CAAC,IAAI,CAAC,EACd,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,gBAAgB;iBAC5B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,SAAS,iBAAiB,CACxB,IAA0D;YAE1D,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEnE,IAAI,IAAA,+BAAwB,EAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpE,OAAO;YACT,CAAC;YAED,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC;gBACtC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,yBAAyB;iBACrC,CAAC,CAAC;gBAEH,OAAO;YACT,CAAC;YAED,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,0BAA0B;iBACtC,CAAC,CAAC;gBAEH,OAAO;YACT,CAAC;YAED,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC;gBAClC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,qBAAqB;iBACjC,CAAC,CAAC;gBAEH,OAAO;YACT,CAAC;YAED,IAAI,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC;gBAC3B,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,uBAAuB;iBACnC,CAAC,CAAC;gBAEH,OAAO;YACT,CAAC;YAED,IACE,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;gBACzB,sEAAsE;gBACtE,CAAC,QAAQ,CAAC,IAAI,CAAC,EACf,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,0BAA0B;iBACtC,CAAC,CAAC;gBAEH,OAAO;YACT,CAAC;YAED,IAAI,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC;gBACnC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,+BAA+B;iBAC3C,CAAC,CAAC;gBAEH,OAAO;YACT,CAAC;YAED,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7B,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,kCAAkC;iBAC9C,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,iCAAiC,EAAE,sBAAsB;YACzD,gCAAgC,EAAE,sBAAsB;YACxD,kBAAkB,EAAE,iBAAiB;YACrC,kCAAkC,EAAE,iBAAiB;SACtD,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,UAAU,CAAC,IAAa,EAAE,OAAuB;IACxD,OAAO,OAAO;SACX,SAAS,CAAC,IAAI,CAAC;SACf,IAAI,CACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CACxE,CAAC;AACN,CAAC;AAED,SAAS,OAAO,CAAC,OAAuB,EAAE,IAAa;IACrD,OAAO,cAAc,CACnB,IAAI,EACJ,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CACtD,CAAC;AACJ,CAAC;AAED,SAAS,QAAQ,CAAC,IAAa;IAC7B,OAAO,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,IAAA,oBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9E,CAAC;AAED,SAAS,sBAAsB,CAAC,IAAa;IAC3C,OAAO,cAAc,CACnB,IAAI,EACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,CAAC,MAAM,KAAK,CAAC,CACxE,CAAC;AACJ,CAAC;AAED,SAAS,SAAS,CAAC,OAAmB,EAAE,IAAa;IACnD,OAAO,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,IAAA,oBAAa,EAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC;AAED,SAAS,eAAe,CAAC,OAAuB,EAAE,IAAa;IAC7D,OAAO,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;QAC9B,oEAAoE;QACpE,IAAI,CAAC,CAAC,sBAAsB,EAAE,CAAC,MAAM,EAAE,CAAC;YACtC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,MAAM,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC;QAE7B,0EAA0E;QAC1E,OAAO,CAAC,CAAC,MAAM;YACb,EAAE,eAAe,EAAE;YACnB,EAAE,IAAI,CACJ,WAAW,CAAC,EAAE,CACZ,OAAO;aACJ,yBAAyB,CAAC,MAAM,EAAE,WAAW,CAAC;aAC9C,sBAAsB,EAAE,CAAC,MAAM,CACrC,CAAC;IACN,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAa;IACvC,OAAO,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;QAC9B,IACE,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;YACvB,OAAO,CAAC,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,2BAA2B,CAAC,EACtE,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,gBAAgB,EAAE,IAAI,CAAC;QAEnD,OAAO,CACL,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB;YACvC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,CACvC,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,KAAK,CAAC,OAAmB,EAAE,IAAa;IAC/C,OAAO,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAC9B,IAAA,0BAAmB,EAAC,OAAO,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC,CACnE,CAAC;AACJ,CAAC;AAED,SAAS,cAAc,CACrB,IAAa,EACb,SAAkC;IAElC,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;AACzB,CAAC"}