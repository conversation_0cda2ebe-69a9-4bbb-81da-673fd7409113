{"version": 3, "file": "no-for-in-array.js", "sourceRoot": "", "sources": ["../../src/rules/no-for-in-array.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sDAAwC;AACxC,+CAAiC;AAEjC,kCAIiB;AACjB,2EAAwE;AAExE,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,qDAAqD;YAClE,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,cAAc,EACZ,iNAAiN;SACpN;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,cAAc,CAAC,IAAI;gBACjB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;gBAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAElD,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEhE,IAAI,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC;oBAC/B,OAAO,CAAC,MAAM,CAAC;wBACb,GAAG,EAAE,IAAA,+CAAsB,EAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;wBACrD,SAAS,EAAE,gBAAgB;qBAC5B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,WAAW,CAAC,OAAuB,EAAE,IAAa;IACzD,OAAO,cAAc,CACnB,IAAI,EACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,kBAAkB,EAAE,IAAI,IAAI,IAAI,iBAAiB,CAAC,OAAO,EAAE,CAAC,CAAC,CACrE,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,OAAuB,EAAE,IAAa;IAC/D,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAElD,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;QAC3B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,OAAO,CAAC,aAAa,CAC1B,OAAO,CAAC,eAAe,CAAC,cAAc,CAAC,EACvC,EAAE,CAAC,SAAS,CAAC,UAAU,CACxB,CAAC;AACJ,CAAC;AAED,SAAS,cAAc,CACrB,IAAa,EACb,SAAkC;IAElC,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;AACzB,CAAC"}