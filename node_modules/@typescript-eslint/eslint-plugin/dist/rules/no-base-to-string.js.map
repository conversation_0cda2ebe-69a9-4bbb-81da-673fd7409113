{"version": 3, "file": "no-base-to-string.js", "sourceRoot": "", "sources": ["../../src/rules/no-base-to-string.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAEjC,kCAMiB;AAEjB,IAAK,UAIJ;AAJD,WAAK,UAAU;IACb,+BAAiB,CAAA;IACjB,4BAAc,CAAA;IACd,+BAAiB,CAAA;AACnB,CAAC,EAJI,UAAU,KAAV,UAAU,QAId;AASD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,8HAA8H;YAChI,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,aAAa,EACX,6HAA6H;YAC/H,YAAY,EACV,4GAA4G;SAC/G;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,gBAAgB,EAAE;wBAChB,IAAI,EAAE,OAAO;wBACb,WAAW,EACT,0DAA0D;wBAC5D,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,gBAAgB,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,iBAAiB,CAAC;SAChE;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,IAAI,EAAE,CAAC;QAEvD,SAAS,eAAe,CAAC,IAAyB,EAAE,IAAc;YAChE,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;gBACzC,OAAO;YACT,CAAC;YACD,MAAM,SAAS,GAAG,wBAAwB,CACxC,IAAI,IAAI,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,EACxC,IAAI,GAAG,EAAE,CACV,CAAC;YACF,IAAI,SAAS,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC;gBACpC,OAAO;YACT,CAAC;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,SAAS,EAAE,cAAc;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;oBACtC,SAAS;iBACV;aACF,CAAC,CAAC;QACL,CAAC;QAED,SAAS,2BAA2B,CAClC,IAAmB,EACnB,IAAa;YAEb,MAAM,SAAS,GAAG,oBAAoB,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;YAExD,IAAI,SAAS,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC;gBACpC,OAAO;YACT,CAAC;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,SAAS,EAAE,eAAe;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;oBACtC,SAAS;iBACV;aACF,CAAC,CAAC;QACL,CAAC;QAED,SAAS,yBAAyB,CAChC,IAAkB,EAClB,uBAAsD;YAEtD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,IAAI,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,KAAK,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnE,OAAO,UAAU,CAAC,KAAK,CAAC;YAC1B,CAAC;YAED,IAAI,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,KAAK,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpE,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,OAAO,UAAU,CAAC,SAAS,CAAC;QAC9B,CAAC;QAED,SAAS,gCAAgC,CACvC,IAAyB,EACzB,uBAAsD;YAEtD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACjC,MAAM,iBAAiB,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;gBAE3D,IAAI,iBAAiB,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC;oBAC5C,OAAO,UAAU,CAAC,MAAM,CAAC;gBAC3B,CAAC;YACH,CAAC;YAED,OAAO,UAAU,CAAC,KAAK,CAAC;QAC1B,CAAC;QAED,SAAS,qBAAqB,CAC5B,IAAsB,EACtB,OAAqB;YAErB,MAAM,QAAQ,GAAG,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CACnC,wBAAwB,CAAC,CAAC,EAAE,OAAO,CAAC,CACrC,CAAC;YACF,IAAI,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,KAAK,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClE,OAAO,UAAU,CAAC,KAAK,CAAC;YAC1B,CAAC;YAED,IAAI,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,KAAK,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtE,OAAO,UAAU,CAAC,SAAS,CAAC;YAC9B,CAAC;YAED,OAAO,UAAU,CAAC,MAAM,CAAC;QAC3B,CAAC;QAED,SAAS,qBAAqB,CAC5B,IAAa,EACb,OAAqB;YAErB,MAAM,QAAQ,GAAG,IAAA,iBAAU,EACzB,IAAI,CAAC,kBAAkB,EAAE,EACzB,qCAAqC,CACtC,CAAC;YACF,OAAO,wBAAwB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC;QAED,SAAS,oBAAoB,CAC3B,IAAa,EACb,OAAqB;YAErB,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,OAAO,yBAAyB,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CACzC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CACjC,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,OAAO,gCAAgC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAChD,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CACjC,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,OAAO,qBAAqB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC9C,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,OAAO,qBAAqB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC9C,CAAC;YAED,OAAO,UAAU,CAAC,MAAM,CAAC;QAC3B,CAAC;QAED,SAAS,wBAAwB,CAC/B,IAAa,EACb,OAAqB;YAErB,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtB,iEAAiE;gBACjE,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,IAAI,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxC,IAAI,UAAU,EAAE,CAAC;oBACf,OAAO,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;gBACvD,CAAC;gBACD,wCAAwC;gBACxC,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,iDAAiD;YACjD,IACE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO;gBACjC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,cAAc,EACxC,CAAC;gBACD,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,IAAI,gBAAgB,CAAC,QAAQ,CAAC,IAAA,kBAAW,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;gBAC1D,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC1B,OAAO,gCAAgC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAChD,wBAAwB,CAAC,CAAC,EAAE,OAAO,CAAC,CACrC,CAAC;YACJ,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;gBACnB,OAAO,yBAAyB,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CACzC,wBAAwB,CAAC,CAAC,EAAE,OAAO,CAAC,CACrC,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,OAAO,qBAAqB,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,GAAG,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YAClE,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,OAAO,qBAAqB,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,GAAG,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YAClE,CAAC;YAED,MAAM,QAAQ,GACZ,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,CAAC;gBAC3C,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;YACpD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,mBAAmB;gBACnB,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,MAAM,YAAY,GAAG,QAAQ,CAAC,eAAe,EAAE,CAAC;YAEhD,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtD,uEAAuE;gBACvE,+BAA+B;gBAC/B,EAAE;gBACF,gDAAgD;gBAChD,yEAAyE;gBACzE,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,cAAc,GAClB,EAAE,CAAC,sBAAsB,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC7C,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC;YAC5C,OAAO,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;QAC/D,CAAC;QAED,SAAS,mBAAmB,CAAC,IAA6B;YACxD,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC9C,6EAA6E;gBAC7E,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ;gBAC7B,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EACjB,CAAC;gBACD,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAChD,6EAA6E;gBAC7E,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACzC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC;YAChC,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;YACL,yEAAyE,CACvE,IAA+D;gBAE/D,MAAM,QAAQ,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvD,MAAM,SAAS,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEzD,IAAI,IAAA,kBAAW,EAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,QAAQ,EAAE,CAAC;oBAChD,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;gBACzC,CAAC;qBAAM,IACL,IAAA,kBAAW,EAAC,OAAO,EAAE,SAAS,CAAC,KAAK,QAAQ;oBAC5C,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EACnD,CAAC;oBACD,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;YACD,cAAc,CAAC,IAA6B;gBAC1C,IACE,mBAAmB,CAAC,IAAI,CAAC;oBACzB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EACvD,CAAC;oBACD,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC;YACD,+EAA+E,CAC7E,IAAyB;gBAEzB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAmC,CAAC;gBAC5D,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;gBACvE,2BAA2B,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACvD,CAAC;YACD,sGAAsG,CACpG,IAAyB;gBAEzB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAmC,CAAC;gBAC5D,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACrC,CAAC;YAED,eAAe,CAAC,IAA8B;gBAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB,EAAE,CAAC;oBACjE,OAAO;gBACT,CAAC;gBACD,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBAC1C,eAAe,CAAC,UAAU,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}