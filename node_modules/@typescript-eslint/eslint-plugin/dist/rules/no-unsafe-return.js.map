{"version": 3, "file": "no-unsafe-return.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-return.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,sDAAwC;AACxC,+CAAiC;AAEjC,kCAaiB;AACjB,yEAAsE;AAEtE,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,4DAA4D;YACzE,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,YAAY,EAAE,4CAA4C;YAC1D,sBAAsB,EACpB,mFAAmF;YACrF,gBAAgB,EAAE;gBAChB,wEAAwE;gBACxE,2HAA2H;aAC5H,CAAC,IAAI,CAAC,IAAI,CAAC;SACb;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC9D,MAAM,gBAAgB,GAAG,OAAO,CAAC,6BAA6B,CAC5D,eAAe,EACf,gBAAgB,CACjB,CAAC;QAEF,SAAS,WAAW,CAClB,UAAyB,EACzB,gBAA+B,UAAU;YAEzC,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAE/C,MAAM,OAAO,GAAG,IAAA,0BAAmB,EACjC,IAAI,EACJ,OAAO,EACP,QAAQ,CAAC,OAAO,EAChB,MAAM,CACP,CAAC;YACF,MAAM,YAAY,GAAG,IAAA,6CAAqB,EAAC,UAAU,CAAC,CAAC;YACvD,wBAAwB,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC3C,OAAO;YACT,CAAC;YAED,qEAAqE;YACrE,MAAM,cAAc,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC1E,MAAM,cAAc,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAExE,yFAAyF;YACzF,+DAA+D;YAC/D,wDAAwD;YACxD,qGAAqG;YACrG,IAAI,YAAY,GACd,EAAE,CAAC,oBAAoB,CAAC,cAAc,CAAC;gBACvC,EAAE,CAAC,eAAe,CAAC,cAAc,CAAC;gBAChC,CAAC,CAAC,IAAA,wBAAiB,EAAC,OAAO,EAAE,cAAc,CAAC;gBAC5C,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC/C,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,YAAY,GAAG,QAAQ,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC1D,CAAC;YACD,MAAM,cAAc,GAAG,OAAO,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;YACrE,6EAA6E;YAC7E,iFAAiF;YACjF,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;gBACxB,KAAK,MAAM,SAAS,IAAI,cAAc,EAAE,CAAC;oBACvC,MAAM,mBAAmB,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;oBAEtD,IACE,cAAc,KAAK,mBAAmB;wBACtC,IAAA,oBAAa,EACX,mBAAmB,EACnB,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CACxC,EACD,CAAC;wBACD,OAAO;oBACT,CAAC;oBACD,IAAI,YAAY,CAAC,KAAK,EAAE,CAAC;wBACvB,MAAM,0BAA0B,GAC9B,OAAO,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;wBAE9C,MAAM,qBAAqB,GACzB,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;wBACzC,IACE,qBAAqB,KAAK,0BAA0B;4BACpD,CAAC,0BAA0B;gCACzB,IAAA,oBAAa,EACX,0BAA0B,EAC1B,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CACxC,CAAC,EACJ,CAAC;4BACD,OAAO;wBACT,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,OAAO,KAAK,cAAO,CAAC,IAAI,EAAE,CAAC;gBAC7B,2FAA2F;gBAC3F,8CAA8C;gBAC9C,KAAK,MAAM,SAAS,IAAI,cAAc,EAAE,CAAC;oBACvC,MAAM,kBAAkB,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;oBACrD,IACE,OAAO,KAAK,cAAO,CAAC,GAAG;wBACvB,IAAA,wBAAiB,EAAC,kBAAkB,CAAC,EACrC,CAAC;wBACD,OAAO;oBACT,CAAC;oBACD,IACE,OAAO,KAAK,cAAO,CAAC,QAAQ;wBAC5B,IAAA,6BAAsB,EAAC,kBAAkB,EAAE,OAAO,CAAC,EACnD,CAAC;wBACD,OAAO;oBACT,CAAC;oBACD,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;oBAC/D,IACE,WAAW;wBACX,OAAO,KAAK,cAAO,CAAC,UAAU;wBAC9B,IAAA,wBAAiB,EAAC,WAAW,CAAC,EAC9B,CAAC;wBACD,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,IAAI,OAAO,KAAK,cAAO,CAAC,UAAU,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;oBAC1D,OAAO;gBACT,CAAC;gBAED,IAAI,SAAS,GAAwC,cAAc,CAAC;gBACpE,MAAM,WAAW,GAAG,OAAO,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;gBAEjE,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACtB,gBAAgB;oBAChB,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,UAAU,CAAC,CAAC;oBACrD,IACE,cAAc;wBACd,IAAA,oBAAa,EACX,IAAA,mCAA4B,EAAC,QAAQ,EAAE,cAAc,CAAC,CACvD,EACD,CAAC;wBACD,SAAS,GAAG,kBAAkB,CAAC;oBACjC,CAAC;gBACH,CAAC;gBAED,qFAAqF;gBACrF,OAAO,OAAO,CAAC,MAAM,CAAC;oBACpB,IAAI,EAAE,aAAa;oBACnB,SAAS;oBACT,IAAI,EAAE;wBACJ,IAAI,EAAE,WAAW;4BACf,CAAC,CAAC,OAAO;4BACT,CAAC,CAAC,OAAO,KAAK,cAAO,CAAC,GAAG;gCACvB,CAAC,CAAC,OAAO;gCACT,CAAC,CAAC,OAAO,KAAK,cAAO,CAAC,UAAU;oCAC9B,CAAC,CAAC,gBAAgB;oCAClB,CAAC,CAAC,SAAS;qBAClB;iBACF,CAAC,CAAC;YACL,CAAC;YAED,MAAM,SAAS,GAAG,YAAY,CAAC,iBAAiB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACzD,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,kBAAkB,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;gBACrD,MAAM,MAAM,GAAG,IAAA,yBAAkB,EAC/B,cAAc,EACd,kBAAkB,EAClB,OAAO,EACP,UAAU,CACX,CAAC;gBACF,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,OAAO;gBACT,CAAC;gBAED,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;gBACpC,OAAO,OAAO,CAAC,MAAM,CAAC;oBACpB,IAAI,EAAE,aAAa;oBACnB,SAAS,EAAE,wBAAwB;oBACnC,IAAI,EAAE;wBACJ,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC;wBACxC,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;qBACrC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,qDAAqD,EAAE,WAAW;YAClE,eAAe,CAAC,IAAI;gBAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO;gBACT,CAAC;gBAED,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC9B,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}