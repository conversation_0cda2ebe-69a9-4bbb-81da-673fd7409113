{"version": 3, "file": "no-unsafe-type-assertion.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-type-assertion.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,sDAAwC;AACxC,+CAAiC;AAEjC,kCAMiB;AAEjB,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,0BAA0B;IAChC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,6CAA6C;YAC1D,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,wBAAwB,EACtB,2FAA2F;YAC7F,wBAAwB,EACtB,8FAA8F;YAChG,kCAAkC,EAChC,+HAA+H;YACjI,mBAAmB,EACjB,+EAA+E;YACjF,yCAAyC,EACvC,+KAA+K;SAClL;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,SAAS,cAAc,CAAC,IAAa;YACnC,OAAO,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC;QACtE,CAAC;QAED,SAAS,mBAAmB,CAAC,IAAa;YACxC,OAAO,CACL,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;gBAC1B,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC,CAC5D,CAAC;QACJ,CAAC;QAED,SAAS,eAAe,CACtB,IAAwD;YAExD,MAAM,cAAc,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACnE,MAAM,YAAY,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAErE,IAAI,cAAc,KAAK,YAAY,EAAE,CAAC;gBACpC,OAAO;YACT,CAAC;YAED,+CAA+C;YAC/C,IAAI,IAAA,oBAAa,EAAC,YAAY,CAAC,IAAI,IAAA,wBAAiB,EAAC,cAAc,CAAC,EAAE,CAAC;gBACrE,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,0BAA0B;oBACrC,IAAI,EAAE;wBACJ,IAAI,EAAE,OAAO;qBACd;iBACF,CAAC,CAAC;gBAEH,OAAO;YACT,CAAC;YAED,MAAM,mBAAmB,GAAG,IAAA,yBAAkB,EAC5C,cAAc,EACd,YAAY,EACZ,OAAO,EACP,IAAI,CAAC,UAAU,CAChB,CAAC;YAEF,IAAI,mBAAmB,EAAE,CAAC;gBACxB,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,0BAA0B;oBACrC,IAAI,EAAE;wBACJ,IAAI,EAAE,cAAc,CAAC,mBAAmB,CAAC,MAAM,CAAC;qBACjD;iBACF,CAAC,CAAC;gBAEH,OAAO;YACT,CAAC;YAED,MAAM,iBAAiB,GAAG,IAAA,yBAAkB,EAC1C,YAAY,EACZ,cAAc,EACd,OAAO,EACP,IAAI,CAAC,cAAc,CACpB,CAAC;YAEF,IAAI,iBAAiB,EAAE,CAAC;gBACtB,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,0BAA0B;oBACrC,IAAI,EAAE;wBACJ,IAAI,EAAE,cAAc,CAAC,iBAAiB,CAAC,MAAM,CAAC;qBAC/C;iBACF,CAAC,CAAC;gBAEH,OAAO;YACT,CAAC;YAED,8EAA8E;YAC9E,uCAAuC;YACvC,MAAM,qBAAqB,GAAG,mBAAmB,CAAC,cAAc,CAAC;gBAC/D,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC;gBACxC,CAAC,CAAC,cAAc,CAAC;YAEnB,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,CAChD,qBAAqB,EACrB,YAAY,CACb,CAAC;YACF,IAAI,eAAe,EAAE,CAAC;gBACpB,OAAO;YACT,CAAC;YAED,wEAAwE;YACxE,IAAI,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC1C,MAAM,sBAAsB,GAC1B,OAAO,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;gBAChD,IAAI,CAAC,sBAAsB,EAAE,CAAC;oBAC5B,yDAAyD;oBACzD,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,oCAAoC;wBAC/C,IAAI,EAAE;4BACJ,IAAI,EAAE,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC;yBACzC;qBACF,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;gBAED,iEAAiE;gBACjE,0CAA0C;gBAC1C,MAAM,wBAAwB,GAAG,OAAO,CAAC,kBAAkB,CACzD,qBAAqB,EACrB,sBAAsB,CACvB,CAAC;gBACF,IAAI,wBAAwB,EAAE,CAAC;oBAC7B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,2CAA2C;wBACtD,IAAI,EAAE;4BACJ,IAAI,EAAE,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC;yBACzC;qBACF,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;YACH,CAAC;YAED,wBAAwB;YACxB,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,SAAS,EAAE,qBAAqB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC;iBACzC;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,iCAAiC,CAC/B,IAAwD;gBAExD,eAAe,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}