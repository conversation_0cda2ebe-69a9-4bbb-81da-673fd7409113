{"version": 3, "file": "prefer-nullish-coalescing.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-nullish-coalescing.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,oDAA2E;AAC3E,sDAAwC;AACxC,+CAAiC;AAEjC,kCAaiB;AAEjB,MAAM,8BAA8B,GAAG,IAAA,oBAAa,EAAC;IACnD,sBAAc,CAAC,UAAU;IACzB,sBAAc,CAAC,gBAAgB;CACvB,CAAC,CAAC;AA0BZ,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,2BAA2B;IACjC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,0FAA0F;YAC5F,WAAW,EAAE,WAAW;YACxB,oBAAoB,EAAE,IAAI;SAC3B;QACD,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,iBAAiB,EACf,kGAAkG;YACpG,mBAAmB,EACjB,mJAAmJ;YACrJ,wBAAwB,EACtB,wHAAwH;YAC1H,cAAc,EAAE,wDAAwD;SACzE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,sDAAsD,EAAE;wBACtD,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,2KAA2K;qBAC9K;oBACD,qBAAqB,EAAE;wBACrB,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,0DAA0D;qBAC7D;oBACD,sBAAsB,EAAE;wBACtB,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,qEAAqE;qBACxE;oBACD,6BAA6B,EAAE;wBAC7B,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,uGAAuG;qBAC1G;oBACD,gBAAgB,EAAE;wBAChB,WAAW,EACT,qFAAqF;wBACvF,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,QAAQ;gCACd,WAAW,EAAE,wCAAwC;gCACrD,UAAU,EAAE;oCACV,MAAM,EAAE;wCACN,IAAI,EAAE,SAAS;wCACf,WAAW,EAAE,gCAAgC;qCAC9C;oCACD,OAAO,EAAE;wCACP,IAAI,EAAE,SAAS;wCACf,WAAW,EAAE,iCAAiC;qCAC/C;oCACD,MAAM,EAAE;wCACN,IAAI,EAAE,SAAS;wCACf,WAAW,EAAE,gCAAgC;qCAC9C;oCACD,MAAM,EAAE;wCACN,IAAI,EAAE,SAAS;wCACf,WAAW,EAAE,gCAAgC;qCAC9C;iCACF;6BACF;4BACD;gCACE,IAAI,EAAE,SAAS;gCACf,WAAW,EAAE,6BAA6B;gCAC1C,IAAI,EAAE,CAAC,IAAI,CAAC;6BACb;yBACF;qBACF;oBACD,kBAAkB,EAAE;wBAClB,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,8GAA8G;qBACjH;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,sDAAsD,EAAE,KAAK;YAC7D,qBAAqB,EAAE,KAAK;YAC5B,sBAAsB,EAAE,IAAI;YAC5B,6BAA6B,EAAE,KAAK;YACpC,gBAAgB,EAAE;gBAChB,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,KAAK;gBACb,MAAM,EAAE,KAAK;aACd;YACD,kBAAkB,EAAE,KAAK;SAC1B;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,sDAAsD,EACtD,qBAAqB,EACrB,sBAAsB,EACtB,6BAA6B,EAC7B,gBAAgB,EAChB,kBAAkB,GACnB,EACF;QAED,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAClD,MAAM,eAAe,GAAG,cAAc,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAEpE,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,MAAM,kBAAkB,GAAG,OAAO,CAAC,6BAA6B,CAC9D,eAAe,EACf,kBAAkB,CACnB,CAAC;QAEF,IACE,CAAC,kBAAkB;YACnB,sDAAsD,KAAK,IAAI,EAC/D,CAAC;YACD,OAAO,CAAC,MAAM,CAAC;gBACb,GAAG,EAAE;oBACH,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;oBAC7B,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;iBAC5B;gBACD,SAAS,EAAE,mBAAmB;aAC/B,CAAC,CAAC;QACL,CAAC;QAED;;;WAGG;QACH,SAAS,8BAA8B,CAAC,IAAa;YACnD,IAAI,CAAC,IAAA,wBAAiB,EAAC,IAAI,CAAC,EAAE,CAAC;gBAC7B,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,cAAc,GAAG;gBACrB,6DAA6D;gBAC7D,CAAC,gBAAgB,KAAK,IAAI,IAAI,gBAAiB,CAAC,MAAM,CAAC;oBACrD,EAAE,CAAC,SAAS,CAAC,UAAU;gBACzB,CAAC,gBAAgB,KAAK,IAAI,IAAI,gBAAiB,CAAC,OAAO,CAAC;oBACtD,EAAE,CAAC,SAAS,CAAC,WAAW;gBAC1B,CAAC,gBAAgB,KAAK,IAAI,IAAI,gBAAiB,CAAC,MAAM,CAAC;oBACrD,EAAE,CAAC,SAAS,CAAC,UAAU;gBACzB,CAAC,gBAAgB,KAAK,IAAI,IAAI,gBAAiB,CAAC,MAAM,CAAC;oBACrD,EAAE,CAAC,SAAS,CAAC,UAAU;gBACzB,4DAA4D;aAC7D;iBACE,MAAM,CAAC,CAAC,IAAI,EAAkB,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC;iBAC1D,MAAM,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;YAClD,IACE,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC,SAAS,CAAC,IAAI;gBAChC,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC,SAAS,CAAC,SAAS;gBACpC,IAAmC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAClD,OAAO;qBACJ,qBAAqB,CAAC,CAAC,CAAC;qBACxB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CACvD,EACD,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;;;;;;;;WAUG;QACH,SAAS,yCAAyC,CAAC,EACjD,IAAI,EACJ,QAAQ,GAOT;YACC,MAAM,QAAQ,GAAG,cAAc,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAC5D,IAAI,CAAC,8BAA8B,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC9C,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,sBAAsB,KAAK,IAAI,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IACE,qBAAqB,KAAK,IAAI;gBAC9B,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC,EAC1C,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,kCAAkC,CACzC,IAAgE,EAChE,WAAmB,EACnB,MAAc;YAEd,IACE,CAAC,yCAAyC,CAAC;gBACzC,IAAI;gBACJ,QAAQ,EAAE,IAAI,CAAC,IAAI;aACpB,CAAC,EACF,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IACE,6BAA6B,KAAK,IAAI;gBACtC,wBAAwB,CAAC,IAAI,CAAC,EAC9B,CAAC;gBACD,OAAO;YACT,CAAC;YAED,MAAM,cAAc,GAAG,IAAA,iBAAU,EAC/B,OAAO,CAAC,UAAU,CAAC,aAAa,CAC9B,IAAI,CAAC,IAAI,EACT,KAAK,CAAC,EAAE,CACN,KAAK,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;gBACzC,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,CAChC,EACD,wBAAiB,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CACtD,CAAC;YAEF,QAAQ,CAAC,CAAC,GAAG,CACX,KAAyB;gBAEzB,IAAI,IAAA,0BAAmB,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;oBACrC,kFAAkF;oBAClF,IACE,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;wBACnD,CAAC,IAAA,0BAAmB,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,CAAC;wBACD,MAAM,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;oBACrD,CAAC;yBAAM,CAAC;wBACN,MAAM,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;oBAC/C,CAAC;oBACD,MAAM,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBAC/C,CAAC;gBACD,MAAM,KAAK,CAAC,WAAW,CACrB,cAAc,EACd,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAClC,CAAC;YACJ,CAAC;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,cAAc;gBACpB,SAAS,EAAE,qBAAqB;gBAChC,IAAI,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;gBAC7B,OAAO,EAAE;oBACP;wBACE,SAAS,EAAE,gBAAgB;wBAC3B,IAAI,EAAE,EAAE,MAAM,EAAE;wBAChB,GAAG;qBACJ;iBACF;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,wCAAwC,CACtC,IAAmC;gBAEnC,kCAAkC,CAAC,IAAI,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;YAC9D,CAAC;YACD,qBAAqB,CAAC,IAAoC;gBACxD,IAAI,kBAAkB,EAAE,CAAC;oBACvB,OAAO;gBACT,CAAC;gBAED,IAAI,QAAuD,CAAC;gBAC5D,IAAI,yBAAyB,GAAoB,EAAE,CAAC;gBACpD,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE,CAAC;oBACvD,yBAAyB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC9D,IACE,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;wBAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;wBAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;wBAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK,EAC5B,CAAC;wBACD,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAChC,CAAC;gBACH,CAAC;qBAAM,IACL,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;oBACnD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACvD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EACxD,CAAC;oBACD,yBAAyB,GAAG;wBAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;wBACnB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;wBACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;wBACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;qBACtB,CAAC;oBACF,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC/C,IACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;4BACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,EAClC,CAAC;4BACD,QAAQ,GAAG,KAAK,CAAC;wBACnB,CAAC;6BAAM,IACL,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;4BACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC;4BACnC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;gCAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC;4BACvC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;gCAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,EACpC,CAAC;4BACD,QAAQ,GAAG,IAAI,CAAC;wBAClB,CAAC;oBACH,CAAC;yBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;wBACvC,IACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;4BACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,EAClC,CAAC;4BACD,QAAQ,GAAG,KAAK,CAAC;wBACnB,CAAC;6BAAM,IACL,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;4BACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC;4BACnC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;gCAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC;4BACvC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;gCAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,EACpC,CAAC;4BACD,QAAQ,GAAG,IAAI,CAAC;wBAClB,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,IAAI,4BAAuD,CAAC;gBAC5D,IAAI,kBAAkB,GAAG,KAAK,CAAC;gBAC/B,IAAI,kCAAkC,GAAG,KAAK,CAAC;gBAC/C,IAAI,uCAAuC,GAAG,KAAK,CAAC;gBAEpD,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,kBAAkB,GAAG,IAAI,CAAC;oBAE1B,IACE,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC;wBACzC,IAAA,kBAAW,EAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,EACvC,CAAC;wBACD,4BAA4B,GAAG,IAAI,CAAC,IAAI,CAAC;oBAC3C,CAAC;yBAAM,IACL,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;wBACjD,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,GAAG;wBAC1B,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;wBAClD,IAAA,kBAAW,EAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,EAC/C,CAAC;wBACD,4BAA4B,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;wBAClD,QAAQ,GAAG,GAAG,CAAC;oBACjB,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,0EAA0E;oBAC1E,KAAK,MAAM,QAAQ,IAAI,yBAAyB,EAAE,CAAC;wBACjD,IAAI,IAAA,oBAAa,EAAC,QAAQ,CAAC,EAAE,CAAC;4BAC5B,kCAAkC,GAAG,IAAI,CAAC;wBAC5C,CAAC;6BAAM,IAAI,IAAA,4BAAqB,EAAC,QAAQ,CAAC,EAAE,CAAC;4BAC3C,uCAAuC,GAAG,IAAI,CAAC;wBACjD,CAAC;6BAAM,IACL,CAAC,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,IAAI,CAAC;4BACzC,IAAA,kBAAW,EAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,EACtC,CAAC;4BACD,4BAA4B,GAAG,QAAQ,CAAC;wBAC1C,CAAC;6BAAM,IACL,CAAC,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,IAAI,CAAC;4BACzC,IAAA,kBAAW,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,EACrC,CAAC;4BACD,4BAA4B,GAAG,QAAQ,CAAC;wBAC1C,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,IAAI,CAAC,4BAA4B,EAAE,CAAC;oBAClC,OAAO;gBACT,CAAC;gBAED,MAAM,qCAAqC,GAAG,CAAC,GAAY,EAAE;oBAC3D,oCAAoC;oBACpC,IAAI,kBAAkB,EAAE,CAAC;wBACvB,OAAO,yCAAyC,CAAC;4BAC/C,IAAI;4BACJ,QAAQ,EAAE,4BAA4B;yBACvC,CAAC,CAAC;oBACL,CAAC;oBAED,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CACrD,4BAA4B,CAC7B,CAAC;oBACF,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;oBAC/C,MAAM,KAAK,GAAG,IAAA,mBAAY,EAAC,IAAI,CAAC,CAAC;oBAEjC,2EAA2E;oBAC3E,IACE,uCAAuC;wBACvC,kCAAkC,EAClC,CAAC;wBACD,OAAO,uCAAuC,CAAC;oBACjD,CAAC;oBAED,iEAAiE;oBACjE,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;wBAC3C,OAAO,IAAI,CAAC;oBACd,CAAC;oBAED,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;wBACtD,OAAO,KAAK,CAAC;oBACf,CAAC;oBAED,MAAM,WAAW,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAEtD,uEAAuE;oBACvE,IAAI,uCAAuC,IAAI,CAAC,WAAW,EAAE,CAAC;wBAC5D,OAAO,IAAI,CAAC;oBACd,CAAC;oBAED,MAAM,gBAAgB,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBAEhE,qEAAqE;oBACrE,OAAO,kCAAkC,IAAI,CAAC,gBAAgB,CAAC;gBACjE,CAAC,CAAC,EAAE,CAAC;gBAEL,IAAI,qCAAqC,EAAE,CAAC;oBAC1C,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,0BAA0B;wBACrC,iDAAiD;wBACjD,IAAI,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;wBACpB,OAAO,EAAE;4BACP;gCACE,SAAS,EAAE,gBAAgB;gCAC3B,IAAI,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;gCACpB,GAAG,CAAC,KAAyB;oCAC3B,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GACjB,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,GAAG;wCACzD,CAAC,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAAC,UAAU,CAAC;wCACjD,CAAC,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;oCACrD,OAAO,KAAK,CAAC,WAAW,CACtB,IAAI,EACJ,GAAG,IAAA,6BAAsB,EAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,IAAA,6BAAsB,EAC9E,OAAO,CAAC,UAAU,EAClB,KAAK,CACN,EAAE,CACJ,CAAC;gCACJ,CAAC;6BACF;yBACF;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YACD,oCAAoC,CAClC,IAAgC;gBAEhC,kCAAkC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACrD,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,iBAAiB,CAAC,IAAmB;IAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;QACrD,OAAO,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB;QACpD,CAAC,MAAM,CAAC,UAAU,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,EACzD,CAAC;QACD,OAAO,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;QACjD,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAClC,CAAC;QACD,OAAO,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;QAC9C,MAAM,CAAC,QAAQ,KAAK,GAAG,EACvB,CAAC;QACD,OAAO,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,IACE,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB;QACnD,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC/C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW;QAC1C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY;QAC3C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC;QAChD,MAAM,CAAC,IAAI,KAAK,IAAI,EACpB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,2BAA2B,CAClC,IAAmB,EACnB,OAA4D;IAE5D,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;QACrD,OAAO,2BAA2B,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB;QACpD,CAAC,MAAM,CAAC,UAAU,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,EACzD,CAAC;QACD,OAAO,2BAA2B,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;QACjD,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAClC,CAAC;QACD,OAAO,2BAA2B,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,OAAO,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,oBAAoB,CAC3B,IAAmB,EACnB,OAA4D;IAE5D,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;QAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;QAC9C,6EAA6E;QAC7E,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EACjB,CAAC;QACD,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAe,CAAC,OAAO,CAAC,CAAC;QACxD,OAAO,QAAQ,IAAI,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;IACxD,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,wBAAwB,CAC/B,IAAgE;IAEhE,MAAM,IAAI,GAAG,IAAI,GAAG,EAA6B,CAAC;IAClD,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACnD,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE,CAAC;QAC5B,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACtB,SAAS;QACX,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAElB,IAAI,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;YACtD,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7C,sEAAsE;gBACtE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}