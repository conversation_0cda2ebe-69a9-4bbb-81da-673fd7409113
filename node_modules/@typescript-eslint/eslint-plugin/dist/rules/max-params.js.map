{"version": 3, "file": "max-params.js", "sourceRoot": "", "sources": ["../../src/rules/max-params.ts"], "names": [], "mappings": ";;AAEA,oDAA0D;AAO1D,kCAAqC;AACrC,iEAA8D;AAW9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,YAAY,CAAC,CAAC;AAKjD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,YAAY;IAClB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,2DAA2D;QAC3D,IAAI,EAAE;YACJ,WAAW,EACT,gEAAgE;YAClE,eAAe,EAAE,IAAI;SACtB;QACD,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;QAChC,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,aAAa,EAAE;wBACb,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,gEAAgE;qBACnE;oBACD,GAAG,EAAE;wBACH,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,yDAAyD;wBAC3D,OAAO,EAAE,CAAC;qBACX;oBACD,OAAO,EAAE;wBACP,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,sEAAsE;wBACxE,OAAO,EAAE,CAAC;qBACX;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IAElD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC;QACjC,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAE3C,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;YAC3B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,mBAAmB,GAAG,CAAyB,IAAO,EAAK,EAAE;YACjE,IACE,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBACjD,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM;gBAC9B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,cAAc,CAAC,IAAI;oBAChD,sBAAc,CAAC,aAAa,EAC9B,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO;gBACL,GAAG,IAAI;gBACP,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aAC7B,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,YAAY,GAAG,CACnB,QAAiC,EACR,EAAE;YAC3B,OAAO,CAAC,IAAO,EAAQ,EAAE;gBACvB,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,OAAO;YACL,uBAAuB,EAAE,YAAY,CAAC,SAAS,CAAC,uBAAuB,CAAC;YACxE,mBAAmB,EAAE,YAAY,CAAC,SAAS,CAAC,mBAAmB,CAAC;YAChE,kBAAkB,EAAE,YAAY,CAAC,SAAS,CAAC,kBAAkB,CAAC;YAC9D,iBAAiB,EAAE,YAAY,CAAC,SAAS,CAAC,mBAAmB,CAAC;YAC9D,cAAc,EAAE,YAAY,CAAC,SAAS,CAAC,mBAAmB,CAAC;SAC5D,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}