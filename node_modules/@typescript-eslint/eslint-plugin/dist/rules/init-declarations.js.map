{"version": 3, "file": "init-declarations.js", "sourceRoot": "", "sources": ["../../src/rules/init-declarations.ts"], "names": [], "mappings": ";;AAEA,oDAA0D;AAO1D,kCAAqC;AACrC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,mBAAmB,CAAC,CAAC;AAKxD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,2DAA2D;QAC3D,IAAI,EAAE;YACJ,WAAW,EACT,6DAA6D;YAC/D,eAAe,EAAE,IAAI;SACtB;QACD,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;QAChC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;KAC7B;IACD,cAAc,EAAE,CAAC,QAAQ,CAAC;IAC1B,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;QACpB,oEAAoE;QACpE,2EAA2E;QAC3E,qBAAqB;QACrB,SAAS,sBAAsB;YAC7B,MAAM,cAAc,GAA0B,UAAU,CAAC,EAAE;gBACzD,IAAI,MAAM,IAAI,UAAU,IAAI,UAAU,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;oBACnD,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,UAAU,CAAC;oBACrC,gEAAgE;oBAChE,qEAAqE;oBACrE,sEAAsE;oBACtE,sEAAsE;oBACtE,kEAAkE;oBAClE,eAAe;oBACf,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;wBAC/C,IAAI,CAAC,IAAI,IAAI,IAAI,EACjB,CAAC;wBACD,OAAO,CAAC,MAAM,CAAC;4BACb,GAAG,IAAI;4BACP,GAAG,EAAE,YAAY,CAAC,IAAI,CAAC;yBACxB,CAAC,CAAC;wBACH,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC7B,CAAC,CAAC;YAEF,yEAAyE;YACzE,+DAA+D;YAC/D,EAAE;YACF,sEAAsE;YACtE,oEAAoE;YACpE,EAAE;YACF,uEAAuE;YACvE,gCAAgC;YAChC,OAAO,IAAI,KAAK,CAAC,EAAoB,EAAE;gBACrC,GAAG,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAW,EAAE,CACvC,IAAI,KAAK,QAAQ;oBACf,CAAC,CAAC,cAAc;oBAChB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC;aAC3C,CAAC,CAAC;QACL,CAAC;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC,CAAC;QAExD,OAAO;YACL,0BAA0B,CAAC,IAAkC;gBAC3D,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACtB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;wBACjB,OAAO;oBACT,CAAC;oBACD,IAAI,2BAA2B,CAAC,IAAI,CAAC,EAAE,CAAC;wBACtC,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,KAAK,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC;SACF,CAAC;QAEF,SAAS,2BAA2B,CAClC,IAAkC;YAElC,IAAI,QAAQ,GAA8B,IAAI,CAAC,MAAM,CAAC;YAEtD,OAAO,QAAQ,EAAE,CAAC;gBAChB,IACE,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;oBACpD,QAAQ,CAAC,OAAO,EAChB,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC7B,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF,CAAC,CAAC;AAEH;;;GAGG;AACH,SAAS,YAAY,CACnB,IAAiC;IAEjC,MAAM,KAAK,GAAsB,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACjE,MAAM,GAAG,GAAsB;QAC7B,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;QACzB,kEAAkE;QAClE,sEAAsE;QACtE,6DAA6D;QAC7D,MAAM,EACJ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAI,IAAI,CAAC,EAA0B,CAAC,IAAI,CAAC,MAAM;KACvE,CAAC;IAEF,OAAO;QACL,KAAK;QACL,GAAG;KACJ,CAAC;AACJ,CAAC"}