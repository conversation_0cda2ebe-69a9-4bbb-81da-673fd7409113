{"version": 3, "file": "switch-exhaustiveness-check.js", "sourceRoot": "", "sources": ["../../src/rules/switch-exhaustiveness-check.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,sDAAwC;AACxC,+CAAiC;AAEjC,kCASiB;AAEjB,MAAM,uBAAuB,GAAG,gBAAgB,CAAC;AA6CjD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,6BAA6B;IACnC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,iDAAiD;YAC9D,oBAAoB,EAAE,IAAI;SAC3B;QACD,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,eAAe,EAAE,iCAAiC;YAClD,oBAAoB,EAClB,yEAAyE;YAC3E,qBAAqB,EACnB,kEAAkE;SACrE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,mCAAmC,EAAE;wBACnC,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,8EAA8E;qBAC5F;oBACD,kCAAkC,EAAE;wBAClC,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,gHAAgH;qBAC9H;oBACD,yBAAyB,EAAE;wBACzB,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,2FAA2F;qBACzG;oBACD,yBAAyB,EAAE;wBACzB,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,wEAAwE;qBACtF;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,mCAAmC,EAAE,IAAI;YACzC,kCAAkC,EAAE,KAAK;YACzC,yBAAyB,EAAE,KAAK;SACjC;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,mCAAmC,EACnC,kCAAkC,EAClC,yBAAyB,EACzB,yBAAyB,GAC1B,EACF;QAED,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC9D,MAAM,aAAa,GACjB,yBAAyB,IAAI,IAAI;YAC/B,CAAC,CAAC,IAAI,MAAM,CAAC,yBAAyB,EAAE,GAAG,CAAC;YAC5C,CAAC,CAAC,uBAAuB,CAAC;QAE9B,SAAS,qBAAqB,CAC5B,IAA8B;YAE9B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,qBAAqB,GAAG,QAAQ;gBACpC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBAC/C,CAAC,CAAC,EAAE,CAAC;YACP,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAExD,IAAI,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;gBAC/D,OAAO,kBAAkB,CAAC;YAC5B,CAAC;YAED,OAAO;QACT,CAAC;QAED,SAAS,iBAAiB,CAAC,IAA8B;YACvD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CACjC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,IAAI,IAAI,CACtC,CAAC;YAEF,MAAM,gBAAgB,GAAG,IAAA,mCAA4B,EACnD,QAAQ,EACR,IAAI,CAAC,YAAY,CAClB,CAAC;YAEF,MAAM,UAAU,GAAG,gBAAgB,CAAC,SAAS,EAAE,EAAE,WAEpC,CAAC;YAEd,MAAM,sBAAsB,GAC1B,6BAA6B,CAAC,gBAAgB,CAAC,CAAC;YAElD,MAAM,SAAS,GAAG,IAAI,GAAG,EAAW,CAAC;YACrC,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACpC,wEAAwE;gBACxE,kBAAkB;gBAClB,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;oBAC5B,SAAS;gBACX,CAAC;gBAED,MAAM,QAAQ,GAAG,IAAA,mCAA4B,EAC3C,QAAQ,EACR,UAAU,CAAC,IAAI,CAChB,CAAC;gBACF,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC1B,CAAC;YAED,MAAM,yBAAyB,GAAc,EAAE,CAAC;YAEhD,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACjE,KAAK,MAAM,gBAAgB,IAAI,OAAO,CAAC,qBAAqB,CAC1D,SAAS,CACV,EAAE,CAAC;oBACF,IACE,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC;wBAC/B,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,EACxC,CAAC;wBACD,SAAS;oBACX,CAAC;oBAED,6EAA6E;oBAC7E,qDAAqD;oBACrD,IACE,CAAC,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC;wBACrD,OAAO,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,EAClD,CAAC;wBACD,SAAS;oBACX,CAAC;oBAED,yBAAyB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACnD,CAAC;YACH,CAAC;YAED,OAAO;gBACL,sBAAsB;gBACtB,WAAW,EAAE,WAAW,IAAI,qBAAqB,CAAC,IAAI,CAAC;gBACvD,yBAAyB;gBACzB,UAAU;aACX,CAAC;QACJ,CAAC;QAED,SAAS,qBAAqB,CAC5B,IAA8B,EAC9B,cAA8B;YAE9B,MAAM,EAAE,WAAW,EAAE,yBAAyB,EAAE,UAAU,EAAE,GAC1D,cAAc,CAAC;YAEjB,mFAAmF;YACnF,sCAAsC;YACtC,IAAI,kCAAkC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;gBAC9D,OAAO;YACT,CAAC;YAED,IAAI,yBAAyB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,IAAI,CAAC,YAAY;oBACvB,SAAS,EAAE,uBAAuB;oBAClC,IAAI,EAAE;wBACJ,eAAe,EAAE,yBAAyB;6BACvC,GAAG,CAAC,WAAW,CAAC,EAAE,CACjB,OAAO,CAAC,aAAa,CAAC,WAAW,EAAE,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC;4BAC3D,CAAC,CAAC,UAAU,WAAW,CAAC,SAAS,EAAE,EAAE,WAAqB,EAAE;4BAC5D,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,CACtC;6BACA,IAAI,CAAC,KAAK,CAAC;qBACf;oBACD,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,iBAAiB;4BAC5B,GAAG,CAAC,KAAK;gCACP,OAAO,SAAS,CACd,KAAK,EACL,IAAI,EACJ,yBAAyB,EACzB,WAAW,EACX,UAAU,EAAE,QAAQ,EAAE,CACvB,CAAC;4BACJ,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,SAAS,SAAS,CAChB,KAAyB,EACzB,IAA8B,EAC9B,kBAAsC,EAAE,4BAA4B;QACpE,WAA+D,EAC/D,UAAmB;YAEnB,MAAM,QAAQ,GACZ,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAEnE,MAAM,UAAU,GAAG,QAAQ;gBACzB,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;gBACvC,CAAC,CAAC,qEAAqE;oBACrE,+CAA+C;oBAC/C,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAEtC,MAAM,YAAY,GAAG,EAAE,CAAC;YACxB,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE,CAAC;gBACnD,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;oBAC9B,YAAY,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;oBAClE,SAAS;gBACX,CAAC;gBAED,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,SAAS,EAAE,EAAE,WAAW,CAAC;gBACrE,IAAI,QAAQ,GAAG,OAAO,CAAC,aAAa,CAClC,iBAAiB,EACjB,EAAE,CAAC,SAAS,CAAC,YAAY,CAC1B;oBACC,CAAC,CAAC,oEAAoE;wBACpE,iBAAkB;oBACpB,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;gBAE5C,IACE,UAAU;oBACV,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,EAAE,CAAC;oBAC/C,IAAA,sBAAe,EAAC,iBAAiB,CAAC,QAAQ,EAAE,EAAE,eAAe,CAAC,MAAM,CAAC,EACrE,CAAC;oBACD,MAAM,iBAAiB,GAAG,iBAAiB;yBACxC,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC;yBACtB,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC;yBACvB,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;oBAE3B,QAAQ,GAAG,GAAG,UAAU,KAAK,iBAAiB,IAAI,CAAC;gBACrD,CAAC;gBAED,YAAY,CAAC,IAAI,CACf,QAAQ,QAAQ,6CAA6C,QAAQ;qBAClE,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC;qBACxB,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,WAAW,CACrC,CAAC;YACJ,CAAC;YAED,MAAM,SAAS,GAAG,YAAY;iBAC3B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,UAAU,GAAG,IAAI,EAAE,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEd,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,eAAe,GAAG,YAAY;yBACjC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,UAAU,EAAE,CAAC;yBACrC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAEZ,OAAO,KAAK,CAAC,gBAAgB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;gBAC9D,CAAC;gBACD,OAAO,KAAK,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,SAAS,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,gCAAgC;YAChC,MAAM,YAAY,GAAG,IAAA,iBAAU,EAC7B,OAAO,CAAC,UAAU,CAAC,aAAa,CAC9B,IAAI,CAAC,YAAY,EACjB,0BAAmB,CACpB,EACD,wBAAiB,CAAC,YAAY,CAAC,GAAG,EAAE,cAAc,CAAC,CACpD,CAAC;YACF,MAAM,YAAY,GAAG,IAAA,iBAAU,EAC7B,OAAO,CAAC,UAAU,CAAC,aAAa,CAC9B,IAAI,CAAC,YAAY,EACjB,0BAAmB,CACpB,EACD,wBAAiB,CAAC,YAAY,CAAC,GAAG,EAAE,cAAc,CAAC,CACpD,CAAC;YAEF,OAAO,KAAK,CAAC,gBAAgB,CAC3B,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC9C,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9C,CAAC;QACJ,CAAC;QAED,SAAS,iCAAiC,CACxC,cAA8B;YAE9B,IAAI,mCAAmC,EAAE,CAAC;gBACxC,OAAO;YACT,CAAC;YAED,MAAM,EAAE,sBAAsB,EAAE,WAAW,EAAE,yBAAyB,EAAE,GACtE,cAAc,CAAC;YAEjB,IACE,yBAAyB,CAAC,MAAM,KAAK,CAAC;gBACtC,WAAW,IAAI,IAAI;gBACnB,CAAC,sBAAsB,EACvB,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,sBAAsB;iBAClC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,SAAS,6BAA6B,CACpC,IAA8B,EAC9B,cAA8B;YAE9B,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;YAED,MAAM,EAAE,sBAAsB,EAAE,WAAW,EAAE,GAAG,cAAc,CAAC;YAE/D,IAAI,sBAAsB,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;gBAClD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,IAAI,CAAC,YAAY;oBACvB,SAAS,EAAE,uBAAuB;oBAClC,IAAI,EAAE,EAAE,eAAe,EAAE,SAAS,EAAE;oBACpC,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,iBAAiB;4BAC5B,GAAG,CAAC,KAAK;gCACP,OAAO,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,CAAC;4BACrD,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,eAAe,CAAC,IAAI;gBAClB,MAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBAE/C,qBAAqB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;gBAC5C,iCAAiC,CAAC,cAAc,CAAC,CAAC;gBAClD,6BAA6B,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YACtD,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,qBAAqB,CAAC,IAAa;IAC1C,OAAO,OAAO,CAAC,aAAa,CAC1B,IAAI,EACJ,EAAE,CAAC,SAAS,CAAC,OAAO;QAClB,EAAE,CAAC,SAAS,CAAC,SAAS;QACtB,EAAE,CAAC,SAAS,CAAC,IAAI;QACjB,EAAE,CAAC,SAAS,CAAC,cAAc,CAC9B,CAAC;AACJ,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,6BAA6B,CAAC,IAAa;IAClD,OAAO,OAAO;SACX,cAAc,CAAC,IAAI,CAAC;SACpB,IAAI,CAAC,IAAI,CAAC,EAAE,CACX,OAAO;SACJ,qBAAqB,CAAC,IAAI,CAAC;SAC3B,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,CACrD,CAAC;AACN,CAAC"}