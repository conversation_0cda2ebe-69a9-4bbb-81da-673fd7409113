{"version": 3, "file": "Reference.js", "sourceRoot": "", "sources": ["../../src/referencer/Reference.ts"], "names": [], "mappings": ";;;AAKA,8BAA0C;AAE1C,IAAY,aAIX;AAJD,WAAY,aAAa;IACvB,iDAAU,CAAA;IACV,mDAAW,CAAA;IACX,2DAAe,CAAA;AACjB,CAAC,EAJW,aAAa,6BAAb,aAAa,QAIxB;AAQD,MAAM,SAAS,GAAG,IAAA,sBAAiB,GAAE,CAAC;AAEtC,IAAY,iBAGX;AAHD,WAAY,iBAAiB;IAC3B,2DAAW,CAAA;IACX,yDAAU,CAAA;AACZ,CAAC,EAHW,iBAAiB,iCAAjB,iBAAiB,QAG5B;AAED;;GAEG;AACH,MAAa,SAAS;IACpB;;OAEG;IACa,GAAG,GAAW,SAAS,EAAE,CAAC;IAE1C;;OAEG;IACM,KAAK,CAAgB;IAE9B;;;OAGG;IACa,IAAI,CAAQ;IAE5B;;;OAGG;IACa,UAAU,CAA+C;IAEzE;;;OAGG;IACa,IAAI,CAAW;IAEf,mBAAmB,CAAkC;IAErE;;;OAGG;IACI,QAAQ,CAAkB;IAEjC;;;OAGG;IACa,SAAS,CAAwB;IAEjD;;OAEG;IACM,cAAc,CAAoB;IAE3C,YACE,UAAwD,EACxD,KAAY,EACZ,IAAmB,EACnB,SAAgC,EAChC,mBAAoD,EACpD,IAAc,EACd,aAAa,GAAG,iBAAiB,CAAC,KAAK;QAEvC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAElB,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,CAAC;QAED,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACxB,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACzB,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC/D,CAAC;IAED;;;OAGG;IACI,OAAO;QACZ,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACI,MAAM;QACX,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACI,UAAU;QACf,OAAO,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC,IAAI,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,WAAW;QAChB,OAAO,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC,KAAK,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACI,WAAW;QAChB,OAAO,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC,SAAS,CAAC;IAChD,CAAC;CACF;AA5HD,8BA4HC"}