/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./node_modules/swiper/swiper.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/**
 * Swiper 10.3.1
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2023 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: September 28, 2023
 */

/* FONT_START */
@font-face {
  font-family: 'swiper-icons';
  src: url('data:application/font-woff;charset=utf-8;base64, 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');
  font-weight: 400;
  font-style: normal;
}
/* FONT_END */
:root {
  --swiper-theme-color: #007aff;
  /*
  --swiper-preloader-color: var(--swiper-theme-color);
  --swiper-wrapper-transition-timing-function: initial;
  */
}
:host {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  z-index: 1;
}
.swiper {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  overflow: clip;
  list-style: none;
  padding: 0;
  /* Fix of Webkit flickering */
  z-index: 1;
  display: block;
}
.swiper-vertical > .swiper-wrapper {
  flex-direction: column;
}
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  transition-property: transform;
  transition-timing-function: var(--swiper-wrapper-transition-timing-function, initial);
  box-sizing: content-box;
}
.swiper-android .swiper-slide,
.swiper-ios .swiper-slide,
.swiper-wrapper {
  transform: translate3d(0px, 0, 0);
}
.swiper-horizontal {
  touch-action: pan-y;
}
.swiper-vertical {
  touch-action: pan-x;
}
.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  transition-property: transform;
  display: block;
}
.swiper-slide-invisible-blank {
  visibility: hidden;
}
/* Auto Height */
.swiper-autoheight,
.swiper-autoheight .swiper-slide {
  height: auto;
}
.swiper-autoheight .swiper-wrapper {
  align-items: flex-start;
  transition-property: transform, height;
}
.swiper-backface-hidden .swiper-slide {
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
/* 3D Effects */
.swiper-3d.swiper-css-mode .swiper-wrapper {
  perspective: 1200px;
}
.swiper-3d .swiper-wrapper {
  transform-style: preserve-3d;
}
.swiper-3d {
  perspective: 1200px;
}
.swiper-3d .swiper-slide,
.swiper-3d .swiper-cube-shadow {
  transform-style: preserve-3d;
}
/* CSS Mode */
.swiper-css-mode > .swiper-wrapper {
  overflow: auto;
  scrollbar-width: none;
  /* For Firefox */
  -ms-overflow-style: none;
  /* For Internet Explorer and Edge */
}
.swiper-css-mode > .swiper-wrapper::-webkit-scrollbar {
  display: none;
}
.swiper-css-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: start start;
}
.swiper-css-mode.swiper-horizontal > .swiper-wrapper {
  scroll-snap-type: x mandatory;
}
.swiper-css-mode.swiper-vertical > .swiper-wrapper {
  scroll-snap-type: y mandatory;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper {
  scroll-snap-type: none;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: none;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper::before {
  content: '';
  flex-shrink: 0;
  order: 9999;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: center center;
  scroll-snap-stop: always;
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper > .swiper-slide:first-child {
  -webkit-margin-start: var(--swiper-centered-offset-before);
          margin-inline-start: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper::before {
  height: 100%;
  min-height: 1px;
  width: var(--swiper-centered-offset-after);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper > .swiper-slide:first-child {
  -webkit-margin-before: var(--swiper-centered-offset-before);
          margin-block-start: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper::before {
  width: 100%;
  min-width: 1px;
  height: var(--swiper-centered-offset-after);
}
/* Slide styles start */
/* 3D Shadows */
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom,
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}
.swiper-3d .swiper-slide-shadow {
  background: rgba(0, 0, 0, 0.15);
}
.swiper-3d .swiper-slide-shadow-left {
  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-right {
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-top {
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-bottom {
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-lazy-preloader {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  transform-origin: 50%;
  box-sizing: border-box;
  border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
  border-radius: 50%;
  border-top-color: transparent;
}
.swiper:not(.swiper-watch-progress) .swiper-lazy-preloader,
.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader {
  animation: swiper-preloader-spin 1s infinite linear;
}
.swiper-lazy-preloader-white {
  --swiper-preloader-color: #fff;
}
.swiper-lazy-preloader-black {
  --swiper-preloader-color: #000;
}
@keyframes swiper-preloader-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Slide styles end */

/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./node_modules/swiper/modules/navigation.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
:root {
  --swiper-navigation-size: 44px;
  /*
  --swiper-navigation-top-offset: 50%;
  --swiper-navigation-sides-offset: 10px;
  --swiper-navigation-color: var(--swiper-theme-color);
  */
}
.swiper-button-prev,
.swiper-button-next {
  position: absolute;
  top: var(--swiper-navigation-top-offset, 50%);
  width: calc(var(--swiper-navigation-size) / 44 * 27);
  height: var(--swiper-navigation-size);
  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color));
}
.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
  opacity: 0.35;
  cursor: auto;
  pointer-events: none;
}
.swiper-button-prev.swiper-button-hidden,
.swiper-button-next.swiper-button-hidden {
  opacity: 0;
  cursor: auto;
  pointer-events: none;
}
.swiper-navigation-disabled .swiper-button-prev,
.swiper-navigation-disabled .swiper-button-next {
  display: none !important;
}
.swiper-button-prev svg,
.swiper-button-next svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transform-origin: center;
}
.swiper-rtl .swiper-button-prev svg,
.swiper-rtl .swiper-button-next svg {
  transform: rotate(180deg);
}
.swiper-button-prev,
.swiper-rtl .swiper-button-next {
  left: var(--swiper-navigation-sides-offset, 10px);
  right: auto;
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}
.swiper-button-lock {
  display: none;
}
/* Navigation font start */
.swiper-button-prev:after,
.swiper-button-next:after {
  font-family: swiper-icons;
  font-size: var(--swiper-navigation-size);
  text-transform: none !important;
  letter-spacing: 0;
  font-feature-settings: ;
  font-variant: initial;
  line-height: 1;
}
.swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
  content: 'prev';
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}
.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
  content: 'next';
}
/* Navigation font end */

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./node_modules/yet-another-react-lightbox/dist/styles.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
.yarl__fullsize{height:100%;width:100%}.yarl__relative{position:relative}.yarl__portal{bottom:0;left:0;opacity:0;overflow:hidden;position:fixed;right:0;top:0;transition:opacity var(--yarl__fade_animation_duration,.25s) var(--yarl__fade_animation_timing_function,ease);z-index:var(--yarl__portal_zindex,9999)}.yarl__portal_open{opacity:1}.yarl__container{background-color:var(--yarl__container_background_color,var(--yarl__color_backdrop,#000));bottom:0;left:0;outline:none;overflow:hidden;overscroll-behavior:var(--yarl__controller_overscroll_behavior,contain);position:absolute;right:0;top:0;touch-action:var(--yarl__controller_touch_action,none);-webkit-user-select:none;-moz-user-select:none;user-select:none}.yarl__carousel{align-content:center;align-items:stretch;display:flex;flex:0 0 auto;height:100%;justify-content:center;opacity:var(--yarl__pull_opacity,1);transform:translate(var(--yarl__swipe_offset,0),var(--yarl__pull_offset,0));width:calc(100% + (var(--yarl__carousel_slides_count) - 1)*(100% + var(--yarl__carousel_spacing_px, 0)*1px + var(--yarl__carousel_spacing_percent, 0)*1%))}.yarl__carousel_with_slides{column-gap:calc(var(--yarl__carousel_spacing_px, 0)*1px + 100/(var(--yarl__carousel_slides_count)*100 + (var(--yarl__carousel_slides_count) - 1)*var(--yarl__carousel_spacing_percent, 0))*var(--yarl__carousel_spacing_percent, 0)*1%)}.yarl__flex_center{align-content:center;align-items:center;display:flex;justify-content:center}.yarl__slide{flex:1 1;overflow:hidden;padding:calc(var(--yarl__carousel_padding_px, 0)*1px + 100/(var(--yarl__carousel_slides_count)*100 + (var(--yarl__carousel_slides_count) - 1)*var(--yarl__carousel_spacing_percent, 0))*var(--yarl__carousel_padding_percent, 0)*1%);position:relative}[dir=rtl] .yarl__slide{--yarl__direction:-1}.yarl__slide_image{max-height:100%;max-width:100%;object-fit:contain;touch-action:var(--yarl__controller_touch_action,none);-moz-user-select:none;user-select:none;-webkit-user-select:none;-webkit-touch-callout:none}.yarl__slide_image_cover{height:100%;object-fit:cover;width:100%}.yarl__slide_image_loading{opacity:0}@media screen and (min-width:800px){.yarl__slide_wrapper:not(.yarl__slide_wrapper_interactive) .yarl__slide_image{-webkit-backface-visibility:hidden;-webkit-transform:translateZ(0);-webkit-transform-style:preserve-3d}}.yarl__slide_placeholder{left:50%;line-height:0;position:absolute;top:50%;transform:translateX(-50%) translateY(-50%)}.yarl__slide_loading{animation:yarl__delayed_fadein 1s linear;color:var(--yarl__slide_icon_loading_color,var(--yarl__color_button,hsla(0,0%,100%,.8)))}.yarl__slide_loading line{animation:yarl__stroke_opacity 1s linear infinite}.yarl__slide_loading line:first-of-type{animation-delay:-1.875s}.yarl__slide_loading line:nth-of-type(2){animation-delay:-1.75s}.yarl__slide_loading line:nth-of-type(3){animation-delay:-1.625s}.yarl__slide_loading line:nth-of-type(4){animation-delay:-1.5s}.yarl__slide_loading line:nth-of-type(5){animation-delay:-1.375s}.yarl__slide_loading line:nth-of-type(6){animation-delay:-1.25s}.yarl__slide_loading line:nth-of-type(7){animation-delay:-1.125s}.yarl__slide_loading line:nth-of-type(8){animation-delay:-1s}.yarl__slide_error{color:var(--yarl__slide_icon_error_color,red);height:var(--yarl__slide_icon_error_size,48px);width:var(--yarl__slide_icon_error_size,48px)}@media (prefers-reduced-motion){.yarl__portal,.yarl__slide{transition:unset}.yarl__slide_loading,.yarl__slide_loading line{animation:unset}}.yarl__toolbar{bottom:auto;display:flex;justify-content:flex-end;left:auto;padding:var(--yarl__toolbar_padding,8px);position:absolute;right:0;top:0}[dir=rtl] .yarl__toolbar{bottom:auto;left:0;right:auto;top:0}.yarl__icon{height:var(--yarl__icon_size,32px);width:var(--yarl__icon_size,32px)}.yarl__button{-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:var(--yarl__button_background_color,transparent);border:var(--yarl__button_border,0);color:var(--yarl__color_button,hsla(0,0%,100%,.8));cursor:pointer;filter:var(--yarl__button_filter,drop-shadow(2px 2px 2px rgba(0,0,0,.8)));line-height:0;margin:var(--yarl__button_margin,0);outline:none;padding:var(--yarl__button_padding,8px);-webkit-tap-highlight-color:transparent}.yarl__button:focus{color:var(--yarl__color_button_active,#fff)}.yarl__button:focus:not(:focus-visible){color:var(--yarl__color_button,hsla(0,0%,100%,.8))}.yarl__button:focus-visible{color:var(--yarl__color_button_active,#fff)}@media (hover:hover){.yarl__button:focus-visible:hover,.yarl__button:focus:hover,.yarl__button:hover{color:var(--yarl__color_button_active,#fff)}}.yarl__button:disabled{color:var(--yarl__color_button_disabled,hsla(0,0%,100%,.4));cursor:default}.yarl__navigation_next,.yarl__navigation_prev{padding:var(--yarl__navigation_button_padding,24px 16px);position:absolute;top:50%;transform:translateY(-50%)}.yarl__navigation_prev{left:0}[dir=rtl] .yarl__navigation_prev{left:unset;right:0;transform:translateY(-50%) rotate(180deg)}.yarl__navigation_next{right:0}[dir=rtl] .yarl__navigation_next{left:0;right:unset;transform:translateY(-50%) rotate(180deg)}.yarl__no_scroll{height:100%;overflow:hidden;overscroll-behavior:none}@keyframes yarl__delayed_fadein{0%{opacity:0}80%{opacity:0}to{opacity:1}}@keyframes yarl__stroke_opacity{0%{stroke-opacity:1}to{stroke-opacity:.125}}
/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./public/assets/css/module-css/product.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/***
=============================================
Product
=============================================
***/
.product-one {
   position: relative;
   display: block;
   background: var(--logistiq-white);
   padding: 120px 0px 120px;
   z-index: 1;
}

.product-one__content {
   position: relative;
   display: block;
   /* margin-top: 120px; */
   margin-right: -50px;
   z-index: 2;
}

.product-one__img {
   position: relative;
   display: block;
   width: 100%;
}
.f-right {
   float: right;
}

/***
=============================================
Nav Tabs
=============================================
***/

/* .product-menu .nav-tabs {
   background-color: var(--logistiq-white);
   border-bottom: 0px solid #fff;
   font-weight: bold;
}

.product-menu .nav-item {
   border: 2px solid var(--logistiq-white);
   border-radius: 5px;
   border-bottom: 0px;
} */

.product-menu {
   display: flex;
   flex-direction: column;
}

.product-menu .nav-link {
   flex: 1 1;
   height: 100%;
   justify-content: center;
   align-items: center;
   border: 1px solid #f8f9fa;
   /* border-bottom: none; */
   border-radius: 5px 5px 0px 0px;
   /* color: #555; */
   padding: 10px 15px;
   background-color: rgba(223, 224, 224, 0.6);
   font-weight: bold;
}

.product-menu .nav-tabs {
   border-bottom: none;
}
/* .product-menu .nav-link:hover {
   background-color: rgb(223, 224, 224);
   border-color: rgb(223, 224, 224);
   border-color: rgb(223, 224, 224);
} */

.product-menu .conifer-menu .nav-link {
   border-bottom: 1px solid #849ced;
   color: var(--logistiq-white);
   background-color: rgba(50, 90, 225, 0.6);
}
.product-menu .conifer-menu .nav-link:hover {
   border-bottom: 1px solid #5b7be7;
   color: var(--logistiq-white);
   background-color: rgba(50, 90, 225, 0.8);
}

.product-menu .conifer-menu .nav-link.active {
   border: 1px solid var(--logistiq-base);
   border-bottom-color: transparent;
   color: var(--logistiq-white);
   background-color: var(--logistiq-base);
   font-weight: bold;
}

.product-menu .bamboo-menu .nav-link {
   border-bottom: 1px solid #80907e;
   background-color: rgba(68, 85, 57, 0.65);
   color: var(--logistiq-white);
}

.product-menu .bamboo-menu .nav-link:hover {
   border-bottom: 1px solid rgb(99, 119, 97);
   background-color: rgba(68, 85, 57, 0.85);
   color: var(--logistiq-white);
}

.product-menu .bamboo-menu .nav-link.active {
   border: 1px solid rgb(68, 85, 57);
   border-bottom-color: transparent;
   color: var(--logistiq-white);
   background-color: rgb(68, 85, 57);
   font-weight: bold;
}

.product-menu .teak-menu .nav-link {
   border-bottom: 1px solid #d6c3b2;
   background-color: rgba(186, 155, 127, 0.6);
   color: var(--logistiq-white);
}

.product-menu .teak-menu .nav-link:hover {
   border-bottom: 1px solid #c2a78e;
   background-color: rgba(186, 155, 127, 0.88);
   color: var(--logistiq-white);
}

.product-menu .teak-menu .nav-link.active {
   border: 1px solid rgb(186, 155, 127);
   border-bottom-color: transparent;
   color: var(--logistiq-white);
   background-color: rgb(186, 155, 127);
   font-weight: bold;
}

/* Style the button element inside nav-link*/
.product-menu .nav-link button {
   background: none;
   border: none;
   padding: 0;
   cursor: pointer;
   color: inherit; /* inherit color from nav-link */
   font-size: inherit;
   font-family: inherit;
   outline: none; /* prevent outline on focus */
}

.faq-one__content-faq.conifer-faq {
   border: 1px solid var(--logistiq-base);
}
.faq-one__content-faq.bamboo-faq {
   border: 1px solid rgb(68, 85, 57);
}
.faq-one__content-faq.teak-faq {
   border: 1px solid rgb(186, 155, 127);
}

/***
=============================================
Buttons
=============================================
***/
.conifer-btn {
   /* height: 9%;
   border: none; */
   background-color: rgba(50, 90, 225, 0.9);
   color: #fff;
   padding: 5px 20px;
   border-radius: 3px;
   align-items: center;
   position: absolute;
   top: 10px;
   right: 10px;
}
.conifer-btn:hover {
   background-color: rgba(6, 63, 225, 0.9);
   -webkit-text-stroke: 0.05rem;
}

.bamboo-btn {
   /* height: 9%;
   border: none; */
   background-color: rgba(68, 85, 57, 0.9);
   color: #fff;
   padding: 5px 20px;
   border-radius: 3px;
   align-items: center;
   position: absolute;
   top: 10px;
   right: 10px;
}
.bamboo-btn:hover {
   background-color: rgba(55, 70, 49, 0.9);
   -webkit-text-stroke: 0.05rem;
}

.teak-btn {
   /* height: 9%;
   border: none; */
   background-color: rgba(186, 155, 127, 0.9);
   color: #fff;
   padding: 5px 20px;
   border-radius: 3px;
   align-items: center;
   position: absolute;
   top: 10px;
   right: 10px;
}
.teak-btn:hover {
   background-color: rgba(148, 123, 101, 0.9);
   -webkit-text-stroke: 0.05rem;
}

/***
=============================================
Accordion
=============================================
***/
/* Default styles for larger screens (tabs) */
/* .product-menu {
   display: flex;
   justify-content: space-between;
} */

.product-content {
   display: flex;
   justify-content: space-between;
   padding: 0;
}

.accordion {
   display: none;
}

.accordion-conifer {
   position: relative;
   display: block;
   cursor: pointer;
   background-color: var(--logistiq-base);
   padding: 15px 25px;
   transition: all 200ms linear;
   transition-delay: 0.1s;
}

.accordion-conifer h5 {
   position: relative;
   color: var(--logistiq-white);
   font-weight: 700;
   text-transform: capitalize;
   transition: all 500ms ease;
}

.accordion-conifer h5::before {
   position: absolute;
   color: var(--logistiq-white);
   font-size: 15px;
   -webkit-text-stroke: 2px var(--logistiq-white);
   top: 52%;
   right: 15px;
   transform: translateY(-50%);
   -webkit-transform: translateY(-50%);
   transition: all 500ms ease;
   text-align: center;
   content: "\e923";
   font-family: "icomoon" !important;
}

.accordion-conifer.active h5::before {
   position: absolute;
   color: var(--logistiq-white);
   font-size: 15px;
   -webkit-text-stroke: 2px var(--logistiq-white);
   top: 52%;
   right: 15px;
   transform: translateY(-50%);
   -webkit-transform: translateY(-50%);
   transition: all 500ms ease;
   text-align: center;
   content: "\e924";
   font-family: "icomoon" !important;
}

.accordion-bamboo {
   position: relative;
   display: block;
   cursor: pointer;
   background-color: rgb(55, 70, 49);
   padding: 15px 25px;
   transition: all 200ms linear;
   transition-delay: 0.1s;
}

.accordion-bamboo h5 {
   position: relative;
   color: var(--logistiq-white);
   font-weight: 700;
   text-transform: capitalize;
   transition: all 500ms ease;
}

.accordion-bamboo h5::before {
   position: absolute;
   color: var(--logistiq-white);
   font-size: 15px;
   -webkit-text-stroke: 2px var(--logistiq-white);
   top: 55%;
   right: 15px;
   transform: translateY(-50%);
   -webkit-transform: translateY(-50%);
   transition: all 500ms ease;
   text-align: center;
   content: "\e923";
   font-family: "icomoon" !important;
}

.accordion-bamboo.active h5::before {
   position: absolute;
   color: var(--logistiq-white);
   font-size: 15px;
   -webkit-text-stroke: 2px var(--logistiq-white);
   top: 55%;
   right: 15px;
   transform: translateY(-50%);
   -webkit-transform: translateY(-50%);
   transition: all 500ms ease;
   text-align: center;
   content: "\e924";
   font-family: "icomoon" !important;
}

.accordion-teak {
   position: relative;
   display: block;
   cursor: pointer;
   background-color: rgb(186, 155, 127);
   padding: 15px 25px;
   transition: all 200ms linear;
   transition-delay: 0.1s;
}

.accordion-teak h5 {
   position: relative;
   color: var(--logistiq-white);
   font-weight: 700;
   text-transform: capitalize;
   transition: all 500ms ease;
}

.accordion-teak h5::before {
   position: absolute;
   color: var(--logistiq-white);
   font-size: 15px;
   -webkit-text-stroke: 2px var(--logistiq-white);
   top: 55%;
   right: 15px;
   transform: translateY(-50%);
   -webkit-transform: translateY(-50%);
   transition: all 500ms ease;
   text-align: center;
   content: "\e923";
   font-family: "icomoon" !important;
}

.accordion-conifer.teak h5::before {
   position: absolute;
   color: var(--logistiq-white);
   font-size: 15px;
   -webkit-text-stroke: 2px var(--logistiq-white);
   top: 55%;
   right: 15px;
   transform: translateY(-50%);
   -webkit-transform: translateY(-50%);
   transition: all 500ms ease;
   text-align: center;
   content: "\e924";
   font-family: "icomoon" !important;
}

/* .accordion-item:last-of-type {
   margin-bottom: 0;
   border-bottom-right-radius: 0rem;
   border-bottom-left-radius: 0rem;
} */

/* Mobile styles (accordion) */
@media (max-width: 768px) {
   .product-menu {
      display: none;
   }

   .product-content {
      display: none;
   }

   .container.conifer {
      border: none;
   }

   .accordion {
      display: block;
      padding: 0 20px;
   }

   .faq-one__content-faq.conifer-faq {
      border: none;
   }
   .faq-one__content-faq.bamboo-faq {
      border: none;
   }
   .faq-one__content-faq.teak-faq {
      border: none;
   }

   .accordion-item {
      margin-bottom: 0;
      border: none;
   }

   .accordion-item.conifer {
      border: solid 1.5px var(--logistiq-base);
   }

   .accordion-item.bamboo {
      border: solid 1.5px rgb(55, 70, 49);
   }

   .accordion-item.teak {
      border: solid 1.5px rgb(186, 155, 127);
   }

   .accrodion-title.accordion-conifer {
      background-color: var(--logistiq-base);
      padding: 15px 25px;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
   }

   .accrodion-title.accordion-conifer h5 {
      color: var(--logistiq-white);
   }

   .accordion-button {
      width: 100%;
      text-align: left;
      padding: 10px;
      /* background-color: #f1f1f1; */
      border: none;
      cursor: pointer;
   }

   .accordion-content {
      display: none;
      padding: 0;
      background-color: #fff;
      /* border: 1px solid #ddd; */
   }

   .accordion-content.active {
      display: block;
   }

   .accordion-item.conifer {
      border: 1px solid var(--logistiq-base);
   }

   .accordion-button.conifer ::before {
      background-color: #6d89e7;
      color: var(--logistiq-white);
   }

   .accordion-button.conifer ::after {
      background-color: var(--logistiq-base);
      color: var(--logistiq-white);
   }

   .accordion-button.conifer h5 {
      color: var(--logistiq-white);
   }

   .accordion-button.bamboo {
      background-color: #d5e8a8;
   }

   .accordion-button.teak {
      background-color: #e8d5a8;
   }

   .accordion-button:not(.collapsed)::after {
      content: "\e924"; /* Example: down arrow icon */
      color: #fff; /* Set the color to white */
      float: right;
      margin-left: 10px;
      font-family: "icomoon";
      display: flex;
      align-items: center;
      font-weight: 700;
      position: absolute;
      right: 10px;
   }
}

