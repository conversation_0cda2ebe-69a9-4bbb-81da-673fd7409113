{"c": ["app/[locale]/layout", "app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fcomponents%2Felements%2FCookiesConsent.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fcomponents%2Felements%2FFacebookMSG.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"]}