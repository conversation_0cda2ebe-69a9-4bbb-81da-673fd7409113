{"c": ["app/[locale]/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./components/elements/CookiesConsent.js", "(app-pages-browser)/./components/elements/FacebookMSG.js", "(app-pages-browser)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js", "(app-pages-browser)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fcomponents%2Felements%2FCookiesConsent.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fcomponents%2Felements%2FFacebookMSG.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fnode_modules%2Freact-modal-video%2Fcss%2Fmodal-video.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fpublic%2Fassets%2Fcss%2Fstyle.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fnode_modules%2Fswiper%2Fswiper.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fnode_modules%2Fswiper%2Fmodules%2Fpagination.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fnode_modules%2Fswiper%2Fmodules%2Ffree-mode.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22lib%2Ffont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--logistiq-font%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22lib%2Ffont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_Thai%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22thai%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--logistiq-thai-font%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansTh%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnopparattk%2FDocuments%2FSAK%20WoodWorks%2FWebsite%2FLanding%20page%2Fdeveloping%2Fintl2-companyprofile-v02%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22lib%2Ffont.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22cyrillic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--logistiq-sc-font%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js", "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"lib/font.js\",\"import\":\"Noto_Sans\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"subsets\":[\"latin\",\"cyrillic\"],\"variable\":\"--logistiq-font\",\"display\":\"swap\"}],\"variableName\":\"notoSans\"}", "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"lib/font.js\",\"import\":\"Noto_Sans_SC\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"subsets\":[\"latin\",\"cyrillic\"],\"variable\":\"--logistiq-sc-font\",\"display\":\"swap\"}],\"variableName\":\"notoSansSC\"}", "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"lib/font.js\",\"import\":\"Noto_Sans_Thai\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"subsets\":[\"thai\"],\"variable\":\"--logistiq-thai-font\",\"display\":\"swap\"}],\"variableName\":\"notoSansTh\"}", "(app-pages-browser)/./node_modules/react-modal-video/css/modal-video.css", "(app-pages-browser)/./node_modules/swiper/modules/free-mode.css", "(app-pages-browser)/./node_modules/swiper/modules/pagination.css", "(app-pages-browser)/./public/assets/css/module-css/chat-button.css", "(app-pages-browser)/./public/assets/css/module-css/cookiesconsent.css", "(app-pages-browser)/./public/assets/css/style.css"]}