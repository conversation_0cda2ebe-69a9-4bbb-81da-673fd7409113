globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/[locale]/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/layout/Layout.js":{"*":{"id":"(ssr)/./components/layout/Layout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sections/index/About.js":{"*":{"id":"(ssr)/./components/sections/index/About.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sections/index/Banner.js":{"*":{"id":"(ssr)/./components/sections/index/Banner.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sections/index/Branch.js":{"*":{"id":"(ssr)/./components/sections/index/Branch.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sections/index/Contact-submit.js":{"*":{"id":"(ssr)/./components/sections/index/Contact-submit.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sections/index/Product-accordian.js":{"*":{"id":"(ssr)/./components/sections/index/Product-accordian.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sections/index/Service.js":{"*":{"id":"(ssr)/./components/sections/index/Service.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sections/index/Store.js":{"*":{"id":"(ssr)/./components/sections/index/Store.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/elements/CookiesConsent.js":{"*":{"id":"(ssr)/./components/elements/CookiesConsent.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/elements/FacebookMSG.js":{"*":{"id":"(ssr)/./components/elements/FacebookMSG.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":{"*":{"id":"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/swiper/swiper.css":{"*":{"id":"(ssr)/./node_modules/swiper/swiper.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js":{"id":"(app-pages-browser)/./components/layout/Layout.js","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/About.js":{"id":"(app-pages-browser)/./components/sections/index/About.js","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Banner.js":{"id":"(app-pages-browser)/./components/sections/index/Banner.js","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Branch.js":{"id":"(app-pages-browser)/./components/sections/index/Branch.js","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Contact-submit.js":{"id":"(app-pages-browser)/./components/sections/index/Contact-submit.js","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Product-accordian.js":{"id":"(app-pages-browser)/./components/sections/index/Product-accordian.js","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Service.js":{"id":"(app-pages-browser)/./components/sections/index/Service.js","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Store.js":{"id":"(app-pages-browser)/./components/sections/index/Store.js","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/elements/CookiesConsent.js":{"id":"(app-pages-browser)/./components/elements/CookiesConsent.js","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/elements/FacebookMSG.js":{"id":"(app-pages-browser)/./components/elements/FacebookMSG.js","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":{"id":"(app-pages-browser)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/react-modal-video/css/modal-video.css":{"id":"(app-pages-browser)/./node_modules/react-modal-video/css/modal-video.css","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/public/assets/css/style.css":{"id":"(app-pages-browser)/./public/assets/css/style.css","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/swiper/swiper.css":{"id":"(app-pages-browser)/./node_modules/swiper/swiper.css","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/swiper/modules/pagination.css":{"id":"(app-pages-browser)/./node_modules/swiper/modules/pagination.css","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/swiper/modules/free-mode.css":{"id":"(app-pages-browser)/./node_modules/swiper/modules/free-mode.css","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/font/google/target.css?{\"path\":\"lib/font.js\",\"import\":\"Noto_Sans\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"subsets\":[\"latin\",\"cyrillic\"],\"variable\":\"--logistiq-font\",\"display\":\"swap\"}],\"variableName\":\"notoSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"lib/font.js\",\"import\":\"Noto_Sans\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"subsets\":[\"latin\",\"cyrillic\"],\"variable\":\"--logistiq-font\",\"display\":\"swap\"}],\"variableName\":\"notoSans\"}","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/font/google/target.css?{\"path\":\"lib/font.js\",\"import\":\"Noto_Sans_Thai\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"subsets\":[\"thai\"],\"variable\":\"--logistiq-thai-font\",\"display\":\"swap\"}],\"variableName\":\"notoSansTh\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"lib/font.js\",\"import\":\"Noto_Sans_Thai\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"subsets\":[\"thai\"],\"variable\":\"--logistiq-thai-font\",\"display\":\"swap\"}],\"variableName\":\"notoSansTh\"}","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/font/google/target.css?{\"path\":\"lib/font.js\",\"import\":\"Noto_Sans_SC\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"subsets\":[\"latin\",\"cyrillic\"],\"variable\":\"--logistiq-sc-font\",\"display\":\"swap\"}],\"variableName\":\"notoSansSC\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"lib/font.js\",\"import\":\"Noto_Sans_SC\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"subsets\":[\"latin\",\"cyrillic\"],\"variable\":\"--logistiq-sc-font\",\"display\":\"swap\"}],\"variableName\":\"notoSansSC\"}","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/esm/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/":[],"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/app/layout":[],"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/app/loading":[],"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/app/not-found":[],"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/app/[locale]/page":["static/css/app/[locale]/page.css"],"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/app/[locale]/layout":["static/css/app/[locale]/layout.css"]}}