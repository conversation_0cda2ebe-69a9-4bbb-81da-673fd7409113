"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-intl";
exports.ids = ["vendor-chunks/use-intl"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-intl/dist/_IntlProvider.js":
/*!*****************************************************!*\
  !*** ./node_modules/use-intl/dist/_IntlProvider.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./development/_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fSW50bFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw2SUFBMEQ7QUFDNUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fSW50bFByb3ZpZGVyLmpzP2FmYzAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vcHJvZHVjdGlvbi9fSW50bFByb3ZpZGVyLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGV2ZWxvcG1lbnQvX0ludGxQcm92aWRlci5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/_IntlProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/_useLocale.js":
/*!**************************************************!*\
  !*** ./node_modules/use-intl/dist/_useLocale.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./development/_useLocale.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fdXNlTG9jYWxlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSx1SUFBdUQ7QUFDekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fdXNlTG9jYWxlLmpzPzRkYmIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vcHJvZHVjdGlvbi9fdXNlTG9jYWxlLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGV2ZWxvcG1lbnQvX3VzZUxvY2FsZS5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/_useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js":
/*!************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nconst IntlContext = /*#__PURE__*/React.createContext(undefined);\n\nexports.IntlContext = IntlContext;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9JbnRsQ29udGV4dC1CS2ZzbnpCeC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixZQUFZLG1CQUFPLENBQUMsd0dBQU87O0FBRTNCOztBQUVBLG1CQUFtQiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGwyLWNvbXBhbnlwcm9maWxlLXYwMi8uL25vZGVfbW9kdWxlcy91c2UtaW50bC9kaXN0L2RldmVsb3BtZW50L0ludGxDb250ZXh0LUJLZnNuekJ4LmpzPzdlY2IiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgUmVhY3QgPSByZXF1aXJlKCdyZWFjdCcpO1xuXG5jb25zdCBJbnRsQ29udGV4dCA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVDb250ZXh0KHVuZGVmaW5lZCk7XG5cbmV4cG9ydHMuSW50bENvbnRleHQgPSBJbnRsQ29udGV4dDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js":
/*!*****************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_IntlProvider.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar IntlContext = __webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\nfunction IntlProvider(_ref) {\n  let {\n    children,\n    defaultTranslationValues,\n    formats,\n    getMessageFallback,\n    locale,\n    messages,\n    now,\n    onError,\n    timeZone\n  } = _ref;\n  // The formatter cache is released when the locale changes. For\n  // long-running apps with a persistent `IntlProvider` at the root,\n  // this can reduce the memory footprint (e.g. in React Native).\n  const cache = React.useMemo(() => {\n    return initializeConfig.createCache();\n  }, [locale]);\n  const formatters = React.useMemo(() => initializeConfig.createIntlFormatters(cache), [cache]);\n\n  // Memoizing this value helps to avoid triggering a re-render of all\n  // context consumers in case the configuration didn't change. However,\n  // if some of the non-primitive values change, a re-render will still\n  // be triggered. Note that there's no need to put `memo` on `IntlProvider`\n  // itself, because the `children` typically change on every render.\n  // There's some burden on the consumer side if it's important to reduce\n  // re-renders, put that's how React works.\n  // See: https://blog.isquaredsoftware.com/2020/05/blogged-answers-a-mostly-complete-guide-to-react-rendering-behavior/#context-updates-and-render-optimizations\n  const value = React.useMemo(() => ({\n    ...initializeConfig.initializeConfig({\n      locale,\n      defaultTranslationValues,\n      formats,\n      getMessageFallback,\n      messages,\n      now,\n      onError,\n      timeZone\n    }),\n    formatters,\n    cache\n  }), [cache, defaultTranslationValues, formats, formatters, getMessageFallback, locale, messages, now, onError, timeZone]);\n  return /*#__PURE__*/React__default.default.createElement(IntlContext.IntlContext.Provider, {\n    value: value\n  }, children);\n}\n\nexports.IntlProvider = IntlProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js":
/*!***********************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar IntlContext = __webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n\nfunction useIntlContext() {\n  const context = React.useContext(IntlContext.IntlContext);\n  if (!context) {\n    throw new Error('No intl context found. Have you configured the provider? See https://next-intl.dev/docs/usage/configuration#client-server-components' );\n  }\n  return context;\n}\n\nfunction useLocale() {\n  return useIntlContext().locale;\n}\n\nexports.useIntlContext = useIntlContext;\nexports.useLocale = useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLUJLM2pPZWFBLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLFlBQVksbUJBQU8sQ0FBQyx3R0FBTztBQUMzQixrQkFBa0IsbUJBQU8sQ0FBQyx5R0FBMkI7O0FBRXJEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxzQkFBc0I7QUFDdEIsaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50bDItY29tcGFueXByb2ZpbGUtdjAyLy4vbm9kZV9tb2R1bGVzL3VzZS1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvX3VzZUxvY2FsZS1CSzNqT2VhQS5qcz9mZDcyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIFJlYWN0ID0gcmVxdWlyZSgncmVhY3QnKTtcbnZhciBJbnRsQ29udGV4dCA9IHJlcXVpcmUoJy4vSW50bENvbnRleHQtQktmc256QnguanMnKTtcblxuZnVuY3Rpb24gdXNlSW50bENvbnRleHQoKSB7XG4gIGNvbnN0IGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KEludGxDb250ZXh0LkludGxDb250ZXh0KTtcbiAgaWYgKCFjb250ZXh0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdObyBpbnRsIGNvbnRleHQgZm91bmQuIEhhdmUgeW91IGNvbmZpZ3VyZWQgdGhlIHByb3ZpZGVyPyBTZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvdXNhZ2UvY29uZmlndXJhdGlvbiNjbGllbnQtc2VydmVyLWNvbXBvbmVudHMnICk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59XG5cbmZ1bmN0aW9uIHVzZUxvY2FsZSgpIHtcbiAgcmV0dXJuIHVzZUludGxDb250ZXh0KCkubG9jYWxlO1xufVxuXG5leHBvcnRzLnVzZUludGxDb250ZXh0ID0gdXNlSW50bENvbnRleHQ7XG5leHBvcnRzLnVzZUxvY2FsZSA9IHVzZUxvY2FsZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_useLocale.js":
/*!**************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_useLocale.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _useLocale = __webpack_require__(/*! ./_useLocale-BK3jOeaA.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n\n\n\nexports.useLocale = _useLocale.useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0QsaUJBQWlCLG1CQUFPLENBQUMsdUdBQTBCO0FBQ25ELG1CQUFPLENBQUMsd0dBQU87QUFDZixtQkFBTyxDQUFDLHlHQUEyQjs7OztBQUluQyxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLmpzPzNiZWIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgX3VzZUxvY2FsZSA9IHJlcXVpcmUoJy4vX3VzZUxvY2FsZS1CSzNqT2VhQS5qcycpO1xucmVxdWlyZSgncmVhY3QnKTtcbnJlcXVpcmUoJy4vSW50bENvbnRleHQtQktmc256QnguanMnKTtcblxuXG5cbmV4cG9ydHMudXNlTG9jYWxlID0gX3VzZUxvY2FsZS51c2VMb2NhbGU7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/core.js":
/*!********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/core.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-QqAaZwGD.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction createTranslatorImpl(_ref, namespacePrefix) {\n  let {\n    messages,\n    namespace,\n    ...rest\n  } = _ref;\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the function invocation.\n  messages = messages[namespacePrefix];\n  namespace = createFormatter.resolveNamespace(namespace, namespacePrefix);\n  return createFormatter.createBaseTranslator({\n    ...rest,\n    messages,\n    namespace\n  });\n}\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction createTranslator(_ref) {\n  let {\n    _cache = initializeConfig.createCache(),\n    _formatters = initializeConfig.createIntlFormatters(_cache),\n    getMessageFallback = initializeConfig.defaultGetMessageFallback,\n    messages,\n    namespace,\n    onError = initializeConfig.defaultOnError,\n    ...rest\n  } = _ref;\n  // We have to wrap the actual function so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  return createTranslatorImpl({\n    ...rest,\n    onError,\n    cache: _cache,\n    formatters: _formatters,\n    getMessageFallback,\n    // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n    messages: {\n      '!': messages\n    },\n    namespace: namespace ? \"!.\".concat(namespace) : '!'\n  }, '!');\n}\n\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createFormatter = createFormatter.createFormatter;\nexports.createTranslator = createTranslator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js":
/*!****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar IntlMessageFormat = __webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar IntlMessageFormat__default = /*#__PURE__*/_interopDefault(IntlMessageFormat);\n\nfunction setTimeZoneInFormats(formats, timeZone) {\n  if (!formats) return formats;\n\n  // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n  // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n  return Object.keys(formats).reduce((acc, key) => {\n    acc[key] = {\n      timeZone,\n      ...formats[key]\n    };\n    return acc;\n  }, {});\n}\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(formats, timeZone) {\n  const formatsWithTimeZone = timeZone ? {\n    ...formats,\n    dateTime: setTimeZoneInFormats(formats.dateTime, timeZone)\n  } : formats;\n  const mfDateDefaults = IntlMessageFormat__default.default.formats.date;\n  const defaultDateFormats = timeZone ? setTimeZoneInFormats(mfDateDefaults, timeZone) : mfDateDefaults;\n  const mfTimeDefaults = IntlMessageFormat__default.default.formats.time;\n  const defaultTimeFormats = timeZone ? setTimeZoneInFormats(mfTimeDefaults, timeZone) : mfTimeDefaults;\n  return {\n    ...formatsWithTimeZone,\n    date: {\n      ...defaultDateFormats,\n      ...formatsWithTimeZone.dateTime\n    },\n    time: {\n      ...defaultTimeFormats,\n      ...formatsWithTimeZone.dateTime\n    }\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = initializeConfig.memoFn(function () {\n    return new IntlMessageFormat__default.default(arguments.length <= 0 ? undefined : arguments[0], arguments.length <= 1 ? undefined : arguments[1], arguments.length <= 2 ? undefined : arguments[2], {\n      formatters: intlFormatters,\n      ...(arguments.length <= 3 ? undefined : arguments[3])\n    });\n  }, cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = initializeConfig.joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(\"No messages available at `\".concat(namespace, \"`.\") );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(\"Could not resolve `\".concat(fullKey, \"` in messages for locale `\").concat(locale, \"`.\") );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  if (Object.keys(values).length === 0) return undefined;\n\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/React.isValidElement(result) ? /*#__PURE__*/React.cloneElement(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace) {\n  let onError = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : initializeConfig.defaultOnError;\n  try {\n    if (!messages) {\n      throw new Error(\"No messages were configured on the provider.\" );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(\"No messages for namespace `\".concat(namespace, \"` found.\") );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  if (values) return undefined;\n  const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n\n  // Placeholders can be in the message if there are default values,\n  // or if the user has forgotten to provide values. In the latter\n  // case we need to compile the message to receive an error.\n  const hasPlaceholders = /<|{/.test(unescapedMessage);\n  if (!hasPlaceholders) {\n    return unescapedMessage;\n  }\n  return undefined;\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl(_ref) {\n  let {\n    cache,\n    defaultTranslationValues,\n    formats: globalFormats,\n    formatters,\n    getMessageFallback = initializeConfig.defaultGetMessageFallback,\n    locale,\n    messagesOrError,\n    namespace,\n    onError,\n    timeZone\n  } = _ref;\n  const hasMessagesError = messagesOrError instanceof initializeConfig.IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new initializeConfig.IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = initializeConfig.IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages\");\n        }\n      } else {\n        code = initializeConfig.IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an object, but only strings are supported. Use a `.` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages\");\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat({\n        ...globalFormats,\n        ...formats\n      }, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? \" (\".concat(thrownError.originalMessage, \")\") : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      prepareTranslationValues({\n        ...defaultTranslationValues,\n        ...values\n      }));\n      if (formattedMessage == null) {\n        throw new Error(\"Unable to format `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : 'messages') );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/React.isValidElement(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, \"The message `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : 'messages', \" didn't resolve to a string. If you want to format rich text, use `t.rich` instead.\") );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n\n    // When only string chunks are provided to the parser, only\n    // strings should be returned here. Note that we need a runtime\n    // check for this since rich text values could be accidentally\n    // inherited from `defaultTranslationValues`.\n    if (typeof result !== 'string') {\n      const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\" );\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch (_unused) {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(_ref) {\n  let {\n    _cache: cache = initializeConfig.createCache(),\n    _formatters: formatters = initializeConfig.createIntlFormatters(cache),\n    formats,\n    locale,\n    now: globalNow,\n    onError = initializeConfig.defaultOnError,\n    timeZone: globalTimeZone\n  } = _ref;\n  function applyTimeZone(options) {\n    var _options;\n    if (!((_options = options) !== null && _options !== void 0 && _options.timeZone)) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `timeZone` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone\" ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats === null || typeFormats === void 0 ? void 0 : typeFormats[formatName];\n      if (!options) {\n        const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_FORMAT, \"Format `\".concat(formatName, \"` is not available. You can configure it on the provider or provide custom options.\") );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions);\n    } catch (_unused) {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(/** If a number is supplied, this is interpreted as a UTC timestamp. */\n  value,\n  /** If a time zone is supplied, the `value` is converted to that time zone.\n   * Otherwise the user time zone will be used. */\n  formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(/** If a number is supplied, this is interpreted as a UTC timestamp. */\n  start, /** If a number is supplied, this is interpreted as a UTC timestamp. */\n  end,\n  /** If a time zone is supplied, the values are converted to that time zone.\n   * Otherwise the user time zone will be used. */\n  formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    if (globalNow) {\n      return globalNow;\n    } else {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `now` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#now\" ));\n      return new Date();\n    }\n  }\n  function relativeTime(/** The date time that needs to be formatted. */\n  date, /** The reference point in time to which `date` will be formatted in relation to.  */\n  nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nexports.createBaseTranslator = createBaseTranslator;\nexports.createFormatter = createFormatter;\nexports.resolveNamespace = resolveNamespace;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar core = __webpack_require__(/*! ./core.js */ \"(ssr)/./node_modules/use-intl/dist/development/core.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-QqAaZwGD.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\");\nvar _IntlProvider = __webpack_require__(/*! ./_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\nvar react = __webpack_require__(/*! ./react.js */ \"(ssr)/./node_modules/use-intl/dist/development/react.js\");\nvar _useLocale = __webpack_require__(/*! ./_useLocale-BK3jOeaA.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n\n\n\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createTranslator = core.createTranslator;\nexports.createFormatter = createFormatter.createFormatter;\nexports.IntlProvider = _IntlProvider.IntlProvider;\nexports.useFormatter = react.useFormatter;\nexports.useMessages = react.useMessages;\nexports.useNow = react.useNow;\nexports.useTimeZone = react.useTimeZone;\nexports.useTranslations = react.useTranslations;\nexports.useLocale = _useLocale.useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdELHVCQUF1QixtQkFBTyxDQUFDLG1IQUFnQztBQUMvRCxXQUFXLG1CQUFPLENBQUMseUVBQVc7QUFDOUIsc0JBQXNCLG1CQUFPLENBQUMsaUhBQStCO0FBQzdELG9CQUFvQixtQkFBTyxDQUFDLDJGQUFvQjtBQUNoRCxZQUFZLG1CQUFPLENBQUMsMkVBQVk7QUFDaEMsaUJBQWlCLG1CQUFPLENBQUMsdUdBQTBCO0FBQ25ELG1CQUFPLENBQUMsd0ZBQXdCO0FBQ2hDLG1CQUFPLENBQUMsZ0ZBQW9CO0FBQzVCLG1CQUFPLENBQUMsd0dBQU87QUFDZixtQkFBTyxDQUFDLHlHQUEyQjs7OztBQUluQyxpQkFBaUI7QUFDakIscUJBQXFCO0FBQ3JCLG9CQUFvQjtBQUNwQiw2QkFBNkI7QUFDN0Isd0JBQXdCO0FBQ3hCLHdCQUF3QjtBQUN4Qix1QkFBdUI7QUFDdkIsb0JBQW9CO0FBQ3BCLG9CQUFvQjtBQUNwQixtQkFBbUI7QUFDbkIsY0FBYztBQUNkLG1CQUFtQjtBQUNuQix1QkFBdUI7QUFDdkIsaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50bDItY29tcGFueXByb2ZpbGUtdjAyLy4vbm9kZV9tb2R1bGVzL3VzZS1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvaW5kZXguanM/YTM4NCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbnZhciBpbml0aWFsaXplQ29uZmlnID0gcmVxdWlyZSgnLi9pbml0aWFsaXplQ29uZmlnLUJoZk1TSFA3LmpzJyk7XG52YXIgY29yZSA9IHJlcXVpcmUoJy4vY29yZS5qcycpO1xudmFyIGNyZWF0ZUZvcm1hdHRlciA9IHJlcXVpcmUoJy4vY3JlYXRlRm9ybWF0dGVyLVFxQWFad0dELmpzJyk7XG52YXIgX0ludGxQcm92aWRlciA9IHJlcXVpcmUoJy4vX0ludGxQcm92aWRlci5qcycpO1xudmFyIHJlYWN0ID0gcmVxdWlyZSgnLi9yZWFjdC5qcycpO1xudmFyIF91c2VMb2NhbGUgPSByZXF1aXJlKCcuL191c2VMb2NhbGUtQkszak9lYUEuanMnKTtcbnJlcXVpcmUoJ0Bmb3JtYXRqcy9mYXN0LW1lbW9pemUnKTtcbnJlcXVpcmUoJ2ludGwtbWVzc2FnZWZvcm1hdCcpO1xucmVxdWlyZSgncmVhY3QnKTtcbnJlcXVpcmUoJy4vSW50bENvbnRleHQtQktmc256QnguanMnKTtcblxuXG5cbmV4cG9ydHMuSW50bEVycm9yID0gaW5pdGlhbGl6ZUNvbmZpZy5JbnRsRXJyb3I7XG5leHBvcnRzLkludGxFcnJvckNvZGUgPSBpbml0aWFsaXplQ29uZmlnLkludGxFcnJvckNvZGU7XG5leHBvcnRzLl9jcmVhdGVDYWNoZSA9IGluaXRpYWxpemVDb25maWcuY3JlYXRlQ2FjaGU7XG5leHBvcnRzLl9jcmVhdGVJbnRsRm9ybWF0dGVycyA9IGluaXRpYWxpemVDb25maWcuY3JlYXRlSW50bEZvcm1hdHRlcnM7XG5leHBvcnRzLmluaXRpYWxpemVDb25maWcgPSBpbml0aWFsaXplQ29uZmlnLmluaXRpYWxpemVDb25maWc7XG5leHBvcnRzLmNyZWF0ZVRyYW5zbGF0b3IgPSBjb3JlLmNyZWF0ZVRyYW5zbGF0b3I7XG5leHBvcnRzLmNyZWF0ZUZvcm1hdHRlciA9IGNyZWF0ZUZvcm1hdHRlci5jcmVhdGVGb3JtYXR0ZXI7XG5leHBvcnRzLkludGxQcm92aWRlciA9IF9JbnRsUHJvdmlkZXIuSW50bFByb3ZpZGVyO1xuZXhwb3J0cy51c2VGb3JtYXR0ZXIgPSByZWFjdC51c2VGb3JtYXR0ZXI7XG5leHBvcnRzLnVzZU1lc3NhZ2VzID0gcmVhY3QudXNlTWVzc2FnZXM7XG5leHBvcnRzLnVzZU5vdyA9IHJlYWN0LnVzZU5vdztcbmV4cG9ydHMudXNlVGltZVpvbmUgPSByZWFjdC51c2VUaW1lWm9uZTtcbmV4cG9ydHMudXNlVHJhbnNsYXRpb25zID0gcmVhY3QudXNlVHJhbnNsYXRpb25zO1xuZXhwb3J0cy51c2VMb2NhbGUgPSBfdXNlTG9jYWxlLnVzZUxvY2FsZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar fastMemoize = __webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\n\nlet IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}({});\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    _defineProperty(this, \"code\", void 0);\n    _defineProperty(this, \"originalMessage\", void 0);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nfunction joinPath() {\n  for (var _len = arguments.length, parts = new Array(_len), _key = 0; _key < _len; _key++) {\n    parts[_key] = arguments[_key];\n  }\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return fastMemoize.memoize(fn, {\n    cache: createMemoCache(cache),\n    strategy: fastMemoize.strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return new ConstructorFn(...args);\n  }, cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(_ref => {\n    let [key, messageOrMessages] = _ref;\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += \" (at \".concat(parentPath, \")\");\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, \"Namespace keys can not contain the character \\\".\\\" as this is used to express nesting. Please remove it or replace it with another character.\\n\\nInvalid \".concat(invalidKeyLabels.length === 1 ? 'key' : 'keys', \": \").concat(invalidKeyLabels.join(', '), \"\\n\\nIf you're migrating from a flat structure, you can convert your messages as follows:\\n\\nimport {set} from \\\"lodash\\\";\\n\\nconst input = {\\n  \\\"one.one\\\": \\\"1.1\\\",\\n  \\\"one.two\\\": \\\"1.2\\\",\\n  \\\"two.one.one\\\": \\\"2.1.1\\\"\\n};\\n\\nconst output = Object.entries(input).reduce(\\n  (acc, [key, value]) => set(acc, key, value),\\n  {}\\n);\\n\\n// Output:\\n//\\n// {\\n//   \\\"one\\\": {\\n//     \\\"one\\\": \\\"1.1\\\",\\n//     \\\"two\\\": \\\"1.2\\\"\\n//   },\\n//   \\\"two\\\": {\\n//     \\\"one\\\": {\\n//       \\\"one\\\": \\\"2.1.1\\\"\\n//     }\\n//   }\\n// }\\n\") ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig(_ref) {\n  let {\n    getMessageFallback,\n    messages,\n    onError,\n    ...rest\n  } = _ref;\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    messages,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\nexports.IntlError = IntlError;\nexports.IntlErrorCode = IntlErrorCode;\nexports.createCache = createCache;\nexports.createIntlFormatters = createIntlFormatters;\nexports.defaultGetMessageFallback = defaultGetMessageFallback;\nexports.defaultOnError = defaultOnError;\nexports.initializeConfig = initializeConfig;\nexports.joinPath = joinPath;\nexports.memoFn = memoFn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/react.js":
/*!*********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/react.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _IntlProvider = __webpack_require__(/*! ./_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\nvar _useLocale = __webpack_require__(/*! ./_useLocale-BK3jOeaA.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-BK3jOeaA.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-QqAaZwGD.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nlet hasWarnedForMissingTimezone = false;\nconst isServer = typeof window === 'undefined';\nfunction useTranslationsImpl(allMessagesPrefixed, namespacePrefixed, namespacePrefix) {\n  const {\n    cache,\n    defaultTranslationValues,\n    formats: globalFormats,\n    formatters,\n    getMessageFallback,\n    locale,\n    onError,\n    timeZone\n  } = _useLocale.useIntlContext();\n\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the hook invocation.\n  const allMessages = allMessagesPrefixed[namespacePrefix];\n  const namespace = createFormatter.resolveNamespace(namespacePrefixed, namespacePrefix);\n  if (!timeZone && !hasWarnedForMissingTimezone && isServer) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    hasWarnedForMissingTimezone = true;\n    onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"There is no `timeZone` configured, this can lead to markup mismatches caused by environment differences. Consider adding a global default: https://next-intl.dev/docs/configuration#time-zone\" ));\n  }\n  const translate = React.useMemo(() => createFormatter.createBaseTranslator({\n    cache,\n    formatters,\n    getMessageFallback,\n    messages: allMessages,\n    defaultTranslationValues,\n    namespace,\n    onError,\n    formats: globalFormats,\n    locale,\n    timeZone\n  }), [cache, formatters, getMessageFallback, allMessages, defaultTranslationValues, namespace, onError, globalFormats, locale, timeZone]);\n  return translate;\n}\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction useTranslations(namespace) {\n  const context = _useLocale.useIntlContext();\n  const messages = context.messages;\n\n  // We have to wrap the actual hook so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  return useTranslationsImpl({\n    '!': messages\n  },\n  // @ts-expect-error\n  namespace ? \"!.\".concat(namespace) : '!', '!');\n}\n\nfunction getNow() {\n  return new Date();\n}\n\n/**\n * Reading the current date via `new Date()` in components should be avoided, as\n * it causes components to be impure and can lead to flaky tests. Instead, this\n * hook can be used.\n *\n * By default, it returns the time when the component mounts. If `updateInterval`\n * is specified, the value will be updated based on the interval.\n *\n * You can however also return a static value from this hook, if you\n * configure the `now` parameter on the context provider. Note however,\n * that if `updateInterval` is configured in this case, the component\n * will initialize with the global value, but will afterwards update\n * continuously based on the interval.\n *\n * For unit tests, this can be mocked to a constant value. For end-to-end\n * testing, an environment parameter can be passed to the `now` parameter\n * of the provider to mock this to a static value.\n */\nfunction useNow(options) {\n  const updateInterval = options === null || options === void 0 ? void 0 : options.updateInterval;\n  const {\n    now: globalNow\n  } = _useLocale.useIntlContext();\n  const [now, setNow] = React.useState(globalNow || getNow());\n  React.useEffect(() => {\n    if (!updateInterval) return;\n    const intervalId = setInterval(() => {\n      setNow(getNow());\n    }, updateInterval);\n    return () => {\n      clearInterval(intervalId);\n    };\n  }, [globalNow, updateInterval]);\n  return updateInterval == null && globalNow ? globalNow : now;\n}\n\nfunction useTimeZone() {\n  return _useLocale.useIntlContext().timeZone;\n}\n\nfunction useMessages() {\n  const context = _useLocale.useIntlContext();\n  if (!context.messages) {\n    throw new Error('No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages' );\n  }\n  return context.messages;\n}\n\nfunction useFormatter() {\n  const {\n    formats,\n    formatters,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone\n  } = _useLocale.useIntlContext();\n  return React.useMemo(() => createFormatter.createFormatter({\n    formats,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone,\n    _formatters: formatters\n  }), [formats, formatters, globalNow, locale, onError, timeZone]);\n}\n\nexports.IntlProvider = _IntlProvider.IntlProvider;\nexports.useLocale = _useLocale.useLocale;\nexports.useFormatter = useFormatter;\nexports.useMessages = useMessages;\nexports.useNow = useNow;\nexports.useTimeZone = useTimeZone;\nexports.useTranslations = useTranslations;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/react.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/index.js":
/*!*********************************************!*\
  !*** ./node_modules/use-intl/dist/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./development/index.js */ \"(ssr)/./node_modules/use-intl/dist/development/index.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixJQUFJLEtBQXFDLEVBQUUsRUFFMUMsQ0FBQztBQUNGLEVBQUUsNkhBQWtEO0FBQ3BEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50bDItY29tcGFueXByb2ZpbGUtdjAyLy4vbm9kZV9tb2R1bGVzL3VzZS1pbnRsL2Rpc3QvaW5kZXguanM/NGIwYSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9wcm9kdWN0aW9uL2luZGV4LmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGV2ZWxvcG1lbnQvaW5kZXguanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/core.js":
/*!********************************************!*\
  !*** ./node_modules/use-intl/dist/core.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./development/core.js */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9jb3JlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSwySEFBaUQ7QUFDbkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9jb3JlLmpzPzUzZWQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vcHJvZHVjdGlvbi9jb3JlLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGV2ZWxvcG1lbnQvY29yZS5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/core.js":
/*!********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/core.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-QqAaZwGD.js */ \"(rsc)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(rsc)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n\nfunction createTranslatorImpl(_ref, namespacePrefix) {\n  let {\n    messages,\n    namespace,\n    ...rest\n  } = _ref;\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the function invocation.\n  messages = messages[namespacePrefix];\n  namespace = createFormatter.resolveNamespace(namespace, namespacePrefix);\n  return createFormatter.createBaseTranslator({\n    ...rest,\n    messages,\n    namespace\n  });\n}\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction createTranslator(_ref) {\n  let {\n    _cache = initializeConfig.createCache(),\n    _formatters = initializeConfig.createIntlFormatters(_cache),\n    getMessageFallback = initializeConfig.defaultGetMessageFallback,\n    messages,\n    namespace,\n    onError = initializeConfig.defaultOnError,\n    ...rest\n  } = _ref;\n  // We have to wrap the actual function so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  return createTranslatorImpl({\n    ...rest,\n    onError,\n    cache: _cache,\n    formatters: _formatters,\n    getMessageFallback,\n    // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n    messages: {\n      '!': messages\n    },\n    namespace: namespace ? \"!.\".concat(namespace) : '!'\n  }, '!');\n}\n\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createFormatter = createFormatter.createFormatter;\nexports.createTranslator = createTranslator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js":
/*!****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar IntlMessageFormat = __webpack_require__(/*! intl-messageformat */ \"(rsc)/./node_modules/intl-messageformat/lib/index.js\");\nvar React = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar IntlMessageFormat__default = /*#__PURE__*/_interopDefault(IntlMessageFormat);\n\nfunction setTimeZoneInFormats(formats, timeZone) {\n  if (!formats) return formats;\n\n  // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n  // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n  return Object.keys(formats).reduce((acc, key) => {\n    acc[key] = {\n      timeZone,\n      ...formats[key]\n    };\n    return acc;\n  }, {});\n}\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(formats, timeZone) {\n  const formatsWithTimeZone = timeZone ? {\n    ...formats,\n    dateTime: setTimeZoneInFormats(formats.dateTime, timeZone)\n  } : formats;\n  const mfDateDefaults = IntlMessageFormat__default.default.formats.date;\n  const defaultDateFormats = timeZone ? setTimeZoneInFormats(mfDateDefaults, timeZone) : mfDateDefaults;\n  const mfTimeDefaults = IntlMessageFormat__default.default.formats.time;\n  const defaultTimeFormats = timeZone ? setTimeZoneInFormats(mfTimeDefaults, timeZone) : mfTimeDefaults;\n  return {\n    ...formatsWithTimeZone,\n    date: {\n      ...defaultDateFormats,\n      ...formatsWithTimeZone.dateTime\n    },\n    time: {\n      ...defaultTimeFormats,\n      ...formatsWithTimeZone.dateTime\n    }\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = initializeConfig.memoFn(function () {\n    return new IntlMessageFormat__default.default(arguments.length <= 0 ? undefined : arguments[0], arguments.length <= 1 ? undefined : arguments[1], arguments.length <= 2 ? undefined : arguments[2], {\n      formatters: intlFormatters,\n      ...(arguments.length <= 3 ? undefined : arguments[3])\n    });\n  }, cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = initializeConfig.joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(\"No messages available at `\".concat(namespace, \"`.\") );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(\"Could not resolve `\".concat(fullKey, \"` in messages for locale `\").concat(locale, \"`.\") );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  if (Object.keys(values).length === 0) return undefined;\n\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/React.isValidElement(result) ? /*#__PURE__*/React.cloneElement(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace) {\n  let onError = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : initializeConfig.defaultOnError;\n  try {\n    if (!messages) {\n      throw new Error(\"No messages were configured on the provider.\" );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(\"No messages for namespace `\".concat(namespace, \"` found.\") );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  if (values) return undefined;\n  const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n\n  // Placeholders can be in the message if there are default values,\n  // or if the user has forgotten to provide values. In the latter\n  // case we need to compile the message to receive an error.\n  const hasPlaceholders = /<|{/.test(unescapedMessage);\n  if (!hasPlaceholders) {\n    return unescapedMessage;\n  }\n  return undefined;\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl(_ref) {\n  let {\n    cache,\n    defaultTranslationValues,\n    formats: globalFormats,\n    formatters,\n    getMessageFallback = initializeConfig.defaultGetMessageFallback,\n    locale,\n    messagesOrError,\n    namespace,\n    onError,\n    timeZone\n  } = _ref;\n  const hasMessagesError = messagesOrError instanceof initializeConfig.IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new initializeConfig.IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = initializeConfig.IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages\");\n        }\n      } else {\n        code = initializeConfig.IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an object, but only strings are supported. Use a `.` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages\");\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat({\n        ...globalFormats,\n        ...formats\n      }, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? \" (\".concat(thrownError.originalMessage, \")\") : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      prepareTranslationValues({\n        ...defaultTranslationValues,\n        ...values\n      }));\n      if (formattedMessage == null) {\n        throw new Error(\"Unable to format `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : 'messages') );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/React.isValidElement(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, \"The message `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : 'messages', \" didn't resolve to a string. If you want to format rich text, use `t.rich` instead.\") );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n\n    // When only string chunks are provided to the parser, only\n    // strings should be returned here. Note that we need a runtime\n    // check for this since rich text values could be accidentally\n    // inherited from `defaultTranslationValues`.\n    if (typeof result !== 'string') {\n      const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\" );\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch (_unused) {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(_ref) {\n  let {\n    _cache: cache = initializeConfig.createCache(),\n    _formatters: formatters = initializeConfig.createIntlFormatters(cache),\n    formats,\n    locale,\n    now: globalNow,\n    onError = initializeConfig.defaultOnError,\n    timeZone: globalTimeZone\n  } = _ref;\n  function applyTimeZone(options) {\n    var _options;\n    if (!((_options = options) !== null && _options !== void 0 && _options.timeZone)) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `timeZone` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone\" ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats === null || typeFormats === void 0 ? void 0 : typeFormats[formatName];\n      if (!options) {\n        const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_FORMAT, \"Format `\".concat(formatName, \"` is not available. You can configure it on the provider or provide custom options.\") );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions);\n    } catch (_unused) {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(/** If a number is supplied, this is interpreted as a UTC timestamp. */\n  value,\n  /** If a time zone is supplied, the `value` is converted to that time zone.\n   * Otherwise the user time zone will be used. */\n  formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(/** If a number is supplied, this is interpreted as a UTC timestamp. */\n  start, /** If a number is supplied, this is interpreted as a UTC timestamp. */\n  end,\n  /** If a time zone is supplied, the values are converted to that time zone.\n   * Otherwise the user time zone will be used. */\n  formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    if (globalNow) {\n      return globalNow;\n    } else {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `now` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#now\" ));\n      return new Date();\n    }\n  }\n  function relativeTime(/** The date time that needs to be formatted. */\n  date, /** The reference point in time to which `date` will be formatted in relation to.  */\n  nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nexports.createBaseTranslator = createBaseTranslator;\nexports.createFormatter = createFormatter;\nexports.resolveNamespace = resolveNamespace;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/createFormatter-QqAaZwGD.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar fastMemoize = __webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\n\nlet IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}({});\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    _defineProperty(this, \"code\", void 0);\n    _defineProperty(this, \"originalMessage\", void 0);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nfunction joinPath() {\n  for (var _len = arguments.length, parts = new Array(_len), _key = 0; _key < _len; _key++) {\n    parts[_key] = arguments[_key];\n  }\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return fastMemoize.memoize(fn, {\n    cache: createMemoCache(cache),\n    strategy: fastMemoize.strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return new ConstructorFn(...args);\n  }, cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(_ref => {\n    let [key, messageOrMessages] = _ref;\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += \" (at \".concat(parentPath, \")\");\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, \"Namespace keys can not contain the character \\\".\\\" as this is used to express nesting. Please remove it or replace it with another character.\\n\\nInvalid \".concat(invalidKeyLabels.length === 1 ? 'key' : 'keys', \": \").concat(invalidKeyLabels.join(', '), \"\\n\\nIf you're migrating from a flat structure, you can convert your messages as follows:\\n\\nimport {set} from \\\"lodash\\\";\\n\\nconst input = {\\n  \\\"one.one\\\": \\\"1.1\\\",\\n  \\\"one.two\\\": \\\"1.2\\\",\\n  \\\"two.one.one\\\": \\\"2.1.1\\\"\\n};\\n\\nconst output = Object.entries(input).reduce(\\n  (acc, [key, value]) => set(acc, key, value),\\n  {}\\n);\\n\\n// Output:\\n//\\n// {\\n//   \\\"one\\\": {\\n//     \\\"one\\\": \\\"1.1\\\",\\n//     \\\"two\\\": \\\"1.2\\\"\\n//   },\\n//   \\\"two\\\": {\\n//     \\\"one\\\": {\\n//       \\\"one\\\": \\\"2.1.1\\\"\\n//     }\\n//   }\\n// }\\n\") ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig(_ref) {\n  let {\n    getMessageFallback,\n    messages,\n    onError,\n    ...rest\n  } = _ref;\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    messages,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\nexports.IntlError = IntlError;\nexports.IntlErrorCode = IntlErrorCode;\nexports.createCache = createCache;\nexports.createIntlFormatters = createIntlFormatters;\nexports.defaultGetMessageFallback = defaultGetMessageFallback;\nexports.defaultOnError = defaultOnError;\nexports.initializeConfig = initializeConfig;\nexports.joinPath = joinPath;\nexports.memoFn = memoFn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\n");

/***/ })

};
;