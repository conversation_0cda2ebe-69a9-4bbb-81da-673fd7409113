"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/yet-another-react-lightbox";
exports.ids = ["vendor-chunks/yet-another-react-lightbox"];
exports.modules = {

/***/ "(ssr)/./node_modules/yet-another-react-lightbox/dist/styles.css":
/*!*****************************************************************!*\
  !*** ./node_modules/yet-another-react-lightbox/dist/styles.css ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"075a87508db5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveWV0LWFub3RoZXItcmVhY3QtbGlnaHRib3gvZGlzdC9zdHlsZXMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50bDItY29tcGFueXByb2ZpbGUtdjAyLy4vbm9kZV9tb2R1bGVzL3lldC1hbm90aGVyLXJlYWN0LWxpZ2h0Ym94L2Rpc3Qvc3R5bGVzLmNzcz9iMDUyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDc1YTg3NTA4ZGI1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/yet-another-react-lightbox/dist/styles.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/yet-another-react-lightbox/dist/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/yet-another-react-lightbox/dist/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_CLOSE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE),\n/* harmony export */   ACTION_NEXT: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT),\n/* harmony export */   ACTION_PREV: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV),\n/* harmony export */   ACTION_SWIPE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_SWIPE),\n/* harmony export */   ACTIVE_SLIDE_COMPLETE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTIVE_SLIDE_COMPLETE),\n/* harmony export */   ACTIVE_SLIDE_ERROR: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTIVE_SLIDE_ERROR),\n/* harmony export */   ACTIVE_SLIDE_LOADING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTIVE_SLIDE_LOADING),\n/* harmony export */   ACTIVE_SLIDE_PLAYING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTIVE_SLIDE_PLAYING),\n/* harmony export */   CLASS_FLEX_CENTER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FLEX_CENTER),\n/* harmony export */   CLASS_FULLSIZE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FULLSIZE),\n/* harmony export */   CLASS_NO_SCROLL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL),\n/* harmony export */   CLASS_NO_SCROLL_PADDING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL_PADDING),\n/* harmony export */   CLASS_SLIDE_WRAPPER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER),\n/* harmony export */   CLASS_SLIDE_WRAPPER_INTERACTIVE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER_INTERACTIVE),\n/* harmony export */   Carousel: () => (/* binding */ Carousel),\n/* harmony export */   CarouselModule: () => (/* binding */ CarouselModule),\n/* harmony export */   CloseIcon: () => (/* binding */ CloseIcon),\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   ControllerContext: () => (/* binding */ ControllerContext),\n/* harmony export */   ControllerModule: () => (/* binding */ ControllerModule),\n/* harmony export */   DocumentContext: () => (/* binding */ DocumentContext),\n/* harmony export */   DocumentContextProvider: () => (/* binding */ DocumentContextProvider),\n/* harmony export */   ELEMENT_BUTTON: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_BUTTON),\n/* harmony export */   ELEMENT_ICON: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_ICON),\n/* harmony export */   EVENT_ON_KEY_DOWN: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_DOWN),\n/* harmony export */   EVENT_ON_KEY_UP: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_UP),\n/* harmony export */   EVENT_ON_POINTER_CANCEL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_CANCEL),\n/* harmony export */   EVENT_ON_POINTER_DOWN: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_DOWN),\n/* harmony export */   EVENT_ON_POINTER_LEAVE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_LEAVE),\n/* harmony export */   EVENT_ON_POINTER_MOVE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_MOVE),\n/* harmony export */   EVENT_ON_POINTER_UP: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_UP),\n/* harmony export */   EVENT_ON_WHEEL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_WHEEL),\n/* harmony export */   ErrorIcon: () => (/* binding */ ErrorIcon),\n/* harmony export */   EventsContext: () => (/* binding */ EventsContext),\n/* harmony export */   EventsProvider: () => (/* binding */ EventsProvider),\n/* harmony export */   IMAGE_FIT_CONTAIN: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_CONTAIN),\n/* harmony export */   IMAGE_FIT_COVER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_COVER),\n/* harmony export */   IconButton: () => (/* binding */ IconButton),\n/* harmony export */   ImageSlide: () => (/* binding */ ImageSlide),\n/* harmony export */   Lightbox: () => (/* binding */ Lightbox),\n/* harmony export */   LightboxDefaultProps: () => (/* binding */ LightboxDefaultProps),\n/* harmony export */   LightboxDispatchContext: () => (/* binding */ LightboxDispatchContext),\n/* harmony export */   LightboxPropsContext: () => (/* binding */ LightboxPropsContext),\n/* harmony export */   LightboxPropsProvider: () => (/* binding */ LightboxPropsProvider),\n/* harmony export */   LightboxRoot: () => (/* binding */ LightboxRoot),\n/* harmony export */   LightboxStateContext: () => (/* binding */ LightboxStateContext),\n/* harmony export */   LightboxStateProvider: () => (/* binding */ LightboxStateProvider),\n/* harmony export */   LoadingIcon: () => (/* binding */ LoadingIcon),\n/* harmony export */   MODULE_CAROUSEL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CAROUSEL),\n/* harmony export */   MODULE_CONTROLLER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CONTROLLER),\n/* harmony export */   MODULE_NAVIGATION: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_NAVIGATION),\n/* harmony export */   MODULE_NO_SCROLL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_NO_SCROLL),\n/* harmony export */   MODULE_PORTAL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_PORTAL),\n/* harmony export */   MODULE_ROOT: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_ROOT),\n/* harmony export */   MODULE_TOOLBAR: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_TOOLBAR),\n/* harmony export */   Navigation: () => (/* binding */ Navigation),\n/* harmony export */   NavigationButton: () => (/* binding */ NavigationButton),\n/* harmony export */   NavigationModule: () => (/* binding */ NavigationModule),\n/* harmony export */   NextIcon: () => (/* binding */ NextIcon),\n/* harmony export */   NoScroll: () => (/* binding */ NoScroll),\n/* harmony export */   NoScrollModule: () => (/* binding */ NoScrollModule),\n/* harmony export */   PLUGIN_CAPTIONS: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_CAPTIONS),\n/* harmony export */   PLUGIN_COUNTER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_COUNTER),\n/* harmony export */   PLUGIN_DOWNLOAD: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_DOWNLOAD),\n/* harmony export */   PLUGIN_FULLSCREEN: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_FULLSCREEN),\n/* harmony export */   PLUGIN_INLINE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_INLINE),\n/* harmony export */   PLUGIN_SHARE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_SHARE),\n/* harmony export */   PLUGIN_SLIDESHOW: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_SLIDESHOW),\n/* harmony export */   PLUGIN_THUMBNAILS: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_THUMBNAILS),\n/* harmony export */   PLUGIN_ZOOM: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_ZOOM),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   PortalModule: () => (/* binding */ PortalModule),\n/* harmony export */   PreviousIcon: () => (/* binding */ PreviousIcon),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   RootModule: () => (/* binding */ RootModule),\n/* harmony export */   SLIDE_STATUS_COMPLETE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_COMPLETE),\n/* harmony export */   SLIDE_STATUS_ERROR: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_ERROR),\n/* harmony export */   SLIDE_STATUS_LOADING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_LOADING),\n/* harmony export */   SLIDE_STATUS_PLACEHOLDER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_PLACEHOLDER),\n/* harmony export */   SLIDE_STATUS_PLAYING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_PLAYING),\n/* harmony export */   SwipeState: () => (/* binding */ SwipeState),\n/* harmony export */   TimeoutsContext: () => (/* binding */ TimeoutsContext),\n/* harmony export */   TimeoutsProvider: () => (/* binding */ TimeoutsProvider),\n/* harmony export */   Toolbar: () => (/* binding */ Toolbar),\n/* harmony export */   ToolbarModule: () => (/* binding */ ToolbarModule),\n/* harmony export */   UNKNOWN_ACTION_TYPE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.UNKNOWN_ACTION_TYPE),\n/* harmony export */   VK_ARROW_LEFT: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ARROW_LEFT),\n/* harmony export */   VK_ARROW_RIGHT: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ARROW_RIGHT),\n/* harmony export */   VK_ESCAPE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ESCAPE),\n/* harmony export */   activeSlideStatus: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.activeSlideStatus),\n/* harmony export */   addToolbarButton: () => (/* binding */ addToolbarButton),\n/* harmony export */   calculatePreload: () => (/* binding */ calculatePreload),\n/* harmony export */   cleanup: () => (/* binding */ cleanup),\n/* harmony export */   clsx: () => (/* binding */ clsx),\n/* harmony export */   composePrefix: () => (/* binding */ composePrefix),\n/* harmony export */   computeSlideRect: () => (/* binding */ computeSlideRect),\n/* harmony export */   createIcon: () => (/* binding */ createIcon),\n/* harmony export */   createIconDisabled: () => (/* binding */ createIconDisabled),\n/* harmony export */   createModule: () => (/* binding */ createModule),\n/* harmony export */   createNode: () => (/* binding */ createNode),\n/* harmony export */   cssClass: () => (/* binding */ cssClass),\n/* harmony export */   cssVar: () => (/* binding */ cssVar),\n/* harmony export */   \"default\": () => (/* binding */ Lightbox),\n/* harmony export */   devicePixelRatio: () => (/* binding */ devicePixelRatio),\n/* harmony export */   getSlide: () => (/* binding */ getSlide),\n/* harmony export */   getSlideIfPresent: () => (/* binding */ getSlideIfPresent),\n/* harmony export */   getSlideIndex: () => (/* binding */ getSlideIndex),\n/* harmony export */   getSlideKey: () => (/* binding */ getSlideKey),\n/* harmony export */   hasSlides: () => (/* binding */ hasSlides),\n/* harmony export */   hasWindow: () => (/* binding */ hasWindow),\n/* harmony export */   isImageFitCover: () => (/* binding */ isImageFitCover),\n/* harmony export */   isImageSlide: () => (/* binding */ isImageSlide),\n/* harmony export */   label: () => (/* binding */ label),\n/* harmony export */   makeComposePrefix: () => (/* binding */ makeComposePrefix),\n/* harmony export */   makeInertWhen: () => (/* binding */ makeInertWhen),\n/* harmony export */   makeUseContext: () => (/* binding */ makeUseContext),\n/* harmony export */   parseInt: () => (/* binding */ parseInt),\n/* harmony export */   parseLengthPercentage: () => (/* binding */ parseLengthPercentage),\n/* harmony export */   round: () => (/* binding */ round),\n/* harmony export */   setRef: () => (/* binding */ setRef),\n/* harmony export */   stopNavigationEventsPropagation: () => (/* binding */ stopNavigationEventsPropagation),\n/* harmony export */   useAnimation: () => (/* binding */ useAnimation),\n/* harmony export */   useContainerRect: () => (/* binding */ useContainerRect),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useDelay: () => (/* binding */ useDelay),\n/* harmony export */   useDocumentContext: () => (/* binding */ useDocumentContext),\n/* harmony export */   useEventCallback: () => (/* binding */ useEventCallback),\n/* harmony export */   useEvents: () => (/* binding */ useEvents),\n/* harmony export */   useForkRef: () => (/* binding */ useForkRef),\n/* harmony export */   useKeyboardNavigation: () => (/* binding */ useKeyboardNavigation),\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect),\n/* harmony export */   useLightboxDispatch: () => (/* binding */ useLightboxDispatch),\n/* harmony export */   useLightboxProps: () => (/* binding */ useLightboxProps),\n/* harmony export */   useLightboxState: () => (/* binding */ useLightboxState),\n/* harmony export */   useLoseFocus: () => (/* binding */ useLoseFocus),\n/* harmony export */   useMotionPreference: () => (/* binding */ useMotionPreference),\n/* harmony export */   useNavigationState: () => (/* binding */ useNavigationState),\n/* harmony export */   usePointerEvents: () => (/* binding */ usePointerEvents),\n/* harmony export */   usePointerSwipe: () => (/* binding */ usePointerSwipe),\n/* harmony export */   usePreventWheelDefaults: () => (/* binding */ usePreventWheelDefaults),\n/* harmony export */   useRTL: () => (/* binding */ useRTL),\n/* harmony export */   useSensors: () => (/* binding */ useSensors),\n/* harmony export */   useThrottle: () => (/* binding */ useThrottle),\n/* harmony export */   useTimeouts: () => (/* binding */ useTimeouts),\n/* harmony export */   useWheelSwipe: () => (/* binding */ useWheelSwipe),\n/* harmony export */   withPlugins: () => (/* binding */ withPlugins)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/yet-another-react-lightbox/dist/types.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ ACTIVE_SLIDE_COMPLETE,ACTIVE_SLIDE_ERROR,ACTIVE_SLIDE_LOADING,ACTIVE_SLIDE_PLAYING,CLASS_FULLSIZE,CLASS_SLIDE_WRAPPER_INTERACTIVE,PLUGIN_CAPTIONS,PLUGIN_COUNTER,PLUGIN_DOWNLOAD,PLUGIN_FULLSCREEN,PLUGIN_INLINE,PLUGIN_SHARE,PLUGIN_SLIDESHOW,PLUGIN_THUMBNAILS,PLUGIN_ZOOM,SLIDE_STATUS_PLAYING,ACTION_CLOSE,ACTION_NEXT,ACTION_PREV,ACTION_SWIPE,CLASS_FLEX_CENTER,CLASS_NO_SCROLL,CLASS_NO_SCROLL_PADDING,CLASS_SLIDE_WRAPPER,Carousel,CarouselModule,CloseIcon,Controller,ControllerContext,ControllerModule,DocumentContext,DocumentContextProvider,ELEMENT_BUTTON,ELEMENT_ICON,EVENT_ON_KEY_DOWN,EVENT_ON_KEY_UP,EVENT_ON_POINTER_CANCEL,EVENT_ON_POINTER_DOWN,EVENT_ON_POINTER_LEAVE,EVENT_ON_POINTER_MOVE,EVENT_ON_POINTER_UP,EVENT_ON_WHEEL,ErrorIcon,EventsContext,EventsProvider,IMAGE_FIT_CONTAIN,IMAGE_FIT_COVER,IconButton,ImageSlide,Lightbox,LightboxDefaultProps,LightboxDispatchContext,LightboxPropsContext,LightboxPropsProvider,LightboxRoot,LightboxStateContext,LightboxStateProvider,LoadingIcon,MODULE_CAROUSEL,MODULE_CONTROLLER,MODULE_NAVIGATION,MODULE_NO_SCROLL,MODULE_PORTAL,MODULE_ROOT,MODULE_TOOLBAR,Navigation,NavigationButton,NavigationModule,NextIcon,NoScroll,NoScrollModule,Portal,PortalModule,PreviousIcon,Root,RootModule,SLIDE_STATUS_COMPLETE,SLIDE_STATUS_ERROR,SLIDE_STATUS_LOADING,SLIDE_STATUS_PLACEHOLDER,SwipeState,TimeoutsContext,TimeoutsProvider,Toolbar,ToolbarModule,UNKNOWN_ACTION_TYPE,VK_ARROW_LEFT,VK_ARROW_RIGHT,VK_ESCAPE,activeSlideStatus,addToolbarButton,calculatePreload,cleanup,clsx,composePrefix,computeSlideRect,createIcon,createIconDisabled,createModule,createNode,cssClass,cssVar,default,devicePixelRatio,getSlide,getSlideIfPresent,getSlideIndex,getSlideKey,hasSlides,hasWindow,isImageFitCover,isImageSlide,label,makeComposePrefix,makeInertWhen,makeUseContext,parseInt,parseLengthPercentage,round,setRef,stopNavigationEventsPropagation,useAnimation,useContainerRect,useController,useDelay,useDocumentContext,useEventCallback,useEvents,useForkRef,useKeyboardNavigation,useLayoutEffect,useLightboxDispatch,useLightboxProps,useLightboxState,useLoseFocus,useMotionPreference,useNavigationState,usePointerEvents,usePointerSwipe,usePreventWheelDefaults,useRTL,useSensors,useThrottle,useTimeouts,useWheelSwipe,withPlugins auto */ \n\n\n\nconst cssPrefix$3 = \"yarl__\";\nfunction clsx(...classes) {\n    return [\n        ...classes\n    ].filter(Boolean).join(\" \");\n}\nfunction cssClass(name) {\n    return `${cssPrefix$3}${name}`;\n}\nfunction cssVar(name) {\n    return `--${cssPrefix$3}${name}`;\n}\nfunction composePrefix(base, prefix) {\n    return `${base}${prefix ? `_${prefix}` : \"\"}`;\n}\nfunction makeComposePrefix(base) {\n    return (prefix)=>composePrefix(base, prefix);\n}\nfunction label(labels, defaultLabel) {\n    var _a;\n    return (_a = labels === null || labels === void 0 ? void 0 : labels[defaultLabel]) !== null && _a !== void 0 ? _a : defaultLabel;\n}\nfunction cleanup(...cleaners) {\n    return ()=>{\n        cleaners.forEach((cleaner)=>{\n            cleaner();\n        });\n    };\n}\nfunction makeUseContext(name, contextName, context) {\n    return ()=>{\n        const ctx = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n        if (!ctx) {\n            throw new Error(`${name} must be used within a ${contextName}.Provider`);\n        }\n        return ctx;\n    };\n}\nfunction hasWindow() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction round(value, decimals = 0) {\n    const factor = 10 ** decimals;\n    return Math.round((value + Number.EPSILON) * factor) / factor;\n}\nfunction isImageSlide(slide) {\n    return slide.type === undefined || slide.type === \"image\";\n}\nfunction isImageFitCover(image, imageFit) {\n    return image.imageFit === _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_COVER || image.imageFit !== _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_CONTAIN && imageFit === _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_COVER;\n}\nfunction parseInt(value) {\n    return typeof value === \"string\" ? Number.parseInt(value, 10) : value;\n}\nfunction parseLengthPercentage(input) {\n    if (typeof input === \"number\") {\n        return {\n            pixel: input\n        };\n    }\n    if (typeof input === \"string\") {\n        const value = parseInt(input);\n        return input.endsWith(\"%\") ? {\n            percent: value\n        } : {\n            pixel: value\n        };\n    }\n    return {\n        pixel: 0\n    };\n}\nfunction computeSlideRect(containerRect, padding) {\n    const paddingValue = parseLengthPercentage(padding);\n    const paddingPixels = paddingValue.percent !== undefined ? containerRect.width / 100 * paddingValue.percent : paddingValue.pixel;\n    return {\n        width: Math.max(containerRect.width - 2 * paddingPixels, 0),\n        height: Math.max(containerRect.height - 2 * paddingPixels, 0)\n    };\n}\nfunction devicePixelRatio() {\n    return (hasWindow() ? window === null || window === void 0 ? void 0 : window.devicePixelRatio : undefined) || 1;\n}\nfunction getSlideIndex(index, slidesCount) {\n    return slidesCount > 0 ? (index % slidesCount + slidesCount) % slidesCount : 0;\n}\nfunction hasSlides(slides) {\n    return slides.length > 0;\n}\nfunction getSlide(slides, index) {\n    return slides[getSlideIndex(index, slides.length)];\n}\nfunction getSlideIfPresent(slides, index) {\n    return hasSlides(slides) ? getSlide(slides, index) : undefined;\n}\nfunction getSlideKey(slide) {\n    return isImageSlide(slide) ? slide.src : undefined;\n}\nfunction addToolbarButton(toolbar, key, button) {\n    if (!button) return toolbar;\n    const { buttons, ...restToolbar } = toolbar;\n    const index = buttons.findIndex((item)=>item === key);\n    const buttonWithKey = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(button) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(button, {\n        key\n    }, null) : button;\n    if (index >= 0) {\n        const result = [\n            ...buttons\n        ];\n        result.splice(index, 1, buttonWithKey);\n        return {\n            buttons: result,\n            ...restToolbar\n        };\n    }\n    return {\n        buttons: [\n            buttonWithKey,\n            ...buttons\n        ],\n        ...restToolbar\n    };\n}\nfunction stopNavigationEventsPropagation() {\n    const stopPropagation = (event)=>{\n        event.stopPropagation();\n    };\n    return {\n        onPointerDown: stopPropagation,\n        onKeyDown: stopPropagation,\n        onWheel: stopPropagation\n    };\n}\nfunction calculatePreload(carousel, slides, minimum = 0) {\n    return Math.min(carousel.preload, Math.max(carousel.finite ? slides.length - 1 : Math.floor(slides.length / 2), minimum));\n}\nconst isReact19 = Number(react__WEBPACK_IMPORTED_MODULE_0__.version.split(\".\")[0]) >= 19;\nfunction makeInertWhen(condition) {\n    const legacyValue = condition ? \"\" : undefined;\n    return {\n        inert: isReact19 ? condition : legacyValue\n    };\n}\nconst LightboxDefaultProps = {\n    open: false,\n    close: ()=>{},\n    index: 0,\n    slides: [],\n    render: {},\n    plugins: [],\n    toolbar: {\n        buttons: [\n            _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE\n        ]\n    },\n    labels: {},\n    animation: {\n        fade: 250,\n        swipe: 500,\n        easing: {\n            fade: \"ease\",\n            swipe: \"ease-out\",\n            navigation: \"ease-in-out\"\n        }\n    },\n    carousel: {\n        finite: false,\n        preload: 2,\n        padding: \"16px\",\n        spacing: \"30%\",\n        imageFit: _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_CONTAIN,\n        imageProps: {}\n    },\n    controller: {\n        ref: null,\n        focus: true,\n        aria: false,\n        touchAction: \"none\",\n        closeOnPullUp: false,\n        closeOnPullDown: false,\n        closeOnBackdropClick: false,\n        preventDefaultWheelX: true,\n        preventDefaultWheelY: false\n    },\n    portal: {},\n    noScroll: {\n        disabled: false\n    },\n    on: {},\n    styles: {},\n    className: \"\"\n};\nfunction createModule(name, component) {\n    return {\n        name,\n        component\n    };\n}\nfunction createNode(module, children) {\n    return {\n        module,\n        children\n    };\n}\nfunction traverseNode(node, target, apply) {\n    if (node.module.name === target) {\n        return apply(node);\n    }\n    if (node.children) {\n        return [\n            createNode(node.module, node.children.flatMap((n)=>{\n                var _a;\n                return (_a = traverseNode(n, target, apply)) !== null && _a !== void 0 ? _a : [];\n            }))\n        ];\n    }\n    return [\n        node\n    ];\n}\nfunction traverse(nodes, target, apply) {\n    return nodes.flatMap((node)=>{\n        var _a;\n        return (_a = traverseNode(node, target, apply)) !== null && _a !== void 0 ? _a : [];\n    });\n}\nfunction withPlugins(root, plugins = [], augmentations = []) {\n    let config = root;\n    const contains = (target)=>{\n        const nodes = [\n            ...config\n        ];\n        while(nodes.length > 0){\n            const node = nodes.pop();\n            if ((node === null || node === void 0 ? void 0 : node.module.name) === target) return true;\n            if (node === null || node === void 0 ? void 0 : node.children) nodes.push(...node.children);\n        }\n        return false;\n    };\n    const addParent = (target, module)=>{\n        if (target === \"\") {\n            config = [\n                createNode(module, config)\n            ];\n            return;\n        }\n        config = traverse(config, target, (node)=>[\n                createNode(module, [\n                    node\n                ])\n            ]);\n    };\n    const append = (target, module)=>{\n        config = traverse(config, target, (node)=>[\n                createNode(node.module, [\n                    createNode(module, node.children)\n                ])\n            ]);\n    };\n    const addChild = (target, module, precede)=>{\n        config = traverse(config, target, (node)=>{\n            var _a;\n            return [\n                createNode(node.module, [\n                    ...precede ? [\n                        createNode(module)\n                    ] : [],\n                    ...(_a = node.children) !== null && _a !== void 0 ? _a : [],\n                    ...!precede ? [\n                        createNode(module)\n                    ] : []\n                ])\n            ];\n        });\n    };\n    const addSibling = (target, module, precede)=>{\n        config = traverse(config, target, (node)=>[\n                ...precede ? [\n                    createNode(module)\n                ] : [],\n                node,\n                ...!precede ? [\n                    createNode(module)\n                ] : []\n            ]);\n    };\n    const addModule = (module)=>{\n        append(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CONTROLLER, module);\n    };\n    const replace = (target, module)=>{\n        config = traverse(config, target, (node)=>[\n                createNode(module, node.children)\n            ]);\n    };\n    const remove = (target)=>{\n        config = traverse(config, target, (node)=>node.children);\n    };\n    const augment = (augmentation)=>{\n        augmentations.push(augmentation);\n    };\n    plugins.forEach((plugin)=>{\n        plugin({\n            contains,\n            addParent,\n            append,\n            addChild,\n            addSibling,\n            addModule,\n            replace,\n            remove,\n            augment\n        });\n    });\n    return {\n        config,\n        augmentation: (props)=>augmentations.reduce((acc, augmentation)=>augmentation(acc), props)\n    };\n}\nconst DocumentContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useDocumentContext = makeUseContext(\"useDocument\", \"DocumentContext\", DocumentContext);\nfunction DocumentContextProvider({ nodeRef, children }) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const getOwnerDocument = (node)=>{\n            var _a;\n            return ((_a = node || nodeRef.current) === null || _a === void 0 ? void 0 : _a.ownerDocument) || document;\n        };\n        const getOwnerWindow = (node)=>{\n            var _a;\n            return ((_a = getOwnerDocument(node)) === null || _a === void 0 ? void 0 : _a.defaultView) || window;\n        };\n        return {\n            getOwnerDocument,\n            getOwnerWindow\n        };\n    }, [\n        nodeRef\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(DocumentContext.Provider, {\n        value: context\n    }, children);\n}\nconst EventsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useEvents = makeUseContext(\"useEvents\", \"EventsContext\", EventsContext);\nfunction EventsProvider({ children }) {\n    const [subscriptions] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>()=>{\n            Object.keys(subscriptions).forEach((topic)=>delete subscriptions[topic]);\n        }, [\n        subscriptions\n    ]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const unsubscribe = (topic, callback)=>{\n            var _a;\n            (_a = subscriptions[topic]) === null || _a === void 0 ? void 0 : _a.splice(0, subscriptions[topic].length, ...subscriptions[topic].filter((cb)=>cb !== callback));\n        };\n        const subscribe = (topic, callback)=>{\n            if (!subscriptions[topic]) {\n                subscriptions[topic] = [];\n            }\n            subscriptions[topic].push(callback);\n            return ()=>unsubscribe(topic, callback);\n        };\n        const publish = (...[topic, event])=>{\n            var _a;\n            (_a = subscriptions[topic]) === null || _a === void 0 ? void 0 : _a.forEach((callback)=>callback(event));\n        };\n        return {\n            publish,\n            subscribe,\n            unsubscribe\n        };\n    }, [\n        subscriptions\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(EventsContext.Provider, {\n        value: context\n    }, children);\n}\nconst LightboxPropsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useLightboxProps = makeUseContext(\"useLightboxProps\", \"LightboxPropsContext\", LightboxPropsContext);\nfunction LightboxPropsProvider({ children, ...props }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxPropsContext.Provider, {\n        value: props\n    }, children);\n}\nconst LightboxStateContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useLightboxState = makeUseContext(\"useLightboxState\", \"LightboxStateContext\", LightboxStateContext);\nconst LightboxDispatchContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useLightboxDispatch = makeUseContext(\"useLightboxDispatch\", \"LightboxDispatchContext\", LightboxDispatchContext);\nfunction reducer(state, action) {\n    switch(action.type){\n        case \"swipe\":\n            {\n                const { slides } = state;\n                const increment = (action === null || action === void 0 ? void 0 : action.increment) || 0;\n                const globalIndex = state.globalIndex + increment;\n                const currentIndex = getSlideIndex(globalIndex, slides.length);\n                const currentSlide = getSlideIfPresent(slides, currentIndex);\n                const animation = increment || action.duration ? {\n                    increment,\n                    duration: action.duration,\n                    easing: action.easing\n                } : undefined;\n                return {\n                    slides,\n                    currentIndex,\n                    globalIndex,\n                    currentSlide,\n                    animation\n                };\n            }\n        case \"update\":\n            if (action.slides !== state.slides || action.index !== state.currentIndex) {\n                return {\n                    slides: action.slides,\n                    currentIndex: action.index,\n                    globalIndex: action.index,\n                    currentSlide: getSlideIfPresent(action.slides, action.index)\n                };\n            }\n            return state;\n        default:\n            throw new Error(_types_js__WEBPACK_IMPORTED_MODULE_2__.UNKNOWN_ACTION_TYPE);\n    }\n}\nfunction LightboxStateProvider({ slides, index, children }) {\n    const [state, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(reducer, {\n        slides,\n        currentIndex: index,\n        globalIndex: index,\n        currentSlide: getSlideIfPresent(slides, index)\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        dispatch({\n            type: \"update\",\n            slides,\n            index\n        });\n    }, [\n        slides,\n        index\n    ]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            ...state,\n            state,\n            dispatch\n        }), [\n        state,\n        dispatch\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxDispatchContext.Provider, {\n        value: dispatch\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxStateContext.Provider, {\n        value: context\n    }, children));\n}\nconst TimeoutsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useTimeouts = makeUseContext(\"useTimeouts\", \"TimeoutsContext\", TimeoutsContext);\nfunction TimeoutsProvider({ children }) {\n    const [timeouts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>()=>{\n            timeouts.forEach((tid)=>window.clearTimeout(tid));\n            timeouts.splice(0, timeouts.length);\n        }, [\n        timeouts\n    ]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const removeTimeout = (id)=>{\n            timeouts.splice(0, timeouts.length, ...timeouts.filter((tid)=>tid !== id));\n        };\n        const setTimeout = (fn, delay)=>{\n            const id = window.setTimeout(()=>{\n                removeTimeout(id);\n                fn();\n            }, delay);\n            timeouts.push(id);\n            return id;\n        };\n        const clearTimeout = (id)=>{\n            if (id !== undefined) {\n                removeTimeout(id);\n                window.clearTimeout(id);\n            }\n        };\n        return {\n            setTimeout,\n            clearTimeout\n        };\n    }, [\n        timeouts\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(TimeoutsContext.Provider, {\n        value: context\n    }, children);\n}\nconst IconButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function IconButton({ label: label$1, className, icon: Icon, renderIcon, onClick, style, ...rest }, ref) {\n    const { styles, labels } = useLightboxProps();\n    const buttonLabel = label(labels, label$1);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        ref: ref,\n        type: \"button\",\n        title: buttonLabel,\n        \"aria-label\": buttonLabel,\n        className: clsx(cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_BUTTON), className),\n        onClick: onClick,\n        style: {\n            ...style,\n            ...styles.button\n        },\n        ...rest\n    }, renderIcon ? renderIcon() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Icon, {\n        className: cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_ICON),\n        style: styles.icon\n    }));\n});\nfunction svgIcon(name, children) {\n    const icon = (props)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            width: \"24\",\n            height: \"24\",\n            \"aria-hidden\": \"true\",\n            focusable: \"false\",\n            ...props\n        }, children);\n    icon.displayName = name;\n    return icon;\n}\nfunction createIcon(name, glyph) {\n    return svgIcon(name, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"g\", {\n        fill: \"currentColor\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0 0h24v24H0z\",\n        fill: \"none\"\n    }), glyph));\n}\nfunction createIconDisabled(name, glyph) {\n    return svgIcon(name, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"defs\", null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"mask\", {\n        id: \"strike\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0 0h24v24H0z\",\n        fill: \"white\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0 0L24 24\",\n        stroke: \"black\",\n        strokeWidth: 4\n    }))), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0.70707 2.121320L21.878680 23.292883\",\n        stroke: \"currentColor\",\n        strokeWidth: 2\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"g\", {\n        fill: \"currentColor\",\n        mask: \"url(#strike)\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0 0h24v24H0z\",\n        fill: \"none\"\n    }), glyph)));\n}\nconst CloseIcon = createIcon(\"Close\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n}));\nconst PreviousIcon = createIcon(\"Previous\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    d: \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"\n}));\nconst NextIcon = createIcon(\"Next\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    d: \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"\n}));\nconst LoadingIcon = createIcon(\"Loading\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, Array.from({\n    length: 8\n}).map((_, index, array)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n        key: index,\n        x1: \"12\",\n        y1: \"6.5\",\n        x2: \"12\",\n        y2: \"1.8\",\n        strokeLinecap: \"round\",\n        strokeWidth: \"2.6\",\n        stroke: \"currentColor\",\n        strokeOpacity: 1 / array.length * (index + 1),\n        transform: `rotate(${360 / array.length * index}, 12, 12)`\n    }))));\nconst ErrorIcon = createIcon(\"Error\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    d: \"M21.9,21.9l-8.49-8.49l0,0L3.59,3.59l0,0L2.1,2.1L0.69,3.51L3,5.83V19c0,1.1,0.9,2,2,2h13.17l2.31,2.31L21.9,21.9z M5,18 l3.5-4.5l2.5,3.01L12.17,15l3,3H5z M21,18.17L5.83,3H19c1.1,0,2,0.9,2,2V18.17z\"\n}));\nconst useLayoutEffect = hasWindow() ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction useMotionPreference() {\n    const [reduceMotion, setReduceMotion] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _a, _b;\n        const mediaQuery = (_a = window.matchMedia) === null || _a === void 0 ? void 0 : _a.call(window, \"(prefers-reduced-motion: reduce)\");\n        setReduceMotion(mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.matches);\n        const listener = (event)=>setReduceMotion(event.matches);\n        (_b = mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.addEventListener) === null || _b === void 0 ? void 0 : _b.call(mediaQuery, \"change\", listener);\n        return ()=>{\n            var _a;\n            return (_a = mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.removeEventListener) === null || _a === void 0 ? void 0 : _a.call(mediaQuery, \"change\", listener);\n        };\n    }, []);\n    return reduceMotion;\n}\nfunction currentTransformation(node) {\n    let x = 0;\n    let y = 0;\n    let z = 0;\n    const matrix = window.getComputedStyle(node).transform;\n    const matcher = matrix.match(/matrix.*\\((.+)\\)/);\n    if (matcher) {\n        const values = matcher[1].split(\",\").map(parseInt);\n        if (values.length === 6) {\n            x = values[4];\n            y = values[5];\n        } else if (values.length === 16) {\n            x = values[12];\n            y = values[13];\n            z = values[14];\n        }\n    }\n    return {\n        x,\n        y,\n        z\n    };\n}\nfunction useAnimation(nodeRef, computeAnimation) {\n    const snapshot = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const animation = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const reduceMotion = useMotionPreference();\n    useLayoutEffect(()=>{\n        var _a, _b, _c;\n        if (nodeRef.current && snapshot.current !== undefined && !reduceMotion) {\n            const { keyframes, duration, easing, onfinish } = computeAnimation(snapshot.current, nodeRef.current.getBoundingClientRect(), currentTransformation(nodeRef.current)) || {};\n            if (keyframes && duration) {\n                (_a = animation.current) === null || _a === void 0 ? void 0 : _a.cancel();\n                animation.current = undefined;\n                try {\n                    animation.current = (_c = (_b = nodeRef.current).animate) === null || _c === void 0 ? void 0 : _c.call(_b, keyframes, {\n                        duration,\n                        easing\n                    });\n                } catch (err) {\n                    console.error(err);\n                }\n                if (animation.current) {\n                    animation.current.onfinish = ()=>{\n                        animation.current = undefined;\n                        onfinish === null || onfinish === void 0 ? void 0 : onfinish();\n                    };\n                }\n            }\n        }\n        snapshot.current = undefined;\n    });\n    return {\n        prepareAnimation: (currentSnapshot)=>{\n            snapshot.current = currentSnapshot;\n        },\n        isAnimationPlaying: ()=>{\n            var _a;\n            return ((_a = animation.current) === null || _a === void 0 ? void 0 : _a.playState) === \"running\";\n        }\n    };\n}\nfunction useContainerRect() {\n    const containerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const observerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const [containerRect, setContainerRect] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const setContainerRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        containerRef.current = node;\n        if (observerRef.current) {\n            observerRef.current.disconnect();\n            observerRef.current = undefined;\n        }\n        const updateContainerRect = ()=>{\n            if (node) {\n                const styles = window.getComputedStyle(node);\n                const parse = (value)=>parseFloat(value) || 0;\n                setContainerRect({\n                    width: Math.round(node.clientWidth - parse(styles.paddingLeft) - parse(styles.paddingRight)),\n                    height: Math.round(node.clientHeight - parse(styles.paddingTop) - parse(styles.paddingBottom))\n                });\n            } else {\n                setContainerRect(undefined);\n            }\n        };\n        updateContainerRect();\n        if (node && typeof ResizeObserver !== \"undefined\") {\n            observerRef.current = new ResizeObserver(updateContainerRect);\n            observerRef.current.observe(node);\n        }\n    }, []);\n    return {\n        setContainerRef,\n        containerRef,\n        containerRect\n    };\n}\nfunction useDelay() {\n    const timeoutId = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const { setTimeout, clearTimeout } = useTimeouts();\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback((callback, delay)=>{\n        clearTimeout(timeoutId.current);\n        timeoutId.current = setTimeout(callback, delay > 0 ? delay : 0);\n    }, [\n        setTimeout,\n        clearTimeout\n    ]);\n}\nfunction useEventCallback(fn) {\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fn);\n    useLayoutEffect(()=>{\n        ref.current = fn;\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback((...args)=>{\n        var _a;\n        return (_a = ref.current) === null || _a === void 0 ? void 0 : _a.call(ref, ...args);\n    }, []);\n}\nfunction setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        ref(value);\n    } else if (ref) {\n        ref.current = value;\n    }\n}\nfunction useForkRef(refA, refB) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>refA == null && refB == null ? null : (refValue)=>{\n            setRef(refA, refValue);\n            setRef(refB, refValue);\n        }, [\n        refA,\n        refB\n    ]);\n}\nfunction useLoseFocus(focus, disabled = false) {\n    const focused = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    useLayoutEffect(()=>{\n        if (disabled && focused.current) {\n            focused.current = false;\n            focus();\n        }\n    }, [\n        disabled,\n        focus\n    ]);\n    const onFocus = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        focused.current = true;\n    }, []);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        focused.current = false;\n    }, []);\n    return {\n        onFocus,\n        onBlur\n    };\n}\nfunction useRTL() {\n    const [isRTL, setIsRTL] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    useLayoutEffect(()=>{\n        setIsRTL(window.getComputedStyle(window.document.documentElement).direction === \"rtl\");\n    }, []);\n    return isRTL;\n}\nfunction useSensors() {\n    const [subscribers] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const notifySubscribers = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((type, event)=>{\n        var _a;\n        (_a = subscribers[type]) === null || _a === void 0 ? void 0 : _a.forEach((listener)=>{\n            if (!event.isPropagationStopped()) listener(event);\n        });\n    }, [\n        subscribers\n    ]);\n    const registerSensors = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            onPointerDown: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_DOWN, event),\n            onPointerMove: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_MOVE, event),\n            onPointerUp: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_UP, event),\n            onPointerLeave: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_LEAVE, event),\n            onPointerCancel: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_CANCEL, event),\n            onKeyDown: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_DOWN, event),\n            onKeyUp: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_UP, event),\n            onWheel: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_WHEEL, event)\n        }), [\n        notifySubscribers\n    ]);\n    const subscribeSensors = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((type, callback)=>{\n        if (!subscribers[type]) {\n            subscribers[type] = [];\n        }\n        subscribers[type].unshift(callback);\n        return ()=>{\n            const listeners = subscribers[type];\n            if (listeners) {\n                listeners.splice(0, listeners.length, ...listeners.filter((el)=>el !== callback));\n            }\n        };\n    }, [\n        subscribers\n    ]);\n    return {\n        registerSensors,\n        subscribeSensors\n    };\n}\nfunction useThrottle(callback, delay) {\n    const lastCallbackTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const delayCallback = useDelay();\n    const executeCallback = useEventCallback((...args)=>{\n        lastCallbackTime.current = Date.now();\n        callback(args);\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback((...args)=>{\n        delayCallback(()=>{\n            executeCallback(args);\n        }, delay - (Date.now() - lastCallbackTime.current));\n    }, [\n        delay,\n        executeCallback,\n        delayCallback\n    ]);\n}\nconst slidePrefix = makeComposePrefix(\"slide\");\nconst slideImagePrefix = makeComposePrefix(\"slide_image\");\nfunction ImageSlide({ slide: image, offset, render, rect, imageFit, imageProps, onClick, onLoad, onError, style }) {\n    var _a, _b, _c, _d, _e, _f, _g;\n    const [status, setStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_LOADING);\n    const { publish } = useEvents();\n    const { setTimeout } = useTimeouts();\n    const imageRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (offset === 0) {\n            publish((0,_types_js__WEBPACK_IMPORTED_MODULE_2__.activeSlideStatus)(status));\n        }\n    }, [\n        offset,\n        status,\n        publish\n    ]);\n    const handleLoading = useEventCallback((img)=>{\n        (\"decode\" in img ? img.decode() : Promise.resolve()).catch(()=>{}).then(()=>{\n            if (!img.parentNode) {\n                return;\n            }\n            setStatus(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_COMPLETE);\n            setTimeout(()=>{\n                onLoad === null || onLoad === void 0 ? void 0 : onLoad(img);\n            }, 0);\n        });\n    });\n    const setImageRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((img)=>{\n        imageRef.current = img;\n        if (img === null || img === void 0 ? void 0 : img.complete) {\n            handleLoading(img);\n        }\n    }, [\n        handleLoading\n    ]);\n    const handleOnLoad = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        handleLoading(event.currentTarget);\n    }, [\n        handleLoading\n    ]);\n    const handleOnError = useEventCallback(()=>{\n        setStatus(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_ERROR);\n        onError === null || onError === void 0 ? void 0 : onError();\n    });\n    const cover = isImageFitCover(image, imageFit);\n    const nonInfinite = (value, fallback)=>Number.isFinite(value) ? value : fallback;\n    const maxWidth = nonInfinite(Math.max(...((_b = (_a = image.srcSet) === null || _a === void 0 ? void 0 : _a.map((x)=>x.width)) !== null && _b !== void 0 ? _b : []).concat(image.width ? [\n        image.width\n    ] : []).filter(Boolean)), ((_c = imageRef.current) === null || _c === void 0 ? void 0 : _c.naturalWidth) || 0);\n    const maxHeight = nonInfinite(Math.max(...((_e = (_d = image.srcSet) === null || _d === void 0 ? void 0 : _d.map((x)=>x.height)) !== null && _e !== void 0 ? _e : []).concat(image.height ? [\n        image.height\n    ] : []).filter(Boolean)), ((_f = imageRef.current) === null || _f === void 0 ? void 0 : _f.naturalHeight) || 0);\n    const defaultStyle = maxWidth && maxHeight ? {\n        maxWidth: `min(${maxWidth}px, 100%)`,\n        maxHeight: `min(${maxHeight}px, 100%)`\n    } : {\n        maxWidth: \"100%\",\n        maxHeight: \"100%\"\n    };\n    const srcSet = (_g = image.srcSet) === null || _g === void 0 ? void 0 : _g.sort((a, b)=>a.width - b.width).map((item)=>`${item.src} ${item.width}w`).join(\", \");\n    const estimateActualWidth = ()=>rect && !cover && image.width && image.height ? rect.height / image.height * image.width : Number.MAX_VALUE;\n    const sizes = srcSet && rect && hasWindow() ? `${Math.round(Math.min(estimateActualWidth(), rect.width))}px` : undefined;\n    const { style: imagePropsStyle, className: imagePropsClassName, ...restImageProps } = imageProps || {};\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"img\", {\n        ref: setImageRef,\n        onLoad: handleOnLoad,\n        onError: handleOnError,\n        onClick: onClick,\n        draggable: false,\n        className: clsx(cssClass(slideImagePrefix()), cover && cssClass(slideImagePrefix(\"cover\")), status !== _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_COMPLETE && cssClass(slideImagePrefix(\"loading\")), imagePropsClassName),\n        style: {\n            ...defaultStyle,\n            ...style,\n            ...imagePropsStyle\n        },\n        ...restImageProps,\n        alt: image.alt,\n        sizes: sizes,\n        srcSet: srcSet,\n        src: image.src\n    }), status !== _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_COMPLETE && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: cssClass(slidePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_PLACEHOLDER))\n    }, status === _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_LOADING && ((render === null || render === void 0 ? void 0 : render.iconLoading) ? render.iconLoading() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LoadingIcon, {\n        className: clsx(cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_ICON), cssClass(slidePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_LOADING)))\n    })), status === _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_ERROR && ((render === null || render === void 0 ? void 0 : render.iconError) ? render.iconError() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ErrorIcon, {\n        className: clsx(cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_ICON), cssClass(slidePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_ERROR)))\n    }))));\n}\nconst LightboxRoot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function LightboxRoot({ className, children, ...rest }, ref) {\n    const nodeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(DocumentContextProvider, {\n        nodeRef: nodeRef\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: useForkRef(ref, nodeRef),\n        className: clsx(cssClass(\"root\"), className),\n        ...rest\n    }, children));\n});\nvar SwipeState;\n(function(SwipeState) {\n    SwipeState[SwipeState[\"NONE\"] = 0] = \"NONE\";\n    SwipeState[SwipeState[\"SWIPE\"] = 1] = \"SWIPE\";\n    SwipeState[SwipeState[\"PULL\"] = 2] = \"PULL\";\n    SwipeState[SwipeState[\"ANIMATION\"] = 3] = \"ANIMATION\";\n})(SwipeState || (SwipeState = {}));\nfunction usePointerEvents(subscribeSensors, onPointerDown, onPointerMove, onPointerUp, disabled) {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>!disabled ? cleanup(subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_DOWN, onPointerDown), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_MOVE, onPointerMove), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_UP, onPointerUp), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_LEAVE, onPointerUp), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_CANCEL, onPointerUp)) : ()=>{}, [\n        subscribeSensors,\n        onPointerDown,\n        onPointerMove,\n        onPointerUp,\n        disabled\n    ]);\n}\nvar Gesture;\n(function(Gesture) {\n    Gesture[Gesture[\"NONE\"] = 0] = \"NONE\";\n    Gesture[Gesture[\"SWIPE\"] = 1] = \"SWIPE\";\n    Gesture[Gesture[\"PULL\"] = 2] = \"PULL\";\n})(Gesture || (Gesture = {}));\nconst SWIPE_THRESHOLD = 30;\nfunction usePointerSwipe(subscribeSensors, isSwipeValid, containerWidth, swipeAnimationDuration, onSwipeStart, onSwipeProgress, onSwipeFinish, onSwipeCancel, pullUpEnabled, pullDownEnabled, onPullStart, onPullProgress, onPullFinish, onPullCancel) {\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointers = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n    const activePointer = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const startTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const gesture = react__WEBPACK_IMPORTED_MODULE_0__.useRef(Gesture.NONE);\n    const clearPointer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        if (activePointer.current === event.pointerId) {\n            activePointer.current = undefined;\n            gesture.current = Gesture.NONE;\n        }\n        const currentPointers = pointers.current;\n        currentPointers.splice(0, currentPointers.length, ...currentPointers.filter((p)=>p.pointerId !== event.pointerId));\n    }, []);\n    const addPointer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        clearPointer(event);\n        event.persist();\n        pointers.current.push(event);\n    }, [\n        clearPointer\n    ]);\n    const onPointerDown = useEventCallback((event)=>{\n        addPointer(event);\n    });\n    const exceedsPullThreshold = (value, threshold)=>pullDownEnabled && value > threshold || pullUpEnabled && value < -threshold;\n    const onPointerUp = useEventCallback((event)=>{\n        if (pointers.current.find((x)=>x.pointerId === event.pointerId) && activePointer.current === event.pointerId) {\n            const duration = Date.now() - startTime.current;\n            const currentOffset = offset.current;\n            if (gesture.current === Gesture.SWIPE) {\n                if (Math.abs(currentOffset) > 0.3 * containerWidth || Math.abs(currentOffset) > 5 && duration < swipeAnimationDuration) {\n                    onSwipeFinish(currentOffset, duration);\n                } else {\n                    onSwipeCancel(currentOffset);\n                }\n            } else if (gesture.current === Gesture.PULL) {\n                if (exceedsPullThreshold(currentOffset, 2 * SWIPE_THRESHOLD)) {\n                    onPullFinish(currentOffset, duration);\n                } else {\n                    onPullCancel(currentOffset);\n                }\n            }\n            offset.current = 0;\n            gesture.current = Gesture.NONE;\n        }\n        clearPointer(event);\n    });\n    const onPointerMove = useEventCallback((event)=>{\n        const pointer = pointers.current.find((p)=>p.pointerId === event.pointerId);\n        if (pointer) {\n            const isCurrentPointer = activePointer.current === event.pointerId;\n            if (event.buttons === 0) {\n                if (isCurrentPointer && offset.current !== 0) {\n                    onPointerUp(event);\n                } else {\n                    clearPointer(pointer);\n                }\n                return;\n            }\n            const deltaX = event.clientX - pointer.clientX;\n            const deltaY = event.clientY - pointer.clientY;\n            if (activePointer.current === undefined) {\n                const startGesture = (newGesture)=>{\n                    addPointer(event);\n                    activePointer.current = event.pointerId;\n                    startTime.current = Date.now();\n                    gesture.current = newGesture;\n                };\n                if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > SWIPE_THRESHOLD && isSwipeValid(deltaX)) {\n                    startGesture(Gesture.SWIPE);\n                    onSwipeStart();\n                } else if (Math.abs(deltaY) > Math.abs(deltaX) && exceedsPullThreshold(deltaY, SWIPE_THRESHOLD)) {\n                    startGesture(Gesture.PULL);\n                    onPullStart();\n                }\n            } else if (isCurrentPointer) {\n                if (gesture.current === Gesture.SWIPE) {\n                    offset.current = deltaX;\n                    onSwipeProgress(deltaX);\n                } else if (gesture.current === Gesture.PULL) {\n                    offset.current = deltaY;\n                    onPullProgress(deltaY);\n                }\n            }\n        }\n    });\n    usePointerEvents(subscribeSensors, onPointerDown, onPointerMove, onPointerUp);\n}\nfunction usePreventWheelDefaults({ preventDefaultWheelX, preventDefaultWheelY }) {\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const listener = useEventCallback((event)=>{\n        const horizontal = Math.abs(event.deltaX) > Math.abs(event.deltaY);\n        if (horizontal && preventDefaultWheelX || !horizontal && preventDefaultWheelY || event.ctrlKey) {\n            event.preventDefault();\n        }\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        var _a;\n        if (node) {\n            node.addEventListener(\"wheel\", listener, {\n                passive: false\n            });\n        } else {\n            (_a = ref.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(\"wheel\", listener);\n        }\n        ref.current = node;\n    }, [\n        listener\n    ]);\n}\nfunction useWheelSwipe(swipeState, subscribeSensors, isSwipeValid, containerWidth, swipeAnimationDuration, onSwipeStart, onSwipeProgress, onSwipeFinish, onSwipeCancel) {\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const intent = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const intentCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const resetCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const wheelInertia = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const wheelInertiaCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const startTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const { setTimeout, clearTimeout } = useTimeouts();\n    const cancelSwipeIntentCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (intentCleanup.current) {\n            clearTimeout(intentCleanup.current);\n            intentCleanup.current = undefined;\n        }\n    }, [\n        clearTimeout\n    ]);\n    const cancelSwipeResetCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (resetCleanup.current) {\n            clearTimeout(resetCleanup.current);\n            resetCleanup.current = undefined;\n        }\n    }, [\n        clearTimeout\n    ]);\n    const handleCleanup = useEventCallback(()=>{\n        if (swipeState !== SwipeState.SWIPE) {\n            offset.current = 0;\n            startTime.current = 0;\n            cancelSwipeIntentCleanup();\n            cancelSwipeResetCleanup();\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleCleanup, [\n        swipeState,\n        handleCleanup\n    ]);\n    const handleCancelSwipe = useEventCallback((currentSwipeOffset)=>{\n        resetCleanup.current = undefined;\n        if (offset.current === currentSwipeOffset) {\n            onSwipeCancel(offset.current);\n        }\n    });\n    const onWheel = useEventCallback((event)=>{\n        if (event.ctrlKey) {\n            return;\n        }\n        if (Math.abs(event.deltaY) > Math.abs(event.deltaX)) {\n            return;\n        }\n        const setWheelInertia = (inertia)=>{\n            wheelInertia.current = inertia;\n            clearTimeout(wheelInertiaCleanup.current);\n            wheelInertiaCleanup.current = inertia > 0 ? setTimeout(()=>{\n                wheelInertia.current = 0;\n                wheelInertiaCleanup.current = undefined;\n            }, 300) : undefined;\n        };\n        if (swipeState === SwipeState.NONE) {\n            if (Math.abs(event.deltaX) <= 1.2 * Math.abs(wheelInertia.current)) {\n                setWheelInertia(event.deltaX);\n                return;\n            }\n            if (!isSwipeValid(-event.deltaX)) {\n                return;\n            }\n            intent.current += event.deltaX;\n            cancelSwipeIntentCleanup();\n            if (Math.abs(intent.current) > 30) {\n                intent.current = 0;\n                setWheelInertia(0);\n                startTime.current = Date.now();\n                onSwipeStart();\n            } else {\n                const currentSwipeIntent = intent.current;\n                intentCleanup.current = setTimeout(()=>{\n                    intentCleanup.current = undefined;\n                    if (currentSwipeIntent === intent.current) {\n                        intent.current = 0;\n                    }\n                }, swipeAnimationDuration);\n            }\n        } else if (swipeState === SwipeState.SWIPE) {\n            let newSwipeOffset = offset.current - event.deltaX;\n            newSwipeOffset = Math.min(Math.abs(newSwipeOffset), containerWidth) * Math.sign(newSwipeOffset);\n            offset.current = newSwipeOffset;\n            onSwipeProgress(newSwipeOffset);\n            cancelSwipeResetCleanup();\n            if (Math.abs(newSwipeOffset) > 0.2 * containerWidth) {\n                setWheelInertia(event.deltaX);\n                onSwipeFinish(newSwipeOffset, Date.now() - startTime.current);\n                return;\n            }\n            resetCleanup.current = setTimeout(()=>handleCancelSwipe(newSwipeOffset), 2 * swipeAnimationDuration);\n        } else {\n            setWheelInertia(event.deltaX);\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_WHEEL, onWheel), [\n        subscribeSensors,\n        onWheel\n    ]);\n}\nconst cssContainerPrefix = makeComposePrefix(\"container\");\nconst ControllerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useController = makeUseContext(\"useController\", \"ControllerContext\", ControllerContext);\nfunction Controller({ children, ...props }) {\n    var _a;\n    const { carousel, animation, controller, on, styles, render } = props;\n    const { closeOnPullUp, closeOnPullDown, preventDefaultWheelX, preventDefaultWheelY } = controller;\n    const [toolbarWidth, setToolbarWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const state = useLightboxState();\n    const dispatch = useLightboxDispatch();\n    const [swipeState, setSwipeState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(SwipeState.NONE);\n    const swipeOffset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pullOffset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pullOpacity = react__WEBPACK_IMPORTED_MODULE_0__.useRef(1);\n    const { registerSensors, subscribeSensors } = useSensors();\n    const { subscribe, publish } = useEvents();\n    const cleanupAnimationIncrement = useDelay();\n    const cleanupSwipeOffset = useDelay();\n    const cleanupPullOffset = useDelay();\n    const { containerRef, setContainerRef, containerRect } = useContainerRect();\n    const handleContainerRef = useForkRef(usePreventWheelDefaults({\n        preventDefaultWheelX,\n        preventDefaultWheelY\n    }), setContainerRef);\n    const carouselRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const setCarouselRef = useForkRef(carouselRef, undefined);\n    const { getOwnerDocument } = useDocumentContext();\n    const isRTL = useRTL();\n    const rtl = (value)=>(isRTL ? -1 : 1) * (typeof value === \"number\" ? value : 1);\n    const focus = useEventCallback(()=>{\n        var _a;\n        return (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    });\n    const getLightboxProps = useEventCallback(()=>props);\n    const getLightboxState = useEventCallback(()=>state);\n    const prev = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((params)=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV, params), [\n        publish\n    ]);\n    const next = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((params)=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT, params), [\n        publish\n    ]);\n    const close = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE), [\n        publish\n    ]);\n    const isSwipeValid = (offset)=>!(carousel.finite && (rtl(offset) > 0 && state.currentIndex === 0 || rtl(offset) < 0 && state.currentIndex === state.slides.length - 1));\n    const setSwipeOffset = (offset)=>{\n        var _a;\n        swipeOffset.current = offset;\n        (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.style.setProperty(cssVar(\"swipe_offset\"), `${Math.round(offset)}px`);\n    };\n    const setPullOffset = (offset)=>{\n        var _a, _b;\n        pullOffset.current = offset;\n        pullOpacity.current = (()=>{\n            const threshold = 60;\n            const minOpacity = 0.5;\n            const offsetValue = (()=>{\n                if (closeOnPullDown && offset > 0) return offset;\n                if (closeOnPullUp && offset < 0) return -offset;\n                return 0;\n            })();\n            return Math.min(Math.max(round(1 - offsetValue / threshold * (1 - minOpacity), 2), minOpacity), 1);\n        })();\n        (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.style.setProperty(cssVar(\"pull_offset\"), `${Math.round(offset)}px`);\n        (_b = containerRef.current) === null || _b === void 0 ? void 0 : _b.style.setProperty(cssVar(\"pull_opacity\"), `${pullOpacity.current}`);\n    };\n    const { prepareAnimation: preparePullAnimation } = useAnimation(carouselRef, (snapshot, rect, translate)=>{\n        if (carouselRef.current && containerRect) {\n            return {\n                keyframes: [\n                    {\n                        transform: `translate(0, ${snapshot.rect.y - rect.y + translate.y}px)`,\n                        opacity: snapshot.opacity\n                    },\n                    {\n                        transform: \"translate(0, 0)\",\n                        opacity: 1\n                    }\n                ],\n                duration: snapshot.duration,\n                easing: animation.easing.fade\n            };\n        }\n        return undefined;\n    });\n    const pull = (offset, cancel)=>{\n        if (closeOnPullUp || closeOnPullDown) {\n            setPullOffset(offset);\n            let duration = 0;\n            if (carouselRef.current) {\n                duration = animation.fade * (cancel ? 2 : 1);\n                preparePullAnimation({\n                    rect: carouselRef.current.getBoundingClientRect(),\n                    opacity: pullOpacity.current,\n                    duration\n                });\n            }\n            cleanupPullOffset(()=>{\n                setPullOffset(0);\n                setSwipeState(SwipeState.NONE);\n            }, duration);\n            setSwipeState(SwipeState.ANIMATION);\n            if (!cancel) {\n                close();\n            }\n        }\n    };\n    const { prepareAnimation, isAnimationPlaying } = useAnimation(carouselRef, (snapshot, rect, translate)=>{\n        var _a;\n        if (carouselRef.current && containerRect && ((_a = state.animation) === null || _a === void 0 ? void 0 : _a.duration)) {\n            const parsedSpacing = parseLengthPercentage(carousel.spacing);\n            const spacingValue = (parsedSpacing.percent ? parsedSpacing.percent * containerRect.width / 100 : parsedSpacing.pixel) || 0;\n            return {\n                keyframes: [\n                    {\n                        transform: `translate(${rtl(state.globalIndex - snapshot.index) * (containerRect.width + spacingValue) + snapshot.rect.x - rect.x + translate.x}px, 0)`\n                    },\n                    {\n                        transform: \"translate(0, 0)\"\n                    }\n                ],\n                duration: state.animation.duration,\n                easing: state.animation.easing\n            };\n        }\n        return undefined;\n    });\n    const swipe = useEventCallback((action)=>{\n        var _a, _b;\n        const currentSwipeOffset = action.offset || 0;\n        const swipeDuration = !currentSwipeOffset ? (_a = animation.navigation) !== null && _a !== void 0 ? _a : animation.swipe : animation.swipe;\n        const swipeEasing = !currentSwipeOffset && !isAnimationPlaying() ? animation.easing.navigation : animation.easing.swipe;\n        let { direction } = action;\n        const count = (_b = action.count) !== null && _b !== void 0 ? _b : 1;\n        let newSwipeState = SwipeState.ANIMATION;\n        let newSwipeAnimationDuration = swipeDuration * count;\n        if (!direction) {\n            const containerWidth = containerRect === null || containerRect === void 0 ? void 0 : containerRect.width;\n            const elapsedTime = action.duration || 0;\n            const expectedTime = containerWidth ? swipeDuration / containerWidth * Math.abs(currentSwipeOffset) : swipeDuration;\n            if (count !== 0) {\n                if (elapsedTime < expectedTime) {\n                    newSwipeAnimationDuration = newSwipeAnimationDuration / expectedTime * Math.max(elapsedTime, expectedTime / 5);\n                } else if (containerWidth) {\n                    newSwipeAnimationDuration = swipeDuration / containerWidth * (containerWidth - Math.abs(currentSwipeOffset));\n                }\n                direction = rtl(currentSwipeOffset) > 0 ? _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV : _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT;\n            } else {\n                newSwipeAnimationDuration = swipeDuration / 2;\n            }\n        }\n        let increment = 0;\n        if (direction === _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV) {\n            if (isSwipeValid(rtl(1))) {\n                increment = -count;\n            } else {\n                newSwipeState = SwipeState.NONE;\n                newSwipeAnimationDuration = swipeDuration;\n            }\n        } else if (direction === _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT) {\n            if (isSwipeValid(rtl(-1))) {\n                increment = count;\n            } else {\n                newSwipeState = SwipeState.NONE;\n                newSwipeAnimationDuration = swipeDuration;\n            }\n        }\n        newSwipeAnimationDuration = Math.round(newSwipeAnimationDuration);\n        cleanupSwipeOffset(()=>{\n            setSwipeOffset(0);\n            setSwipeState(SwipeState.NONE);\n        }, newSwipeAnimationDuration);\n        if (carouselRef.current) {\n            prepareAnimation({\n                rect: carouselRef.current.getBoundingClientRect(),\n                index: state.globalIndex\n            });\n        }\n        setSwipeState(newSwipeState);\n        publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_SWIPE, {\n            type: \"swipe\",\n            increment,\n            duration: newSwipeAnimationDuration,\n            easing: swipeEasing\n        });\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _a, _b;\n        if (((_a = state.animation) === null || _a === void 0 ? void 0 : _a.increment) && ((_b = state.animation) === null || _b === void 0 ? void 0 : _b.duration)) {\n            cleanupAnimationIncrement(()=>dispatch({\n                    type: \"swipe\",\n                    increment: 0\n                }), state.animation.duration);\n        }\n    }, [\n        state.animation,\n        dispatch,\n        cleanupAnimationIncrement\n    ]);\n    const swipeParams = [\n        subscribeSensors,\n        isSwipeValid,\n        (containerRect === null || containerRect === void 0 ? void 0 : containerRect.width) || 0,\n        animation.swipe,\n        ()=>setSwipeState(SwipeState.SWIPE),\n        (offset)=>setSwipeOffset(offset),\n        (offset, duration)=>swipe({\n                offset,\n                duration,\n                count: 1\n            }),\n        (offset)=>swipe({\n                offset,\n                count: 0\n            })\n    ];\n    const pullParams = [\n        ()=>{\n            if (closeOnPullDown) {\n                setSwipeState(SwipeState.PULL);\n            }\n        },\n        (offset)=>setPullOffset(offset),\n        (offset)=>pull(offset),\n        (offset)=>pull(offset, true)\n    ];\n    usePointerSwipe(...swipeParams, closeOnPullUp, closeOnPullDown, ...pullParams);\n    useWheelSwipe(swipeState, ...swipeParams);\n    const focusOnMount = useEventCallback(()=>{\n        if (controller.focus && getOwnerDocument().querySelector(`.${cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_PORTAL)} .${cssClass(cssContainerPrefix())}`)) {\n            focus();\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(focusOnMount, [\n        focusOnMount\n    ]);\n    const onViewCallback = useEventCallback(()=>{\n        var _a;\n        (_a = on.view) === null || _a === void 0 ? void 0 : _a.call(on, {\n            index: state.currentIndex\n        });\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(onViewCallback, [\n        state.globalIndex,\n        onViewCallback\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>cleanup(subscribe(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV, (action)=>swipe({\n                direction: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV,\n                ...action\n            })), subscribe(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT, (action)=>swipe({\n                direction: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT,\n                ...action\n            })), subscribe(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_SWIPE, (action)=>dispatch(action))), [\n        subscribe,\n        swipe,\n        dispatch\n    ]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            prev,\n            next,\n            close,\n            focus,\n            slideRect: containerRect ? computeSlideRect(containerRect, carousel.padding) : {\n                width: 0,\n                height: 0\n            },\n            containerRect: containerRect || {\n                width: 0,\n                height: 0\n            },\n            subscribeSensors,\n            containerRef,\n            setCarouselRef,\n            toolbarWidth,\n            setToolbarWidth\n        }), [\n        prev,\n        next,\n        close,\n        focus,\n        subscribeSensors,\n        containerRect,\n        containerRef,\n        setCarouselRef,\n        toolbarWidth,\n        setToolbarWidth,\n        carousel.padding\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(controller.ref, ()=>({\n            prev,\n            next,\n            close,\n            focus,\n            getLightboxProps,\n            getLightboxState\n        }), [\n        prev,\n        next,\n        close,\n        focus,\n        getLightboxProps,\n        getLightboxState\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: handleContainerRef,\n        className: clsx(cssClass(cssContainerPrefix()), cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FLEX_CENTER)),\n        style: {\n            ...swipeState === SwipeState.SWIPE ? {\n                [cssVar(\"swipe_offset\")]: `${Math.round(swipeOffset.current)}px`\n            } : null,\n            ...swipeState === SwipeState.PULL ? {\n                [cssVar(\"pull_offset\")]: `${Math.round(pullOffset.current)}px`,\n                [cssVar(\"pull_opacity\")]: `${pullOpacity.current}`\n            } : null,\n            ...controller.touchAction !== \"none\" ? {\n                [cssVar(\"controller_touch_action\")]: controller.touchAction\n            } : null,\n            ...styles.container\n        },\n        ...controller.aria ? {\n            role: \"presentation\",\n            \"aria-live\": \"polite\"\n        } : null,\n        tabIndex: -1,\n        ...registerSensors\n    }, containerRect && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ControllerContext.Provider, {\n        value: context\n    }, children, (_a = render.controls) === null || _a === void 0 ? void 0 : _a.call(render)));\n}\nconst ControllerModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CONTROLLER, Controller);\nfunction cssPrefix$2(value) {\n    return composePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CAROUSEL, value);\n}\nfunction cssSlidePrefix(value) {\n    return composePrefix(\"slide\", value);\n}\nfunction CarouselSlide({ slide, offset }) {\n    const containerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { currentIndex } = useLightboxState();\n    const { slideRect, close, focus } = useController();\n    const { render, carousel: { imageFit, imageProps }, on: { click: onClick }, controller: { closeOnBackdropClick }, styles: { slide: style } } = useLightboxProps();\n    const { getOwnerDocument } = useDocumentContext();\n    const offscreen = offset !== 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _a;\n        if (offscreen && ((_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.contains(getOwnerDocument().activeElement))) {\n            focus();\n        }\n    }, [\n        offscreen,\n        focus,\n        getOwnerDocument\n    ]);\n    const renderSlide = ()=>{\n        var _a, _b, _c, _d;\n        let rendered = (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, {\n            slide,\n            offset,\n            rect: slideRect\n        });\n        if (!rendered && isImageSlide(slide)) {\n            rendered = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ImageSlide, {\n                slide: slide,\n                offset: offset,\n                render: render,\n                rect: slideRect,\n                imageFit: imageFit,\n                imageProps: imageProps,\n                onClick: !offscreen ? ()=>onClick === null || onClick === void 0 ? void 0 : onClick({\n                        index: currentIndex\n                    }) : undefined\n            });\n        }\n        return rendered ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (_b = render.slideHeader) === null || _b === void 0 ? void 0 : _b.call(render, {\n            slide\n        }), ((_c = render.slideContainer) !== null && _c !== void 0 ? _c : ({ children })=>children)({\n            slide,\n            children: rendered\n        }), (_d = render.slideFooter) === null || _d === void 0 ? void 0 : _d.call(render, {\n            slide\n        })) : null;\n    };\n    const handleBackdropClick = (event)=>{\n        const container = containerRef.current;\n        const target = event.target instanceof HTMLElement ? event.target : undefined;\n        if (closeOnBackdropClick && target && container && (target === container || Array.from(container.children).find((x)=>x === target) && target.classList.contains(cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER)))) {\n            close();\n        }\n    };\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: containerRef,\n        className: clsx(cssClass(cssSlidePrefix()), !offscreen && cssClass(cssSlidePrefix(\"current\")), cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FLEX_CENTER)),\n        ...makeInertWhen(offscreen),\n        onClick: handleBackdropClick,\n        style: style\n    }, renderSlide());\n}\nfunction Placeholder() {\n    const style = useLightboxProps().styles.slide;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: cssClass(\"slide\"),\n        style: style\n    });\n}\nfunction Carousel({ carousel }) {\n    const { slides, currentIndex, globalIndex } = useLightboxState();\n    const { setCarouselRef } = useController();\n    const spacingValue = parseLengthPercentage(carousel.spacing);\n    const paddingValue = parseLengthPercentage(carousel.padding);\n    const preload = calculatePreload(carousel, slides, 1);\n    const items = [];\n    if (hasSlides(slides)) {\n        for(let index = currentIndex - preload; index <= currentIndex + preload; index += 1){\n            const slide = getSlide(slides, index);\n            const key = globalIndex - currentIndex + index;\n            const placeholder = carousel.finite && (index < 0 || index > slides.length - 1);\n            items.push(!placeholder ? {\n                key: [\n                    `${key}`,\n                    getSlideKey(slide)\n                ].filter(Boolean).join(\"|\"),\n                offset: index - currentIndex,\n                slide\n            } : {\n                key\n            });\n        }\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: setCarouselRef,\n        className: clsx(cssClass(cssPrefix$2()), items.length > 0 && cssClass(cssPrefix$2(\"with_slides\"))),\n        style: {\n            [`${cssVar(cssPrefix$2(\"slides_count\"))}`]: items.length,\n            [`${cssVar(cssPrefix$2(\"spacing_px\"))}`]: spacingValue.pixel || 0,\n            [`${cssVar(cssPrefix$2(\"spacing_percent\"))}`]: spacingValue.percent || 0,\n            [`${cssVar(cssPrefix$2(\"padding_px\"))}`]: paddingValue.pixel || 0,\n            [`${cssVar(cssPrefix$2(\"padding_percent\"))}`]: paddingValue.percent || 0\n        }\n    }, items.map(({ key, slide, offset })=>slide ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CarouselSlide, {\n            key: key,\n            slide: slide,\n            offset: offset\n        }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Placeholder, {\n            key: key\n        })));\n}\nconst CarouselModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CAROUSEL, Carousel);\nfunction useNavigationState() {\n    const { carousel } = useLightboxProps();\n    const { slides, currentIndex } = useLightboxState();\n    const prevDisabled = slides.length === 0 || carousel.finite && currentIndex === 0;\n    const nextDisabled = slides.length === 0 || carousel.finite && currentIndex === slides.length - 1;\n    return {\n        prevDisabled,\n        nextDisabled\n    };\n}\nfunction useKeyboardNavigation(subscribeSensors) {\n    var _a;\n    const isRTL = useRTL();\n    const { publish } = useEvents();\n    const { animation } = useLightboxProps();\n    const { prevDisabled, nextDisabled } = useNavigationState();\n    const throttle = ((_a = animation.navigation) !== null && _a !== void 0 ? _a : animation.swipe) / 2;\n    const prev = useThrottle(()=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV), throttle);\n    const next = useThrottle(()=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT), throttle);\n    const handleKeyDown = useEventCallback((event)=>{\n        switch(event.key){\n            case _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ESCAPE:\n                publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE);\n                break;\n            case _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ARROW_LEFT:\n                if (!(isRTL ? nextDisabled : prevDisabled)) (isRTL ? next : prev)();\n                break;\n            case _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ARROW_RIGHT:\n                if (!(isRTL ? prevDisabled : nextDisabled)) (isRTL ? prev : next)();\n                break;\n            default:\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_DOWN, handleKeyDown), [\n        subscribeSensors,\n        handleKeyDown\n    ]);\n}\nfunction NavigationButton({ label, icon, renderIcon, action, onClick, disabled, style }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(IconButton, {\n        label: label,\n        icon: icon,\n        renderIcon: renderIcon,\n        className: cssClass(`navigation_${action}`),\n        disabled: disabled,\n        onClick: onClick,\n        style: style,\n        ...useLoseFocus(useController().focus, disabled)\n    });\n}\nfunction Navigation({ render: { buttonPrev, buttonNext, iconPrev, iconNext }, styles }) {\n    const { prev, next, subscribeSensors } = useController();\n    const { prevDisabled, nextDisabled } = useNavigationState();\n    useKeyboardNavigation(subscribeSensors);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, buttonPrev ? buttonPrev() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(NavigationButton, {\n        label: \"Previous\",\n        action: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV,\n        icon: PreviousIcon,\n        renderIcon: iconPrev,\n        style: styles.navigationPrev,\n        disabled: prevDisabled,\n        onClick: prev\n    }), buttonNext ? buttonNext() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(NavigationButton, {\n        label: \"Next\",\n        action: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT,\n        icon: NextIcon,\n        renderIcon: iconNext,\n        style: styles.navigationNext,\n        disabled: nextDisabled,\n        onClick: next\n    }));\n}\nconst NavigationModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_NAVIGATION, Navigation);\nconst noScroll = cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL);\nconst noScrollPadding = cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL_PADDING);\nfunction isHTMLElement(element) {\n    return \"style\" in element;\n}\nfunction padScrollbar(element, padding, rtl) {\n    const styles = window.getComputedStyle(element);\n    const property = rtl ? \"padding-left\" : \"padding-right\";\n    const computedValue = rtl ? styles.paddingLeft : styles.paddingRight;\n    const originalValue = element.style.getPropertyValue(property);\n    element.style.setProperty(property, `${(parseInt(computedValue) || 0) + padding}px`);\n    return ()=>{\n        if (originalValue) {\n            element.style.setProperty(property, originalValue);\n        } else {\n            element.style.removeProperty(property);\n        }\n    };\n}\nfunction NoScroll({ noScroll: { disabled }, children }) {\n    const rtl = useRTL();\n    const { getOwnerDocument, getOwnerWindow } = useDocumentContext();\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (disabled) return ()=>{};\n        const cleanup = [];\n        const ownerWindow = getOwnerWindow();\n        const { body, documentElement } = getOwnerDocument();\n        const scrollbar = Math.round(ownerWindow.innerWidth - documentElement.clientWidth);\n        if (scrollbar > 0) {\n            cleanup.push(padScrollbar(body, scrollbar, rtl));\n            const elements = body.getElementsByTagName(\"*\");\n            for(let i = 0; i < elements.length; i += 1){\n                const element = elements[i];\n                if (isHTMLElement(element) && ownerWindow.getComputedStyle(element).getPropertyValue(\"position\") === \"fixed\" && !element.classList.contains(noScrollPadding)) {\n                    cleanup.push(padScrollbar(element, scrollbar, rtl));\n                }\n            }\n        }\n        body.classList.add(noScroll);\n        return ()=>{\n            body.classList.remove(noScroll);\n            cleanup.forEach((clean)=>clean());\n        };\n    }, [\n        rtl,\n        disabled,\n        getOwnerDocument,\n        getOwnerWindow\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children);\n}\nconst NoScrollModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_NO_SCROLL, NoScroll);\nfunction cssPrefix$1(value) {\n    return composePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_PORTAL, value);\n}\nfunction setAttribute(element, attribute, value) {\n    const previousValue = element.getAttribute(attribute);\n    element.setAttribute(attribute, value);\n    return ()=>{\n        if (previousValue) {\n            element.setAttribute(attribute, previousValue);\n        } else {\n            element.removeAttribute(attribute);\n        }\n    };\n}\nfunction Portal({ children, animation, styles, className, on, portal, close }) {\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const cleanup = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n    const restoreFocus = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { setTimeout } = useTimeouts();\n    const { subscribe } = useEvents();\n    const reduceMotion = useMotionPreference();\n    const animationDuration = !reduceMotion ? animation.fade : 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n        return ()=>{\n            setMounted(false);\n            setVisible(false);\n        };\n    }, []);\n    const handleCleanup = useEventCallback(()=>{\n        cleanup.current.forEach((clean)=>clean());\n        cleanup.current = [];\n    });\n    const handleClose = useEventCallback(()=>{\n        var _a;\n        setVisible(false);\n        handleCleanup();\n        (_a = on.exiting) === null || _a === void 0 ? void 0 : _a.call(on);\n        setTimeout(()=>{\n            var _a;\n            (_a = on.exited) === null || _a === void 0 ? void 0 : _a.call(on);\n            close();\n        }, animationDuration);\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>subscribe(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE, handleClose), [\n        subscribe,\n        handleClose\n    ]);\n    const handleEnter = useEventCallback((node)=>{\n        var _a, _b, _c;\n        node.scrollTop;\n        setVisible(true);\n        (_a = on.entering) === null || _a === void 0 ? void 0 : _a.call(on);\n        const elements = (_c = (_b = node.parentNode) === null || _b === void 0 ? void 0 : _b.children) !== null && _c !== void 0 ? _c : [];\n        for(let i = 0; i < elements.length; i += 1){\n            const element = elements[i];\n            if ([\n                \"TEMPLATE\",\n                \"SCRIPT\",\n                \"STYLE\"\n            ].indexOf(element.tagName) === -1 && element !== node) {\n                cleanup.current.push(setAttribute(element, \"inert\", \"\"));\n                cleanup.current.push(setAttribute(element, \"aria-hidden\", \"true\"));\n            }\n        }\n        cleanup.current.push(()=>{\n            var _a, _b;\n            (_b = (_a = restoreFocus.current) === null || _a === void 0 ? void 0 : _a.focus) === null || _b === void 0 ? void 0 : _b.call(_a);\n        });\n        setTimeout(()=>{\n            var _a;\n            (_a = on.entered) === null || _a === void 0 ? void 0 : _a.call(on);\n        }, animationDuration);\n    });\n    const handleRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        if (node) {\n            handleEnter(node);\n        } else {\n            handleCleanup();\n        }\n    }, [\n        handleEnter,\n        handleCleanup\n    ]);\n    return mounted ? /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxRoot, {\n        ref: handleRef,\n        className: clsx(className, cssClass(cssPrefix$1()), cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL_PADDING), visible && cssClass(cssPrefix$1(\"open\"))),\n        role: \"presentation\",\n        \"aria-live\": \"polite\",\n        style: {\n            ...animation.fade !== LightboxDefaultProps.animation.fade ? {\n                [cssVar(\"fade_animation_duration\")]: `${animationDuration}ms`\n            } : null,\n            ...animation.easing.fade !== LightboxDefaultProps.animation.easing.fade ? {\n                [cssVar(\"fade_animation_timing_function\")]: animation.easing.fade\n            } : null,\n            ...styles.root\n        },\n        onFocus: (event)=>{\n            if (!restoreFocus.current) {\n                restoreFocus.current = event.relatedTarget;\n            }\n        }\n    }, children), portal.root || document.body) : null;\n}\nconst PortalModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_PORTAL, Portal);\nfunction Root({ children }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children);\n}\nconst RootModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_ROOT, Root);\nfunction cssPrefix(value) {\n    return composePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_TOOLBAR, value);\n}\nfunction Toolbar({ toolbar: { buttons }, render: { buttonClose, iconClose }, styles }) {\n    const { close, setToolbarWidth } = useController();\n    const { setContainerRef, containerRect } = useContainerRect();\n    useLayoutEffect(()=>{\n        setToolbarWidth(containerRect === null || containerRect === void 0 ? void 0 : containerRect.width);\n    }, [\n        setToolbarWidth,\n        containerRect === null || containerRect === void 0 ? void 0 : containerRect.width\n    ]);\n    const renderCloseButton = ()=>{\n        if (buttonClose) return buttonClose();\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(IconButton, {\n            key: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE,\n            label: \"Close\",\n            icon: CloseIcon,\n            renderIcon: iconClose,\n            onClick: close\n        });\n    };\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: setContainerRef,\n        style: styles.toolbar,\n        className: cssClass(cssPrefix())\n    }, buttons === null || buttons === void 0 ? void 0 : buttons.map((button)=>button === _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE ? renderCloseButton() : button));\n}\nconst ToolbarModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_TOOLBAR, Toolbar);\nfunction renderNode(node, props) {\n    var _a;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(node.module.component, {\n        key: node.module.name,\n        ...props\n    }, (_a = node.children) === null || _a === void 0 ? void 0 : _a.map((child)=>renderNode(child, props)));\n}\nfunction mergeAnimation(defaultAnimation, animation = {}) {\n    const { easing: defaultAnimationEasing, ...restDefaultAnimation } = defaultAnimation;\n    const { easing, ...restAnimation } = animation;\n    return {\n        easing: {\n            ...defaultAnimationEasing,\n            ...easing\n        },\n        ...restDefaultAnimation,\n        ...restAnimation\n    };\n}\nfunction Lightbox({ carousel, animation, render, toolbar, controller, noScroll, on, plugins, slides, index, ...restProps }) {\n    const { animation: defaultAnimation, carousel: defaultCarousel, render: defaultRender, toolbar: defaultToolbar, controller: defaultController, noScroll: defaultNoScroll, on: defaultOn, slides: defaultSlides, index: defaultIndex, plugins: defaultPlugins, ...restDefaultProps } = LightboxDefaultProps;\n    const { config, augmentation } = withPlugins([\n        createNode(PortalModule, [\n            createNode(NoScrollModule, [\n                createNode(ControllerModule, [\n                    createNode(CarouselModule),\n                    createNode(ToolbarModule),\n                    createNode(NavigationModule)\n                ])\n            ])\n        ])\n    ], plugins || defaultPlugins);\n    const props = augmentation({\n        animation: mergeAnimation(defaultAnimation, animation),\n        carousel: {\n            ...defaultCarousel,\n            ...carousel\n        },\n        render: {\n            ...defaultRender,\n            ...render\n        },\n        toolbar: {\n            ...defaultToolbar,\n            ...toolbar\n        },\n        controller: {\n            ...defaultController,\n            ...controller\n        },\n        noScroll: {\n            ...defaultNoScroll,\n            ...noScroll\n        },\n        on: {\n            ...defaultOn,\n            ...on\n        },\n        ...restDefaultProps,\n        ...restProps\n    });\n    if (!props.open) return null;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxPropsProvider, {\n        ...props\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxStateProvider, {\n        slides: slides || defaultSlides,\n        index: parseInt(index || defaultIndex)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(TimeoutsProvider, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(EventsProvider, null, renderNode(createNode(RootModule, config), props)))));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/yet-another-react-lightbox/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/yet-another-react-lightbox/dist/plugins/zoom/index.js":
/*!****************************************************************************!*\
  !*** ./node_modules/yet-another-react-lightbox/dist/plugins/zoom/index.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Zoom)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../index.js */ \"(ssr)/./node_modules/yet-another-react-lightbox/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../types.js */ \"(ssr)/./node_modules/yet-another-react-lightbox/dist/types.js\");\n\n\n\n\nconst defaultZoomProps = {\n    maxZoomPixelRatio: 1,\n    zoomInMultiplier: 2,\n    doubleTapDelay: 300,\n    doubleClickDelay: 500,\n    doubleClickMaxStops: 2,\n    keyboardMoveDistance: 50,\n    wheelZoomDistanceFactor: 100,\n    pinchZoomDistanceFactor: 100,\n    scrollToZoom: false,\n};\nconst resolveZoomProps = (zoom) => ({\n    ...defaultZoomProps,\n    ...zoom,\n});\n\nfunction useZoomAnimation(zoom, offsetX, offsetY, zoomWrapperRef) {\n    const zoomAnimation = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const zoomAnimationStart = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const { zoom: zoomAnimationDuration } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)().animation;\n    const reduceMotion = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useMotionPreference)();\n    const playZoomAnimation = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        var _a, _b, _c;\n        (_a = zoomAnimation.current) === null || _a === void 0 ? void 0 : _a.cancel();\n        zoomAnimation.current = undefined;\n        if (zoomAnimationStart.current && (zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current)) {\n            try {\n                zoomAnimation.current = (_c = (_b = zoomWrapperRef.current).animate) === null || _c === void 0 ? void 0 : _c.call(_b, [\n                    { transform: zoomAnimationStart.current },\n                    { transform: `scale(${zoom}) translateX(${offsetX}px) translateY(${offsetY}px)` },\n                ], {\n                    duration: !reduceMotion ? (zoomAnimationDuration !== null && zoomAnimationDuration !== void 0 ? zoomAnimationDuration : 500) : 0,\n                    easing: zoomAnimation.current ? \"ease-out\" : \"ease-in-out\",\n                });\n            }\n            catch (err) {\n                console.error(err);\n            }\n            zoomAnimationStart.current = undefined;\n            if (zoomAnimation.current) {\n                zoomAnimation.current.onfinish = () => {\n                    zoomAnimation.current = undefined;\n                };\n            }\n        }\n    });\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(playZoomAnimation, [zoom, offsetX, offsetY, playZoomAnimation]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n        zoomAnimationStart.current = (zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current)\n            ? window.getComputedStyle(zoomWrapperRef.current).transform\n            : undefined;\n    }, [zoomWrapperRef]);\n}\n\nfunction useZoomCallback(zoom, disabled) {\n    const { on } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const onZoomCallback = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        var _a;\n        if (!disabled) {\n            (_a = on.zoom) === null || _a === void 0 ? void 0 : _a.call(on, { zoom });\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(onZoomCallback, [zoom, onZoomCallback]);\n}\n\nfunction useZoomProps() {\n    const { zoom } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    return resolveZoomProps(zoom);\n}\n\nfunction useZoomImageRect(slideRect, imageDimensions) {\n    var _a, _b;\n    let imageRect = { width: 0, height: 0 };\n    let maxImageRect = { width: 0, height: 0 };\n    const { currentSlide } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    const { imageFit } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)().carousel;\n    const { maxZoomPixelRatio } = useZoomProps();\n    if (slideRect && currentSlide) {\n        const slide = { ...currentSlide, ...imageDimensions };\n        if ((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(slide)) {\n            const cover = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageFitCover)(slide, imageFit);\n            const width = Math.max(...(((_a = slide.srcSet) === null || _a === void 0 ? void 0 : _a.map((x) => x.width)) || []).concat(slide.width ? [slide.width] : []));\n            const height = Math.max(...(((_b = slide.srcSet) === null || _b === void 0 ? void 0 : _b.map((x) => x.height)) || []).concat(slide.height ? [slide.height] : []));\n            if (width > 0 && height > 0 && slideRect.width > 0 && slideRect.height > 0) {\n                maxImageRect = cover\n                    ? {\n                        width: Math.round(Math.min(width, (slideRect.width / slideRect.height) * height)),\n                        height: Math.round(Math.min(height, (slideRect.height / slideRect.width) * width)),\n                    }\n                    : { width, height };\n                maxImageRect = {\n                    width: maxImageRect.width * maxZoomPixelRatio,\n                    height: maxImageRect.height * maxZoomPixelRatio,\n                };\n                imageRect = cover\n                    ? {\n                        width: Math.min(slideRect.width, maxImageRect.width, width),\n                        height: Math.min(slideRect.height, maxImageRect.height, height),\n                    }\n                    : {\n                        width: Math.round(Math.min(slideRect.width, (slideRect.height / height) * width, width)),\n                        height: Math.round(Math.min(slideRect.height, (slideRect.width / width) * height, height)),\n                    };\n            }\n        }\n    }\n    const maxZoom = imageRect.width ? Math.max((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.round)(maxImageRect.width / imageRect.width, 5), 1) : 1;\n    return { imageRect, maxZoom };\n}\n\nfunction distance(pointerA, pointerB) {\n    return ((pointerA.clientX - pointerB.clientX) ** 2 + (pointerA.clientY - pointerB.clientY) ** 2) ** 0.5;\n}\nfunction scaleZoom(value, delta, factor = 100, clamp = 2) {\n    return value * Math.min(1 + Math.abs(delta / factor), clamp) ** Math.sign(delta);\n}\nfunction useZoomSensors(zoom, maxZoom, disabled, changeZoom, changeOffsets, zoomWrapperRef) {\n    const activePointers = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n    const lastPointerDown = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pinchZoomDistance = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const { globalIndex } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    const { getOwnerWindow } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useDocumentContext)();\n    const { containerRef, subscribeSensors } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const { keyboardMoveDistance, zoomInMultiplier, wheelZoomDistanceFactor, scrollToZoom, doubleTapDelay, doubleClickDelay, doubleClickMaxStops, pinchZoomDistanceFactor, } = useZoomProps();\n    const translateCoordinates = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => {\n        if (containerRef.current) {\n            const { pageX, pageY } = event;\n            const { scrollX, scrollY } = getOwnerWindow();\n            const { left, top, width, height } = containerRef.current.getBoundingClientRect();\n            return [pageX - left - scrollX - width / 2, pageY - top - scrollY - height / 2];\n        }\n        return [];\n    }, [containerRef, getOwnerWindow]);\n    const onKeyDown = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((event) => {\n        const preventDefault = () => {\n            event.preventDefault();\n            event.stopPropagation();\n        };\n        if (zoom > 1) {\n            const move = (deltaX, deltaY) => {\n                preventDefault();\n                changeOffsets(deltaX, deltaY);\n            };\n            if (event.key === \"ArrowDown\") {\n                move(0, keyboardMoveDistance);\n            }\n            else if (event.key === \"ArrowUp\") {\n                move(0, -keyboardMoveDistance);\n            }\n            else if (event.key === \"ArrowLeft\") {\n                move(-keyboardMoveDistance, 0);\n            }\n            else if (event.key === \"ArrowRight\") {\n                move(keyboardMoveDistance, 0);\n            }\n        }\n        const handleChangeZoom = (zoomValue) => {\n            preventDefault();\n            changeZoom(zoomValue);\n        };\n        const hasMeta = () => event.getModifierState(\"Meta\");\n        if (event.key === \"+\" || (event.key === \"=\" && hasMeta())) {\n            handleChangeZoom(zoom * zoomInMultiplier);\n        }\n        else if (event.key === \"-\" || (event.key === \"_\" && hasMeta())) {\n            handleChangeZoom(zoom / zoomInMultiplier);\n        }\n        else if (event.key === \"0\" && hasMeta()) {\n            handleChangeZoom(1);\n        }\n    });\n    const onWheel = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((event) => {\n        if (event.ctrlKey || scrollToZoom) {\n            if (Math.abs(event.deltaY) > Math.abs(event.deltaX)) {\n                event.stopPropagation();\n                changeZoom(scaleZoom(zoom, -event.deltaY, wheelZoomDistanceFactor), true, ...translateCoordinates(event));\n                return;\n            }\n        }\n        if (zoom > 1) {\n            event.stopPropagation();\n            if (!scrollToZoom) {\n                changeOffsets(event.deltaX, event.deltaY);\n            }\n        }\n    });\n    const clearPointer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => {\n        const pointers = activePointers.current;\n        pointers.splice(0, pointers.length, ...pointers.filter((p) => p.pointerId !== event.pointerId));\n    }, []);\n    const replacePointer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => {\n        clearPointer(event);\n        event.persist();\n        activePointers.current.push(event);\n    }, [clearPointer]);\n    const onPointerDown = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((event) => {\n        var _a;\n        const pointers = activePointers.current;\n        if ((event.pointerType === \"mouse\" && event.buttons > 1) ||\n            !((_a = zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current) === null || _a === void 0 ? void 0 : _a.contains(event.target))) {\n            return;\n        }\n        if (zoom > 1) {\n            event.stopPropagation();\n        }\n        const { timeStamp } = event;\n        if (pointers.length === 0 &&\n            timeStamp - lastPointerDown.current < (event.pointerType === \"touch\" ? doubleTapDelay : doubleClickDelay)) {\n            lastPointerDown.current = 0;\n            changeZoom(zoom !== maxZoom ? zoom * Math.max(maxZoom ** (1 / doubleClickMaxStops), zoomInMultiplier) : 1, false, ...translateCoordinates(event));\n        }\n        else {\n            lastPointerDown.current = timeStamp;\n        }\n        replacePointer(event);\n        if (pointers.length === 2) {\n            pinchZoomDistance.current = distance(pointers[0], pointers[1]);\n        }\n    });\n    const onPointerMove = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((event) => {\n        const pointers = activePointers.current;\n        const activePointer = pointers.find((p) => p.pointerId === event.pointerId);\n        if (pointers.length === 2 && pinchZoomDistance.current) {\n            event.stopPropagation();\n            replacePointer(event);\n            const currentDistance = distance(pointers[0], pointers[1]);\n            const delta = currentDistance - pinchZoomDistance.current;\n            if (Math.abs(delta) > 0) {\n                changeZoom(scaleZoom(zoom, delta, pinchZoomDistanceFactor), true, ...pointers\n                    .map((x) => translateCoordinates(x))\n                    .reduce((acc, coordinate) => coordinate.map((x, i) => acc[i] + x / 2)));\n                pinchZoomDistance.current = currentDistance;\n            }\n            return;\n        }\n        if (zoom > 1) {\n            event.stopPropagation();\n            if (activePointer) {\n                if (pointers.length === 1) {\n                    changeOffsets((activePointer.clientX - event.clientX) / zoom, (activePointer.clientY - event.clientY) / zoom);\n                }\n                replacePointer(event);\n            }\n        }\n    });\n    const onPointerUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => {\n        const pointers = activePointers.current;\n        if (pointers.length === 2 && pointers.find((p) => p.pointerId === event.pointerId)) {\n            pinchZoomDistance.current = undefined;\n        }\n        clearPointer(event);\n    }, [clearPointer]);\n    const cleanupSensors = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n        const pointers = activePointers.current;\n        pointers.splice(0, pointers.length);\n        lastPointerDown.current = 0;\n        pinchZoomDistance.current = undefined;\n    }, []);\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.usePointerEvents)(subscribeSensors, onPointerDown, onPointerMove, onPointerUp, disabled);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(cleanupSensors, [globalIndex, cleanupSensors]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (!disabled) {\n            return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cleanup)(cleanupSensors, subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_DOWN, onKeyDown), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_WHEEL, onWheel));\n        }\n        return () => { };\n    }, [disabled, subscribeSensors, cleanupSensors, onKeyDown, onWheel]);\n}\n\nfunction useZoomState(imageRect, maxZoom, zoomWrapperRef) {\n    const [zoom, setZoom] = react__WEBPACK_IMPORTED_MODULE_0__.useState(1);\n    const [offsetX, setOffsetX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [offsetY, setOffsetY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const animate = useZoomAnimation(zoom, offsetX, offsetY, zoomWrapperRef);\n    const { currentSlide, globalIndex } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    const { containerRect, slideRect } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const { zoomInMultiplier } = useZoomProps();\n    const currentSource = currentSlide && (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(currentSlide) ? currentSlide.src : undefined;\n    const disabled = !currentSource || !(zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current);\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n        setZoom(1);\n        setOffsetX(0);\n        setOffsetY(0);\n    }, [globalIndex, currentSource]);\n    const changeOffsets = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((dx, dy, targetZoom) => {\n        const newZoom = targetZoom || zoom;\n        const newOffsetX = offsetX - (dx || 0);\n        const newOffsetY = offsetY - (dy || 0);\n        const maxOffsetX = (imageRect.width * newZoom - slideRect.width) / 2 / newZoom;\n        const maxOffsetY = (imageRect.height * newZoom - slideRect.height) / 2 / newZoom;\n        setOffsetX(Math.min(Math.abs(newOffsetX), Math.max(maxOffsetX, 0)) * Math.sign(newOffsetX));\n        setOffsetY(Math.min(Math.abs(newOffsetY), Math.max(maxOffsetY, 0)) * Math.sign(newOffsetY));\n    }, [zoom, offsetX, offsetY, slideRect, imageRect.width, imageRect.height]);\n    const changeZoom = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((targetZoom, rapid, dx, dy) => {\n        const newZoom = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.round)(Math.min(Math.max(targetZoom + 0.001 < maxZoom ? targetZoom : maxZoom, 1), maxZoom), 5);\n        if (newZoom === zoom)\n            return;\n        if (!rapid) {\n            animate();\n        }\n        changeOffsets(dx ? dx * (1 / zoom - 1 / newZoom) : 0, dy ? dy * (1 / zoom - 1 / newZoom) : 0, newZoom);\n        setZoom(newZoom);\n    }, [zoom, maxZoom, changeOffsets, animate]);\n    const handleControllerRectChange = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        if (zoom > 1) {\n            if (zoom > maxZoom) {\n                changeZoom(maxZoom, true);\n            }\n            changeOffsets();\n        }\n    });\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(handleControllerRectChange, [containerRect.width, containerRect.height, handleControllerRectChange]);\n    const zoomIn = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => changeZoom(zoom * zoomInMultiplier), [zoom, zoomInMultiplier, changeZoom]);\n    const zoomOut = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => changeZoom(zoom / zoomInMultiplier), [zoom, zoomInMultiplier, changeZoom]);\n    return { zoom, offsetX, offsetY, disabled, changeOffsets, changeZoom, zoomIn, zoomOut };\n}\n\nconst ZoomControllerContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useZoom = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.makeUseContext)(\"useZoom\", \"ZoomControllerContext\", ZoomControllerContext);\nfunction ZoomContextProvider({ children }) {\n    const [zoomWrapper, setZoomWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const { slideRect } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const { imageRect, maxZoom } = useZoomImageRect(slideRect, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.imageDimensions);\n    const { zoom, offsetX, offsetY, disabled, changeZoom, changeOffsets, zoomIn, zoomOut } = useZoomState(imageRect, maxZoom, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.zoomWrapperRef);\n    useZoomCallback(zoom, disabled);\n    useZoomSensors(zoom, maxZoom, disabled, changeZoom, changeOffsets, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.zoomWrapperRef);\n    const zoomRef = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ zoom, maxZoom, offsetX, offsetY, disabled, zoomIn, zoomOut, changeZoom }), [zoom, maxZoom, offsetX, offsetY, disabled, zoomIn, zoomOut, changeZoom]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(useZoomProps().ref, () => zoomRef, [zoomRef]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ ...zoomRef, setZoomWrapper }), [zoomRef, setZoomWrapper]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomControllerContext.Provider, { value: context }, children);\n}\n\nconst ZoomInIcon = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"ZoomIn\", react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\" }),\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z\" })));\nconst ZoomOutIcon = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"ZoomOut\", react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z\" }));\nconst ZoomButton = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function ZoomButton({ zoomIn, onLoseFocus }, ref) {\n    const wasEnabled = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const wasFocused = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const { zoom, maxZoom, zoomIn: zoomInCallback, zoomOut: zoomOutCallback, disabled: zoomDisabled } = useZoom();\n    const { render } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const disabled = zoomDisabled || (zoomIn ? zoom >= maxZoom : zoom <= 1);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (disabled && wasEnabled.current && wasFocused.current) {\n            onLoseFocus();\n        }\n        if (!disabled) {\n            wasEnabled.current = true;\n        }\n    }, [disabled, onLoseFocus]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.IconButton, { ref: ref, disabled: disabled, label: zoomIn ? \"Zoom in\" : \"Zoom out\", icon: zoomIn ? ZoomInIcon : ZoomOutIcon, renderIcon: zoomIn ? render.iconZoomIn : render.iconZoomOut, onClick: zoomIn ? zoomInCallback : zoomOutCallback, onFocus: () => {\n            wasFocused.current = true;\n        }, onBlur: () => {\n            wasFocused.current = false;\n        } }));\n});\n\nfunction ZoomButtonsGroup() {\n    const zoomInRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const zoomOutRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { focus } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const focusSibling = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((sibling) => {\n        var _a, _b;\n        if (!((_a = sibling.current) === null || _a === void 0 ? void 0 : _a.disabled)) {\n            (_b = sibling.current) === null || _b === void 0 ? void 0 : _b.focus();\n        }\n        else {\n            focus();\n        }\n    }, [focus]);\n    const focusZoomIn = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => focusSibling(zoomInRef), [focusSibling]);\n    const focusZoomOut = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => focusSibling(zoomOutRef), [focusSibling]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomButton, { zoomIn: true, ref: zoomInRef, onLoseFocus: focusZoomOut }),\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomButton, { ref: zoomOutRef, onLoseFocus: focusZoomIn })));\n}\n\nfunction ZoomToolbarControl() {\n    const { render } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const zoomRef = useZoom();\n    if (render.buttonZoom) {\n        return react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render.buttonZoom(zoomRef));\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomButtonsGroup, null);\n}\n\nfunction isResponsiveImageSlide(slide) {\n    var _a;\n    return (((_a = slide.srcSet) === null || _a === void 0 ? void 0 : _a.length) || 0) > 0;\n}\nfunction reducer({ current, preload }, { type, source }) {\n    switch (type) {\n        case \"fetch\":\n            if (!current) {\n                return { current: source };\n            }\n            return { current, preload: source };\n        case \"done\":\n            if (source === preload) {\n                return { current: source };\n            }\n            return { current, preload };\n        default:\n            throw new Error(_types_js__WEBPACK_IMPORTED_MODULE_2__.UNKNOWN_ACTION_TYPE);\n    }\n}\nfunction ResponsiveImage(props) {\n    var _a, _b;\n    const [{ current, preload }, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(reducer, {});\n    const { slide: image, rect, imageFit, render, interactive } = props;\n    const srcSet = image.srcSet.sort((a, b) => a.width - b.width);\n    const width = (_a = image.width) !== null && _a !== void 0 ? _a : srcSet[srcSet.length - 1].width;\n    const height = (_b = image.height) !== null && _b !== void 0 ? _b : srcSet[srcSet.length - 1].height;\n    const cover = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageFitCover)(image, imageFit);\n    const maxWidth = Math.max(...srcSet.map((x) => x.width));\n    const targetWidth = Math.min((cover ? Math.max : Math.min)(rect.width, width * (rect.height / height)), maxWidth);\n    const pixelDensity = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.devicePixelRatio)();\n    const handleResize = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        var _a;\n        const targetSource = (_a = srcSet.find((x) => x.width >= targetWidth * pixelDensity)) !== null && _a !== void 0 ? _a : srcSet[srcSet.length - 1];\n        if (!current || srcSet.findIndex((x) => x.src === current) < srcSet.findIndex((x) => x === targetSource)) {\n            dispatch({ type: \"fetch\", source: targetSource.src });\n        }\n    });\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(handleResize, [rect.width, rect.height, pixelDensity, handleResize]);\n    const handlePreload = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((currentPreload) => dispatch({ type: \"done\", source: currentPreload }));\n    const style = {\n        WebkitTransform: !interactive ? \"translateZ(0)\" : \"initial\",\n    };\n    if (!cover) {\n        Object.assign(style, rect.width / rect.height < width / height ? { width: \"100%\", height: \"auto\" } : { width: \"auto\", height: \"100%\" });\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n        preload && preload !== current && (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.ImageSlide, { key: \"preload\", ...props, slide: { ...image, src: preload, srcSet: undefined }, style: { position: \"absolute\", visibility: \"hidden\", ...style }, onLoad: () => handlePreload(preload), render: {\n                ...render,\n                iconLoading: () => null,\n                iconError: () => null,\n            } })),\n        current && (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.ImageSlide, { key: \"current\", ...props, slide: { ...image, src: current, srcSet: undefined }, style: style }))));\n}\n\nfunction ZoomWrapper({ render, slide, offset, rect }) {\n    var _a;\n    const [imageDimensions, setImageDimensions] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const zoomWrapperRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { zoom, maxZoom, offsetX, offsetY, setZoomWrapper } = useZoom();\n    const interactive = zoom > 1;\n    const { carousel, on } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const { currentIndex } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n        if (offset === 0) {\n            setZoomWrapper({ zoomWrapperRef, imageDimensions });\n            return () => setZoomWrapper(undefined);\n        }\n        return () => { };\n    }, [offset, imageDimensions, setZoomWrapper]);\n    let rendered = (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, { slide, offset, rect, zoom, maxZoom });\n    if (!rendered && (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(slide)) {\n        const slideProps = {\n            slide,\n            offset,\n            rect,\n            render,\n            imageFit: carousel.imageFit,\n            imageProps: carousel.imageProps,\n            onClick: offset === 0 ? () => { var _a; return (_a = on.click) === null || _a === void 0 ? void 0 : _a.call(on, { index: currentIndex }); } : undefined,\n        };\n        rendered = isResponsiveImageSlide(slide) ? (react__WEBPACK_IMPORTED_MODULE_0__.createElement(ResponsiveImage, { ...slideProps, slide: slide, interactive: interactive, rect: offset === 0 ? { width: rect.width * zoom, height: rect.height * zoom } : rect })) : (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.ImageSlide, { onLoad: (img) => setImageDimensions({ width: img.naturalWidth, height: img.naturalHeight }), ...slideProps }));\n    }\n    if (!rendered)\n        return null;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: zoomWrapperRef, className: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.clsx)((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FULLSIZE), (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FLEX_CENTER), (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER), interactive && (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER_INTERACTIVE)), style: offset === 0 ? { transform: `scale(${zoom}) translateX(${offsetX}px) translateY(${offsetY}px)` } : undefined }, rendered));\n}\n\nconst Zoom = ({ augment, addModule }) => {\n    augment(({ zoom: zoomProps, toolbar, render, controller, ...restProps }) => {\n        const zoom = resolveZoomProps(zoomProps);\n        return {\n            zoom,\n            toolbar: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.addToolbarButton)(toolbar, _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_ZOOM, react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomToolbarControl, null)),\n            render: {\n                ...render,\n                slide: (props) => { var _a; return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(props.slide) ? react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomWrapper, { render: render, ...props }) : (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, props); },\n            },\n            controller: { ...controller, preventDefaultWheelY: zoom.scrollToZoom },\n            ...restProps,\n        };\n    });\n    addModule((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createModule)(_types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_ZOOM, ZoomContextProvider));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/yet-another-react-lightbox/dist/plugins/zoom/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/yet-another-react-lightbox/dist/types.js":
/*!***************************************************************!*\
  !*** ./node_modules/yet-another-react-lightbox/dist/types.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_CLOSE: () => (/* binding */ ACTION_CLOSE),\n/* harmony export */   ACTION_NEXT: () => (/* binding */ ACTION_NEXT),\n/* harmony export */   ACTION_PREV: () => (/* binding */ ACTION_PREV),\n/* harmony export */   ACTION_SWIPE: () => (/* binding */ ACTION_SWIPE),\n/* harmony export */   ACTIVE_SLIDE_COMPLETE: () => (/* binding */ ACTIVE_SLIDE_COMPLETE),\n/* harmony export */   ACTIVE_SLIDE_ERROR: () => (/* binding */ ACTIVE_SLIDE_ERROR),\n/* harmony export */   ACTIVE_SLIDE_LOADING: () => (/* binding */ ACTIVE_SLIDE_LOADING),\n/* harmony export */   ACTIVE_SLIDE_PLAYING: () => (/* binding */ ACTIVE_SLIDE_PLAYING),\n/* harmony export */   CLASS_FLEX_CENTER: () => (/* binding */ CLASS_FLEX_CENTER),\n/* harmony export */   CLASS_FULLSIZE: () => (/* binding */ CLASS_FULLSIZE),\n/* harmony export */   CLASS_NO_SCROLL: () => (/* binding */ CLASS_NO_SCROLL),\n/* harmony export */   CLASS_NO_SCROLL_PADDING: () => (/* binding */ CLASS_NO_SCROLL_PADDING),\n/* harmony export */   CLASS_SLIDE_WRAPPER: () => (/* binding */ CLASS_SLIDE_WRAPPER),\n/* harmony export */   CLASS_SLIDE_WRAPPER_INTERACTIVE: () => (/* binding */ CLASS_SLIDE_WRAPPER_INTERACTIVE),\n/* harmony export */   ELEMENT_BUTTON: () => (/* binding */ ELEMENT_BUTTON),\n/* harmony export */   ELEMENT_ICON: () => (/* binding */ ELEMENT_ICON),\n/* harmony export */   EVENT_ON_KEY_DOWN: () => (/* binding */ EVENT_ON_KEY_DOWN),\n/* harmony export */   EVENT_ON_KEY_UP: () => (/* binding */ EVENT_ON_KEY_UP),\n/* harmony export */   EVENT_ON_POINTER_CANCEL: () => (/* binding */ EVENT_ON_POINTER_CANCEL),\n/* harmony export */   EVENT_ON_POINTER_DOWN: () => (/* binding */ EVENT_ON_POINTER_DOWN),\n/* harmony export */   EVENT_ON_POINTER_LEAVE: () => (/* binding */ EVENT_ON_POINTER_LEAVE),\n/* harmony export */   EVENT_ON_POINTER_MOVE: () => (/* binding */ EVENT_ON_POINTER_MOVE),\n/* harmony export */   EVENT_ON_POINTER_UP: () => (/* binding */ EVENT_ON_POINTER_UP),\n/* harmony export */   EVENT_ON_WHEEL: () => (/* binding */ EVENT_ON_WHEEL),\n/* harmony export */   IMAGE_FIT_CONTAIN: () => (/* binding */ IMAGE_FIT_CONTAIN),\n/* harmony export */   IMAGE_FIT_COVER: () => (/* binding */ IMAGE_FIT_COVER),\n/* harmony export */   MODULE_CAROUSEL: () => (/* binding */ MODULE_CAROUSEL),\n/* harmony export */   MODULE_CONTROLLER: () => (/* binding */ MODULE_CONTROLLER),\n/* harmony export */   MODULE_NAVIGATION: () => (/* binding */ MODULE_NAVIGATION),\n/* harmony export */   MODULE_NO_SCROLL: () => (/* binding */ MODULE_NO_SCROLL),\n/* harmony export */   MODULE_PORTAL: () => (/* binding */ MODULE_PORTAL),\n/* harmony export */   MODULE_ROOT: () => (/* binding */ MODULE_ROOT),\n/* harmony export */   MODULE_TOOLBAR: () => (/* binding */ MODULE_TOOLBAR),\n/* harmony export */   PLUGIN_CAPTIONS: () => (/* binding */ PLUGIN_CAPTIONS),\n/* harmony export */   PLUGIN_COUNTER: () => (/* binding */ PLUGIN_COUNTER),\n/* harmony export */   PLUGIN_DOWNLOAD: () => (/* binding */ PLUGIN_DOWNLOAD),\n/* harmony export */   PLUGIN_FULLSCREEN: () => (/* binding */ PLUGIN_FULLSCREEN),\n/* harmony export */   PLUGIN_INLINE: () => (/* binding */ PLUGIN_INLINE),\n/* harmony export */   PLUGIN_SHARE: () => (/* binding */ PLUGIN_SHARE),\n/* harmony export */   PLUGIN_SLIDESHOW: () => (/* binding */ PLUGIN_SLIDESHOW),\n/* harmony export */   PLUGIN_THUMBNAILS: () => (/* binding */ PLUGIN_THUMBNAILS),\n/* harmony export */   PLUGIN_ZOOM: () => (/* binding */ PLUGIN_ZOOM),\n/* harmony export */   SLIDE_STATUS_COMPLETE: () => (/* binding */ SLIDE_STATUS_COMPLETE),\n/* harmony export */   SLIDE_STATUS_ERROR: () => (/* binding */ SLIDE_STATUS_ERROR),\n/* harmony export */   SLIDE_STATUS_LOADING: () => (/* binding */ SLIDE_STATUS_LOADING),\n/* harmony export */   SLIDE_STATUS_PLACEHOLDER: () => (/* binding */ SLIDE_STATUS_PLACEHOLDER),\n/* harmony export */   SLIDE_STATUS_PLAYING: () => (/* binding */ SLIDE_STATUS_PLAYING),\n/* harmony export */   UNKNOWN_ACTION_TYPE: () => (/* binding */ UNKNOWN_ACTION_TYPE),\n/* harmony export */   VK_ARROW_LEFT: () => (/* binding */ VK_ARROW_LEFT),\n/* harmony export */   VK_ARROW_RIGHT: () => (/* binding */ VK_ARROW_RIGHT),\n/* harmony export */   VK_ESCAPE: () => (/* binding */ VK_ESCAPE),\n/* harmony export */   activeSlideStatus: () => (/* binding */ activeSlideStatus)\n/* harmony export */ });\nconst MODULE_CAROUSEL = \"carousel\";\nconst MODULE_CONTROLLER = \"controller\";\nconst MODULE_NAVIGATION = \"navigation\";\nconst MODULE_NO_SCROLL = \"no-scroll\";\nconst MODULE_PORTAL = \"portal\";\nconst MODULE_ROOT = \"root\";\nconst MODULE_TOOLBAR = \"toolbar\";\nconst PLUGIN_CAPTIONS = \"captions\";\nconst PLUGIN_COUNTER = \"counter\";\nconst PLUGIN_DOWNLOAD = \"download\";\nconst PLUGIN_FULLSCREEN = \"fullscreen\";\nconst PLUGIN_INLINE = \"inline\";\nconst PLUGIN_SHARE = \"share\";\nconst PLUGIN_SLIDESHOW = \"slideshow\";\nconst PLUGIN_THUMBNAILS = \"thumbnails\";\nconst PLUGIN_ZOOM = \"zoom\";\nconst SLIDE_STATUS_LOADING = \"loading\";\nconst SLIDE_STATUS_PLAYING = \"playing\";\nconst SLIDE_STATUS_ERROR = \"error\";\nconst SLIDE_STATUS_COMPLETE = \"complete\";\nconst SLIDE_STATUS_PLACEHOLDER = \"placeholder\";\nconst activeSlideStatus = (status) => `active-slide-${status}`;\nconst ACTIVE_SLIDE_LOADING = activeSlideStatus(SLIDE_STATUS_LOADING);\nconst ACTIVE_SLIDE_PLAYING = activeSlideStatus(SLIDE_STATUS_PLAYING);\nconst ACTIVE_SLIDE_ERROR = activeSlideStatus(SLIDE_STATUS_ERROR);\nconst ACTIVE_SLIDE_COMPLETE = activeSlideStatus(SLIDE_STATUS_COMPLETE);\nconst CLASS_FULLSIZE = \"fullsize\";\nconst CLASS_FLEX_CENTER = \"flex_center\";\nconst CLASS_NO_SCROLL = \"no_scroll\";\nconst CLASS_NO_SCROLL_PADDING = \"no_scroll_padding\";\nconst CLASS_SLIDE_WRAPPER = \"slide_wrapper\";\nconst CLASS_SLIDE_WRAPPER_INTERACTIVE = \"slide_wrapper_interactive\";\nconst ACTION_PREV = \"prev\";\nconst ACTION_NEXT = \"next\";\nconst ACTION_SWIPE = \"swipe\";\nconst ACTION_CLOSE = \"close\";\nconst EVENT_ON_POINTER_DOWN = \"onPointerDown\";\nconst EVENT_ON_POINTER_MOVE = \"onPointerMove\";\nconst EVENT_ON_POINTER_UP = \"onPointerUp\";\nconst EVENT_ON_POINTER_LEAVE = \"onPointerLeave\";\nconst EVENT_ON_POINTER_CANCEL = \"onPointerCancel\";\nconst EVENT_ON_KEY_DOWN = \"onKeyDown\";\nconst EVENT_ON_KEY_UP = \"onKeyUp\";\nconst EVENT_ON_WHEEL = \"onWheel\";\nconst VK_ESCAPE = \"Escape\";\nconst VK_ARROW_LEFT = \"ArrowLeft\";\nconst VK_ARROW_RIGHT = \"ArrowRight\";\nconst ELEMENT_BUTTON = \"button\";\nconst ELEMENT_ICON = \"icon\";\nconst IMAGE_FIT_CONTAIN = \"contain\";\nconst IMAGE_FIT_COVER = \"cover\";\nconst UNKNOWN_ACTION_TYPE = \"Unknown action type\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/yet-another-react-lightbox/dist/types.js\n");

/***/ })

};
;