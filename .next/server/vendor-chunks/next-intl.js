"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-intl";
exports.ids = ["vendor-chunks/next-intl"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nexports[\"extends\"] = _extends;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdEO0FBQ0E7QUFDQSxvQkFBb0Isc0JBQXNCO0FBQzFDO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUEsa0JBQWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcz85YmY0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxuZnVuY3Rpb24gX2V4dGVuZHMoKSB7XG4gIHJldHVybiBfZXh0ZW5kcyA9IE9iamVjdC5hc3NpZ24gPyBPYmplY3QuYXNzaWduLmJpbmQoKSA6IGZ1bmN0aW9uIChuKSB7XG4gICAgZm9yICh2YXIgZSA9IDE7IGUgPCBhcmd1bWVudHMubGVuZ3RoOyBlKyspIHtcbiAgICAgIHZhciB0ID0gYXJndW1lbnRzW2VdO1xuICAgICAgZm9yICh2YXIgciBpbiB0KSAoe30pLmhhc093blByb3BlcnR5LmNhbGwodCwgcikgJiYgKG5bcl0gPSB0W3JdKTtcbiAgICB9XG4gICAgcmV0dXJuIG47XG4gIH0sIF9leHRlbmRzLmFwcGx5KG51bGwsIGFyZ3VtZW50cyk7XG59XG5cbmV4cG9ydHMuZXh0ZW5kcyA9IF9leHRlbmRzO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/index.react-client.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/index.react-client.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar index = __webpack_require__(/*! ./react-client/index.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/index.js\");\nvar useLocale = __webpack_require__(/*! ./react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar NextIntlClientProvider = __webpack_require__(/*! ./shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js\");\nvar useIntl = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/index.js\");\n\n\n\nexports.useFormatter = index.useFormatter;\nexports.useTranslations = index.useTranslations;\nexports.useLocale = useLocale.default;\nexports.NextIntlClientProvider = NextIntlClientProvider.default;\nObject.keys(useIntl).forEach(function (k) {\n\tif (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n\t\tenumerable: true,\n\t\tget: function () { return useIntl[k]; }\n\t});\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvaW5kZXgucmVhY3QtY2xpZW50LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0QsWUFBWSxtQkFBTyxDQUFDLHNHQUF5QjtBQUM3QyxnQkFBZ0IsbUJBQU8sQ0FBQyw4R0FBNkI7QUFDckQsNkJBQTZCLG1CQUFPLENBQUMsNEhBQW9DO0FBQ3pFLGNBQWMsbUJBQU8sQ0FBQyw2REFBVTs7OztBQUloQyxvQkFBb0I7QUFDcEIsdUJBQXVCO0FBQ3ZCLGlCQUFpQjtBQUNqQiw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLEVBQUU7QUFDRixDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50bDItY29tcGFueXByb2ZpbGUtdjAyLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2RldmVsb3BtZW50L2luZGV4LnJlYWN0LWNsaWVudC5qcz81Zjk4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxudmFyIGluZGV4ID0gcmVxdWlyZSgnLi9yZWFjdC1jbGllbnQvaW5kZXguanMnKTtcbnZhciB1c2VMb2NhbGUgPSByZXF1aXJlKCcuL3JlYWN0LWNsaWVudC91c2VMb2NhbGUuanMnKTtcbnZhciBOZXh0SW50bENsaWVudFByb3ZpZGVyID0gcmVxdWlyZSgnLi9zaGFyZWQvTmV4dEludGxDbGllbnRQcm92aWRlci5qcycpO1xudmFyIHVzZUludGwgPSByZXF1aXJlKCd1c2UtaW50bCcpO1xuXG5cblxuZXhwb3J0cy51c2VGb3JtYXR0ZXIgPSBpbmRleC51c2VGb3JtYXR0ZXI7XG5leHBvcnRzLnVzZVRyYW5zbGF0aW9ucyA9IGluZGV4LnVzZVRyYW5zbGF0aW9ucztcbmV4cG9ydHMudXNlTG9jYWxlID0gdXNlTG9jYWxlLmRlZmF1bHQ7XG5leHBvcnRzLk5leHRJbnRsQ2xpZW50UHJvdmlkZXIgPSBOZXh0SW50bENsaWVudFByb3ZpZGVyLmRlZmF1bHQ7XG5PYmplY3Qua2V5cyh1c2VJbnRsKS5mb3JFYWNoKGZ1bmN0aW9uIChrKSB7XG5cdGlmIChrICE9PSAnZGVmYXVsdCcgJiYgIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChleHBvcnRzLCBrKSkgT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIGssIHtcblx0XHRlbnVtZXJhYmxlOiB0cnVlLFxuXHRcdGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gdXNlSW50bFtrXTsgfVxuXHR9KTtcbn0pO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation.react-client.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation.react-client.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar createSharedPathnamesNavigation = __webpack_require__(/*! ./navigation/react-client/createSharedPathnamesNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js\");\nvar createLocalizedPathnamesNavigation = __webpack_require__(/*! ./navigation/react-client/createLocalizedPathnamesNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js\");\nvar createNavigation = __webpack_require__(/*! ./navigation/react-client/createNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js\");\n\n\n\nexports.createSharedPathnamesNavigation = createSharedPathnamesNavigation.default;\nexports.createLocalizedPathnamesNavigation = createLocalizedPathnamesNavigation.default;\nexports.createNavigation = createNavigation.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi5yZWFjdC1jbGllbnQuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDLEVBQUUsYUFBYSxFQUFDOztBQUU3RCxzQ0FBc0MsbUJBQU8sQ0FBQyxnTEFBOEQ7QUFDNUcseUNBQXlDLG1CQUFPLENBQUMsc0xBQWlFO0FBQ2xILHVCQUF1QixtQkFBTyxDQUFDLGtKQUErQzs7OztBQUk5RSx1Q0FBdUM7QUFDdkMsMENBQTBDO0FBQzFDLHdCQUF3QiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGwyLWNvbXBhbnlwcm9maWxlLXYwMi8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9kZXZlbG9wbWVudC9uYXZpZ2F0aW9uLnJlYWN0LWNsaWVudC5qcz85NGYzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxudmFyIGNyZWF0ZVNoYXJlZFBhdGhuYW1lc05hdmlnYXRpb24gPSByZXF1aXJlKCcuL25hdmlnYXRpb24vcmVhY3QtY2xpZW50L2NyZWF0ZVNoYXJlZFBhdGhuYW1lc05hdmlnYXRpb24uanMnKTtcbnZhciBjcmVhdGVMb2NhbGl6ZWRQYXRobmFtZXNOYXZpZ2F0aW9uID0gcmVxdWlyZSgnLi9uYXZpZ2F0aW9uL3JlYWN0LWNsaWVudC9jcmVhdGVMb2NhbGl6ZWRQYXRobmFtZXNOYXZpZ2F0aW9uLmpzJyk7XG52YXIgY3JlYXRlTmF2aWdhdGlvbiA9IHJlcXVpcmUoJy4vbmF2aWdhdGlvbi9yZWFjdC1jbGllbnQvY3JlYXRlTmF2aWdhdGlvbi5qcycpO1xuXG5cblxuZXhwb3J0cy5jcmVhdGVTaGFyZWRQYXRobmFtZXNOYXZpZ2F0aW9uID0gY3JlYXRlU2hhcmVkUGF0aG5hbWVzTmF2aWdhdGlvbi5kZWZhdWx0O1xuZXhwb3J0cy5jcmVhdGVMb2NhbGl6ZWRQYXRobmFtZXNOYXZpZ2F0aW9uID0gY3JlYXRlTG9jYWxpemVkUGF0aG5hbWVzTmF2aWdhdGlvbi5kZWZhdWx0O1xuZXhwb3J0cy5jcmVhdGVOYXZpZ2F0aW9uID0gY3JlYXRlTmF2aWdhdGlvbi5kZWZhdWx0O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation.react-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar LegacyBaseLink = __webpack_require__(/*! ../shared/LegacyBaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\nfunction ClientLink(_ref, ref) {\n  let {\n    locale,\n    localePrefix,\n    ...rest\n  } = _ref;\n  const defaultLocale = useLocale.default();\n  const finalLocale = locale || defaultLocale;\n  const prefix = utils.getLocalePrefix(finalLocale, localePrefix);\n  return /*#__PURE__*/React__default.default.createElement(LegacyBaseLink.default, _rollupPluginBabelHelpers.extends({\n    ref: ref,\n    locale: finalLocale,\n    localePrefixMode: localePrefix.mode,\n    prefix: prefix\n  }, rest));\n}\n\n/**\n * Wraps `next/link` and prefixes the `href` with the current locale if\n * necessary.\n *\n * @example\n * ```tsx\n * import {Link} from 'next-intl';\n *\n * // When the user is on `/en`, the link will point to `/en/about`\n * <Link href=\"/about\">About</Link>\n *\n * // You can override the `locale` to switch to another language\n * <Link href=\"/\" locale=\"de\">Switch to German</Link>\n * ```\n *\n * Note that when a `locale` prop is passed to switch the locale, the `prefetch`\n * prop is not supported. This is because Next.js would prefetch the page and\n * the `set-cookie` response header would cause the locale cookie on the current\n * page to be overwritten before the user even decides to change the locale.\n */\nconst ClientLinkWithRef = /*#__PURE__*/React.forwardRef(ClientLink);\nClientLinkWithRef.displayName = 'ClientLink';\n\nexports[\"default\"] = ClientLinkWithRef;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\nvar ClientLink = __webpack_require__(/*! ./ClientLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\");\nvar redirects = __webpack_require__(/*! ./redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\nvar useBaseRouter = __webpack_require__(/*! ./useBaseRouter.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\n/**\n * @deprecated Consider switching to `createNavigation` (see https://next-intl.dev/blog/next-intl-3-22#create-navigation)\n **/\nfunction createLocalizedPathnamesNavigation(routing) {\n  const config$1 = config.receiveRoutingConfig(routing);\n  const localeCookie = config.receiveLocaleCookie(routing.localeCookie);\n  function useTypedLocale() {\n    const locale = useLocale.default();\n    const isValid = config$1.locales.includes(locale);\n    if (!isValid) {\n      throw new Error(\"Unknown locale encountered: \\\"\".concat(locale, \"\\\". Make sure to validate the locale in `i18n.ts`.\") );\n    }\n    return locale;\n  }\n  function Link(_ref, ref) {\n    let {\n      href,\n      locale,\n      ...rest\n    } = _ref;\n    const defaultLocale = useTypedLocale();\n    const finalLocale = locale || defaultLocale;\n    return /*#__PURE__*/React__default.default.createElement(ClientLink.default, _rollupPluginBabelHelpers.extends({\n      ref: ref,\n      href: utils.compileLocalizedPathname({\n        locale: finalLocale,\n        // @ts-expect-error -- This is ok\n        pathname: href,\n        // @ts-expect-error -- This is ok\n        params: typeof href === 'object' ? href.params : undefined,\n        pathnames: config$1.pathnames\n      }),\n      locale: locale,\n      localeCookie: localeCookie,\n      localePrefix: config$1.localePrefix\n    }, rest));\n  }\n  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);\n  LinkWithRef.displayName = 'Link';\n  function redirect(href) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n    const locale = useTypedLocale();\n    const resolvedHref = getPathname({\n      href,\n      locale\n    });\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirects.clientRedirect({\n      pathname: resolvedHref,\n      localePrefix: config$1.localePrefix\n    }, ...args);\n  }\n  function permanentRedirect(href) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n    const locale = useTypedLocale();\n    const resolvedHref = getPathname({\n      href,\n      locale\n    });\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    return redirects.clientPermanentRedirect({\n      pathname: resolvedHref,\n      localePrefix: config$1.localePrefix\n    }, ...args);\n  }\n  function useRouter() {\n    const baseRouter = useBaseRouter.default(config$1.localePrefix, localeCookie);\n    const defaultLocale = useTypedLocale();\n    return React.useMemo(() => ({\n      ...baseRouter,\n      push(href) {\n        var _args$;\n        for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n          args[_key3 - 1] = arguments[_key3];\n        }\n        const resolvedHref = getPathname({\n          href,\n          locale: ((_args$ = args[0]) === null || _args$ === void 0 ? void 0 : _args$.locale) || defaultLocale\n        });\n        return baseRouter.push(resolvedHref, ...args);\n      },\n      replace(href) {\n        var _args$2;\n        for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n          args[_key4 - 1] = arguments[_key4];\n        }\n        const resolvedHref = getPathname({\n          href,\n          locale: ((_args$2 = args[0]) === null || _args$2 === void 0 ? void 0 : _args$2.locale) || defaultLocale\n        });\n        return baseRouter.replace(resolvedHref, ...args);\n      },\n      prefetch(href) {\n        var _args$3;\n        for (var _len5 = arguments.length, args = new Array(_len5 > 1 ? _len5 - 1 : 0), _key5 = 1; _key5 < _len5; _key5++) {\n          args[_key5 - 1] = arguments[_key5];\n        }\n        const resolvedHref = getPathname({\n          href,\n          locale: ((_args$3 = args[0]) === null || _args$3 === void 0 ? void 0 : _args$3.locale) || defaultLocale\n        });\n        return baseRouter.prefetch(resolvedHref, ...args);\n      }\n    }), [baseRouter, defaultLocale]);\n  }\n  function usePathname() {\n    const pathname = useBasePathname.default(config$1);\n    const locale = useTypedLocale();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return React.useMemo(() => pathname ? utils.getRoute(locale, pathname, config$1.pathnames) : pathname, [locale, pathname]);\n  }\n  function getPathname(_ref2) {\n    let {\n      href,\n      locale\n    } = _ref2;\n    return utils.compileLocalizedPathname({\n      ...utils.normalizeNameOrNameWithParams(href),\n      locale,\n      pathnames: config$1.pathnames\n    });\n  }\n  return {\n    Link: LinkWithRef,\n    redirect,\n    permanentRedirect,\n    usePathname,\n    useRouter,\n    getPathname\n  };\n}\n\nexports[\"default\"] = createLocalizedPathnamesNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar createSharedNavigationFns = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ../shared/syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\n\nfunction createNavigation(routing) {\n  function useTypedLocale() {\n    return useLocale.default();\n  }\n  const {\n    Link,\n    config,\n    getPathname,\n    ...redirects\n  } = createSharedNavigationFns.default(useTypedLocale, routing);\n\n  /** @see https://next-intl.dev/docs/routing/navigation#usepathname */\n  function usePathname() {\n    const pathname = useBasePathname.default(config);\n    const locale = useTypedLocale();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return React.useMemo(() => pathname &&\n    // @ts-expect-error -- This is fine\n    config.pathnames ? utils.getRoute(locale, pathname,\n    // @ts-expect-error -- This is fine\n    config.pathnames) : pathname, [locale, pathname]);\n  }\n  function useRouter() {\n    const router = navigation.useRouter();\n    const curLocale = useTypedLocale();\n    const nextPathname = navigation.usePathname();\n    return React.useMemo(() => {\n      function createHandler(fn) {\n        return function handler(href, options) {\n          const {\n            locale: nextLocale,\n            ...rest\n          } = options || {};\n\n          // @ts-expect-error -- We're passing a domain here just in case\n          const pathname = getPathname({\n            href,\n            locale: nextLocale || curLocale,\n            domain: window.location.host\n          });\n          const args = [pathname];\n          if (Object.keys(rest).length > 0) {\n            // @ts-expect-error -- This is fine\n            args.push(rest);\n          }\n          fn(...args);\n          syncLocaleCookie.default(config.localeCookie, nextPathname, curLocale, nextLocale);\n        };\n      }\n      return {\n        ...router,\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        push: createHandler(router.push),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        replace: createHandler(router.replace),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        prefetch: createHandler(router.prefetch)\n      };\n    }, [curLocale, nextPathname, router]);\n  }\n  return {\n    ...redirects,\n    Link,\n    usePathname,\n    useRouter,\n    getPathname\n  };\n}\n\nexports[\"default\"] = createNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar ClientLink = __webpack_require__(/*! ./ClientLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\");\nvar redirects = __webpack_require__(/*! ./redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\nvar useBaseRouter = __webpack_require__(/*! ./useBaseRouter.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\n/**\n * @deprecated Consider switching to `createNavigation` (see https://next-intl.dev/blog/next-intl-3-22#create-navigation)\n **/\nfunction createSharedPathnamesNavigation(routing) {\n  const localePrefix = config.receiveLocalePrefixConfig(routing === null || routing === void 0 ? void 0 : routing.localePrefix);\n  const localeCookie = config.receiveLocaleCookie(routing === null || routing === void 0 ? void 0 : routing.localeCookie);\n  function Link(props, ref) {\n    return /*#__PURE__*/React__default.default.createElement(ClientLink.default, _rollupPluginBabelHelpers.extends({\n      ref: ref,\n      localeCookie: localeCookie,\n      localePrefix: localePrefix\n    }, props));\n  }\n  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);\n  LinkWithRef.displayName = 'Link';\n  function redirect(pathname) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirects.clientRedirect({\n      pathname,\n      localePrefix\n    }, ...args);\n  }\n  function permanentRedirect(pathname) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    return redirects.clientPermanentRedirect({\n      pathname,\n      localePrefix\n    }, ...args);\n  }\n  function usePathname() {\n    const result = useBasePathname.default({\n      localePrefix,\n      defaultLocale: routing === null || routing === void 0 ? void 0 : routing.defaultLocale\n    });\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return result;\n  }\n  function useRouter() {\n    return useBaseRouter.default(localePrefix, localeCookie);\n  }\n  return {\n    Link: LinkWithRef,\n    redirect,\n    permanentRedirect,\n    usePathname,\n    useRouter\n  };\n}\n\nexports[\"default\"] = createSharedPathnamesNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/redirects.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar redirects = __webpack_require__(/*! ../shared/redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js\");\n\nfunction createRedirectFn(redirectFn) {\n  return function clientRedirect(params) {\n    let locale;\n    try {\n      // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n      locale = useLocale.default();\n    } catch (e) {\n      {\n        throw new Error('`redirect()` and `permanentRedirect()` can only be called during render. To redirect in an event handler or similar, you can use `useRouter()` instead.');\n      }\n    }\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirectFn({\n      ...params,\n      locale\n    }, ...args);\n  };\n}\nconst clientRedirect = createRedirectFn(redirects.baseRedirect);\nconst clientPermanentRedirect = createRedirectFn(redirects.basePermanentRedirect);\n\nexports.clientPermanentRedirect = clientPermanentRedirect;\nexports.clientRedirect = clientRedirect;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\n\nfunction useBasePathname(config) {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n\n  // Notes on `useNextPathname`:\n  // - Types aren't entirely correct. Outside of Next.js the\n  //   hook will return `null` (e.g. unit tests)\n  // - A base path is stripped from the result\n  // - Rewrites *are* taken into account (i.e. the pathname\n  //   that the user sees in the browser is returned)\n  const pathname = navigation.usePathname();\n  const locale = useLocale.default();\n  return React.useMemo(() => {\n    if (!pathname) return pathname;\n    let unlocalizedPathname = pathname;\n    const prefix = utils.getLocalePrefix(locale, config.localePrefix);\n    const isPathnamePrefixed = utils.hasPathnamePrefixed(prefix, pathname);\n    if (isPathnamePrefixed) {\n      unlocalizedPathname = utils.unprefixPathname(pathname, prefix);\n    } else if (config.localePrefix.mode === 'as-needed' && config.localePrefix.prefixes) {\n      // Workaround for https://github.com/vercel/next.js/issues/73085\n      const localeAsPrefix = utils.getLocaleAsPrefix(locale);\n      if (utils.hasPathnamePrefixed(localeAsPrefix, pathname)) {\n        unlocalizedPathname = utils.unprefixPathname(pathname, localeAsPrefix);\n      }\n    }\n    return unlocalizedPathname;\n  }, [config.localePrefix, locale, pathname]);\n}\n\nexports[\"default\"] = useBasePathname;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils$1 = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ../shared/syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n/**\n * Returns a wrapped instance of `useRouter` from `next/navigation` that\n * will automatically localize the `href` parameters it receives.\n *\n * @example\n * ```tsx\n * 'use client';\n *\n * import {useRouter} from 'next-intl/client';\n *\n * const router = useRouter();\n *\n * // When the user is on `/en`, the router will navigate to `/en/about`\n * router.push('/about');\n *\n * // Optionally, you can switch the locale by passing the second argument\n * router.push('/about', {locale: 'de'});\n * ```\n */ function useBaseRouter(localePrefix, localeCookie) {\n    const router = navigation.useRouter();\n    const locale = useLocale.default();\n    const pathname = navigation.usePathname();\n    return React.useMemo(()=>{\n        function localize(href, nextLocale) {\n            let curPathname = window.location.pathname;\n            const basePath = utils.getBasePath(pathname);\n            if (basePath) curPathname = curPathname.replace(basePath, \"\");\n            const targetLocale = nextLocale || locale;\n            // We generate a prefix in any case, but decide\n            // in `localizeHref` if we apply it or not\n            const prefix = utils$1.getLocalePrefix(targetLocale, localePrefix);\n            return utils$1.localizeHref(href, targetLocale, locale, curPathname, prefix);\n        }\n        function createHandler(fn) {\n            return function handler(href, options) {\n                const { locale: nextLocale, ...rest } = options || {};\n                syncLocaleCookie.default(localeCookie, pathname, locale, nextLocale);\n                const args = [\n                    localize(href, nextLocale)\n                ];\n                if (Object.keys(rest).length > 0) {\n                    args.push(rest);\n                }\n                // @ts-expect-error -- This is ok\n                return fn(...args);\n            };\n        }\n        return {\n            ...router,\n            push: createHandler(router.push),\n            replace: createHandler(router.replace),\n            prefetch: createHandler(router.prefetch)\n        };\n    }, [\n        locale,\n        localeCookie,\n        localePrefix,\n        pathname,\n        router\n    ]);\n}\nexports[\"default\"] = useBaseRouter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar NextLink = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ./syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar NextLink__default = /*#__PURE__*/ _interopDefault(NextLink);\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction BaseLink(_ref, ref) {\n    let { defaultLocale, href, locale, localeCookie, onClick, prefetch, unprefixed, ...rest } = _ref;\n    const curLocale = useLocale.default();\n    const isChangingLocale = locale != null && locale !== curLocale;\n    const linkLocale = locale || curLocale;\n    const host = useHost();\n    const finalHref = // Only after hydration (to avoid mismatches)\n    host && // If there is an `unprefixed` prop, the\n    // `defaultLocale` might differ by domain\n    unprefixed && // Unprefix the pathname if a domain matches\n    (unprefixed.domains[host] === linkLocale || // … and handle unknown domains by applying the\n    // global `defaultLocale` (e.g. on localhost)\n    !Object.keys(unprefixed.domains).includes(host) && curLocale === defaultLocale && !locale) ? unprefixed.pathname : href;\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const pathname = navigation.usePathname();\n    function onLinkClick(event) {\n        syncLocaleCookie.default(localeCookie, pathname, curLocale, locale);\n        if (onClick) onClick(event);\n    }\n    if (isChangingLocale) {\n        if (prefetch && \"development\" !== \"production\") {\n            console.error(\"The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`\");\n        }\n        prefetch = false;\n    }\n    return /*#__PURE__*/ React__default.default.createElement(NextLink__default.default, _rollupPluginBabelHelpers.extends({\n        ref: ref,\n        href: finalHref,\n        hrefLang: isChangingLocale ? locale : undefined,\n        onClick: onLinkClick,\n        prefetch: prefetch\n    }, rest));\n}\nfunction useHost() {\n    const [host, setHost] = React.useState();\n    React.useEffect(()=>{\n        setHost(window.location.host);\n    }, []);\n    return host;\n}\nvar BaseLink$1 = /*#__PURE__*/ React.forwardRef(BaseLink);\nexports[\"default\"] = BaseLink$1;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar BaseLink = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction LegacyBaseLink(_ref, ref) {\n    let { href, locale, localeCookie, localePrefixMode, prefix, ...rest } = _ref;\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const pathname = navigation.usePathname();\n    const curLocale = useLocale.default();\n    const isChangingLocale = locale !== curLocale;\n    const [localizedHref, setLocalizedHref] = React.useState(()=>utils.isLocalizableHref(href) && (localePrefixMode !== \"never\" || isChangingLocale) ? // For the `localePrefix: 'as-needed' strategy, the href shouldn't\n        // be prefixed if the locale is the default locale. To determine this, we\n        // need a) the default locale and b) the information if we use prefixed\n        // routing. The default locale can vary by domain, therefore during the\n        // RSC as well as the SSR render, we can't determine the default locale\n        // statically. Therefore we always prefix the href since this will\n        // always result in a valid URL, even if it might cause a redirect. This\n        // is better than pointing to a non-localized href during the server\n        // render, which would potentially be wrong. The final href is\n        // determined in the effect below.\n        utils.prefixHref(href, prefix) : href);\n    React.useEffect(()=>{\n        if (!pathname) return;\n        setLocalizedHref(utils.localizeHref(href, locale, curLocale, pathname, prefix));\n    }, [\n        curLocale,\n        href,\n        locale,\n        pathname,\n        prefix\n    ]);\n    return /*#__PURE__*/ React__default.default.createElement(BaseLink.default, _rollupPluginBabelHelpers.extends({\n        ref: ref,\n        href: localizedHref,\n        locale: locale,\n        localeCookie: localeCookie\n    }, rest));\n}\nconst LegacyBaseLinkWithRef = /*#__PURE__*/ React.forwardRef(LegacyBaseLink);\nLegacyBaseLinkWithRef.displayName = \"ClientLink\";\nexports[\"default\"] = LegacyBaseLinkWithRef;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar utils$1 = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar BaseLink = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\");\nvar utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config$1 = config.receiveRoutingConfig(routing || {});\n  {\n    utils.validateReceivedConfig(config$1);\n  }\n  const pathnames = config$1.pathnames;\n\n  // This combination requires that the current host is known in order to\n  // compute a correct pathname. Since that can only be achieved by reading from\n  // headers, this would break static rendering. Therefore, as a workaround we\n  // always add a prefix in this case to be on the safe side. The downside is\n  // that the user might get redirected again if the middleware detects that the\n  // prefix is not needed.\n  const forcePrefixSsr = config$1.localePrefix.mode === 'as-needed' && config$1.domains || undefined;\n  function Link(_ref, ref) {\n    let {\n      href,\n      locale,\n      ...rest\n    } = _ref;\n    let pathname, params, query;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      query = href.query;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = utils$1.isLocalizableHref(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = utils$1.isPromise(localePromiseOrValue) ? React.use(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname(\n    // @ts-expect-error -- This is ok\n    {\n      locale: locale || curLocale,\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      }\n    }, locale != null || forcePrefixSsr || undefined) : pathname;\n    return /*#__PURE__*/React__default.default.createElement(BaseLink.default, _rollupPluginBabelHelpers.extends({\n      ref: ref\n      // @ts-expect-error -- Available after the validation\n      ,\n      defaultLocale: config$1.defaultLocale\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config$1.localeCookie\n      // Provide the minimal relevant information to the client side in order\n      // to potentially remove the prefix in case of the `forcePrefixSsr` case\n      ,\n      unprefixed: forcePrefixSsr && isLocalizable ? {\n        domains: config$1.domains.reduce((acc, domain) => {\n          // @ts-expect-error -- This is ok\n          acc[domain.domain] = domain.defaultLocale;\n          return acc;\n        }, {}),\n        pathname: getPathname(\n        // @ts-expect-error -- This is ok\n        {\n          locale: curLocale,\n          href: pathnames == null ? {\n            pathname,\n            query\n          } : {\n            pathname,\n            query,\n            params\n          }\n        }, false)\n      } : undefined\n    }, rest));\n  }\n  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);\n  function getPathname(args, /** @private Removed in types returned below */\n  _forcePrefix) {\n    const {\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += utils.serializeSearchParams(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = utils.compileLocalizedPathname({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...utils.normalizeNameOrNameWithParams(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config$1.pathnames\n      });\n    }\n    return utils.applyPathnamePrefix(pathname, locale, config$1,\n    // @ts-expect-error -- This is ok\n    args.domain, _forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl.dev/docs/routing/navigation#redirect */\n    return function redirectFn(args) {\n      for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        rest[_key - 1] = arguments[_key];\n      }\n      return fn(\n      // @ts-expect-error -- We're forcing the prefix when no domain is provided\n      getPathname(args, args.domain ? undefined : forcePrefixSsr), ...rest);\n    };\n  }\n  const redirect = getRedirectFn(navigation.redirect);\n  const permanentRedirect = getRedirectFn(navigation.permanentRedirect);\n  return {\n    config: config$1,\n    Link: LinkWithRef,\n    redirect,\n    permanentRedirect,\n    // Remove `_forcePrefix` from public API\n    getPathname: getPathname\n  };\n}\n\nexports[\"default\"] = createSharedNavigationFns;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/redirects.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\n\nfunction createRedirectFn(redirectFn) {\n  return function baseRedirect(params) {\n    const prefix = utils.getLocalePrefix(params.locale, params.localePrefix);\n\n    // This logic is considered legacy and is replaced by `applyPathnamePrefix`.\n    // We keep it this way for now for backwards compatibility.\n    const localizedPathname = params.localePrefix.mode === 'never' || !utils.isLocalizableHref(params.pathname) ? params.pathname : utils.prefixPathname(prefix, params.pathname);\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirectFn(localizedPathname, ...args);\n  };\n}\nconst baseRedirect = createRedirectFn(navigation.redirect);\nconst basePermanentRedirect = createRedirectFn(navigation.permanentRedirect);\n\nexports.basePermanentRedirect = basePermanentRedirect;\nexports.baseRedirect = baseRedirect;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n\n/**\n * We have to keep the cookie value in sync as Next.js might\n * skip a request to the server due to its router cache.\n * See https://github.com/amannn/next-intl/issues/786.\n */\nfunction syncLocaleCookie(localeCookie, pathname, locale, nextLocale) {\n  const isSwitchingLocale = nextLocale !== locale && nextLocale != null;\n  if (!localeCookie || !isSwitchingLocale ||\n  // Theoretical case, we always have a pathname in a real app,\n  // only not when running e.g. in a simulated test environment\n  !pathname) {\n    return;\n  }\n  const basePath = utils.getBasePath(pathname);\n  const hasBasePath = basePath !== '';\n  const defaultPath = hasBasePath ? basePath : '/';\n  const {\n    name,\n    ...rest\n  } = localeCookie;\n  if (!rest.path) {\n    rest.path = defaultPath;\n  }\n  let localeCookieString = \"\".concat(name, \"=\").concat(nextLocale, \";\");\n  for (const [key, value] of Object.entries(rest)) {\n    // Map object properties to cookie properties.\n    // Interestingly, `maxAge` corresponds to `max-age`,\n    // while `sameSite` corresponds to `SameSite`.\n    // Also, keys are case-insensitive.\n    const targetKey = key === 'maxAge' ? 'max-age' : key;\n    localeCookieString += \"\".concat(targetKey);\n    if (typeof value !== 'boolean') {\n      localeCookieString += '=' + value;\n    }\n\n    // A trailing \";\" is allowed by browsers\n    localeCookieString += ';';\n  }\n\n  // Note that writing to `document.cookie` doesn't overwrite all\n  // cookies, but only the ones referenced via the name here.\n  document.cookie = localeCookieString;\n}\n\nexports[\"default\"] = syncLocaleCookie;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi9zaGFyZWQvc3luY0xvY2FsZUNvb2tpZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdELFlBQVksbUJBQU8sQ0FBQyw4RkFBWTs7QUFFaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0EscUVBQXFFO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG9CQUFvQjtBQUNwQiw0QkFBNEI7QUFDNUI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsa0JBQWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi9zaGFyZWQvc3luY0xvY2FsZUNvb2tpZS5qcz9jMGNkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxudmFyIHV0aWxzID0gcmVxdWlyZSgnLi91dGlscy5qcycpO1xuXG4vKipcbiAqIFdlIGhhdmUgdG8ga2VlcCB0aGUgY29va2llIHZhbHVlIGluIHN5bmMgYXMgTmV4dC5qcyBtaWdodFxuICogc2tpcCBhIHJlcXVlc3QgdG8gdGhlIHNlcnZlciBkdWUgdG8gaXRzIHJvdXRlciBjYWNoZS5cbiAqIFNlZSBodHRwczovL2dpdGh1Yi5jb20vYW1hbm5uL25leHQtaW50bC9pc3N1ZXMvNzg2LlxuICovXG5mdW5jdGlvbiBzeW5jTG9jYWxlQ29va2llKGxvY2FsZUNvb2tpZSwgcGF0aG5hbWUsIGxvY2FsZSwgbmV4dExvY2FsZSkge1xuICBjb25zdCBpc1N3aXRjaGluZ0xvY2FsZSA9IG5leHRMb2NhbGUgIT09IGxvY2FsZSAmJiBuZXh0TG9jYWxlICE9IG51bGw7XG4gIGlmICghbG9jYWxlQ29va2llIHx8ICFpc1N3aXRjaGluZ0xvY2FsZSB8fFxuICAvLyBUaGVvcmV0aWNhbCBjYXNlLCB3ZSBhbHdheXMgaGF2ZSBhIHBhdGhuYW1lIGluIGEgcmVhbCBhcHAsXG4gIC8vIG9ubHkgbm90IHdoZW4gcnVubmluZyBlLmcuIGluIGEgc2ltdWxhdGVkIHRlc3QgZW52aXJvbm1lbnRcbiAgIXBhdGhuYW1lKSB7XG4gICAgcmV0dXJuO1xuICB9XG4gIGNvbnN0IGJhc2VQYXRoID0gdXRpbHMuZ2V0QmFzZVBhdGgocGF0aG5hbWUpO1xuICBjb25zdCBoYXNCYXNlUGF0aCA9IGJhc2VQYXRoICE9PSAnJztcbiAgY29uc3QgZGVmYXVsdFBhdGggPSBoYXNCYXNlUGF0aCA/IGJhc2VQYXRoIDogJy8nO1xuICBjb25zdCB7XG4gICAgbmFtZSxcbiAgICAuLi5yZXN0XG4gIH0gPSBsb2NhbGVDb29raWU7XG4gIGlmICghcmVzdC5wYXRoKSB7XG4gICAgcmVzdC5wYXRoID0gZGVmYXVsdFBhdGg7XG4gIH1cbiAgbGV0IGxvY2FsZUNvb2tpZVN0cmluZyA9IFwiXCIuY29uY2F0KG5hbWUsIFwiPVwiKS5jb25jYXQobmV4dExvY2FsZSwgXCI7XCIpO1xuICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhyZXN0KSkge1xuICAgIC8vIE1hcCBvYmplY3QgcHJvcGVydGllcyB0byBjb29raWUgcHJvcGVydGllcy5cbiAgICAvLyBJbnRlcmVzdGluZ2x5LCBgbWF4QWdlYCBjb3JyZXNwb25kcyB0byBgbWF4LWFnZWAsXG4gICAgLy8gd2hpbGUgYHNhbWVTaXRlYCBjb3JyZXNwb25kcyB0byBgU2FtZVNpdGVgLlxuICAgIC8vIEFsc28sIGtleXMgYXJlIGNhc2UtaW5zZW5zaXRpdmUuXG4gICAgY29uc3QgdGFyZ2V0S2V5ID0ga2V5ID09PSAnbWF4QWdlJyA/ICdtYXgtYWdlJyA6IGtleTtcbiAgICBsb2NhbGVDb29raWVTdHJpbmcgKz0gXCJcIi5jb25jYXQodGFyZ2V0S2V5KTtcbiAgICBpZiAodHlwZW9mIHZhbHVlICE9PSAnYm9vbGVhbicpIHtcbiAgICAgIGxvY2FsZUNvb2tpZVN0cmluZyArPSAnPScgKyB2YWx1ZTtcbiAgICB9XG5cbiAgICAvLyBBIHRyYWlsaW5nIFwiO1wiIGlzIGFsbG93ZWQgYnkgYnJvd3NlcnNcbiAgICBsb2NhbGVDb29raWVTdHJpbmcgKz0gJzsnO1xuICB9XG5cbiAgLy8gTm90ZSB0aGF0IHdyaXRpbmcgdG8gYGRvY3VtZW50LmNvb2tpZWAgZG9lc24ndCBvdmVyd3JpdGUgYWxsXG4gIC8vIGNvb2tpZXMsIGJ1dCBvbmx5IHRoZSBvbmVzIHJlZmVyZW5jZWQgdmlhIHRoZSBuYW1lIGhlcmUuXG4gIGRvY3VtZW50LmNvb2tpZSA9IGxvY2FsZUNvb2tpZVN0cmluZztcbn1cblxuZXhwb3J0cy5kZWZhdWx0ID0gc3luY0xvY2FsZUNvb2tpZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/utils.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\n\n// Minor false positive: A route that has both optional and\n// required params will allow optional params.\n\n// For `Link`\n\n// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)\n\nfunction normalizeNameOrNameWithParams(href) {\n  return typeof href === 'string' ? {\n    pathname: href\n  } : href;\n}\nfunction serializeSearchParams(searchParams) {\n  function serializeValue(value) {\n    return String(value);\n  }\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(searchParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(cur => {\n        urlSearchParams.append(key, serializeValue(cur));\n      });\n    } else {\n      urlSearchParams.set(key, serializeValue(value));\n    }\n  }\n  return '?' + urlSearchParams.toString();\n}\nfunction compileLocalizedPathname(_ref) {\n  let {\n    pathname,\n    locale,\n    params,\n    pathnames,\n    query\n  } = _ref;\n  function getNamedPath(value) {\n    let namedPath = pathnames[value];\n    if (!namedPath) {\n      // Unknown pathnames\n      namedPath = value;\n    }\n    return namedPath;\n  }\n  function compilePath(namedPath) {\n    const template = typeof namedPath === 'string' ? namedPath : namedPath[locale];\n    let compiled = template;\n    if (params) {\n      Object.entries(params).forEach(_ref2 => {\n        let [key, value] = _ref2;\n        let regexp, replacer;\n        if (Array.isArray(value)) {\n          regexp = \"(\\\\[)?\\\\[...\".concat(key, \"\\\\](\\\\])?\");\n          replacer = value.map(v => String(v)).join('/');\n        } else {\n          regexp = \"\\\\[\".concat(key, \"\\\\]\");\n          replacer = String(value);\n        }\n        compiled = compiled.replace(new RegExp(regexp, 'g'), replacer);\n      });\n    }\n\n    // Clean up optional catch-all segments that were not replaced\n    compiled = compiled.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, '');\n    compiled = utils.normalizeTrailingSlash(compiled);\n    if (compiled.includes('[')) {\n      // Next.js throws anyway, therefore better provide a more helpful error message\n      throw new Error(\"Insufficient params provided for localized pathname.\\nTemplate: \".concat(template, \"\\nParams: \").concat(JSON.stringify(params)));\n    }\n    if (query) {\n      compiled += serializeSearchParams(query);\n    }\n    return compiled;\n  }\n  if (typeof pathname === 'string') {\n    const namedPath = getNamedPath(pathname);\n    const compiled = compilePath(namedPath);\n    return compiled;\n  } else {\n    const {\n      pathname: href,\n      ...rest\n    } = pathname;\n    const namedPath = getNamedPath(href);\n    const compiled = compilePath(namedPath);\n    const result = {\n      ...rest,\n      pathname: compiled\n    };\n    return result;\n  }\n}\nfunction getRoute(locale, pathname, pathnames) {\n  const sortedPathnames = utils.getSortedPathnames(Object.keys(pathnames));\n  const decoded = decodeURI(pathname);\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if (utils.matchesPathname(localizedPathname, decoded)) {\n        return internalPathname;\n      }\n    } else {\n      if (utils.matchesPathname(localizedPathnamesOrPathname[locale], decoded)) {\n        return internalPathname;\n      }\n    }\n  }\n  return pathname;\n}\nfunction getBasePath(pathname) {\n  let windowPathname = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : window.location.pathname;\n  if (pathname === '/') {\n    return windowPathname;\n  } else {\n    return windowPathname.replace(pathname, '');\n  }\n}\nfunction applyPathnamePrefix(pathname, locale, routing, domain, force) {\n  const {\n    mode\n  } = routing.localePrefix;\n  let shouldPrefix;\n  if (force !== undefined) {\n    shouldPrefix = force;\n  } else if (utils.isLocalizableHref(pathname)) {\n    if (mode === 'always') {\n      shouldPrefix = true;\n    } else if (mode === 'as-needed') {\n      let defaultLocale = routing.defaultLocale;\n      if (routing.domains) {\n        const domainConfig = routing.domains.find(cur => cur.domain === domain);\n        if (domainConfig) {\n          defaultLocale = domainConfig.defaultLocale;\n        } else {\n          if (!domain) {\n            console.error(\"You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\\n\\nSee: https://next-intl.dev/docs/routing#domains-localeprefix-asneeded\");\n          }\n        }\n      }\n      shouldPrefix = defaultLocale !== locale;\n    }\n  }\n  return shouldPrefix ? utils.prefixPathname(utils.getLocalePrefix(locale, routing.localePrefix), pathname) : pathname;\n}\nfunction validateReceivedConfig(config) {\n  var _config$localePrefix;\n  if (((_config$localePrefix = config.localePrefix) === null || _config$localePrefix === void 0 ? void 0 : _config$localePrefix.mode) === 'as-needed' && !('defaultLocale' in config)) {\n    throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n  }\n}\n\nexports.applyPathnamePrefix = applyPathnamePrefix;\nexports.compileLocalizedPathname = compileLocalizedPathname;\nexports.getBasePath = getBasePath;\nexports.getRoute = getRoute;\nexports.normalizeNameOrNameWithParams = normalizeNameOrNameWithParams;\nexports.serializeSearchParams = serializeSearchParams;\nexports.validateReceivedConfig = validateReceivedConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/react-client/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/react-client/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar useIntl = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/index.js\");\n\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */\n\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n  return function () {\n    try {\n      return hook(...arguments);\n    } catch (_unused) {\n      throw new Error(\"Failed to call `\".concat(name, \"` because the context from `NextIntlClientProvider` was not found.\\n\\nThis can happen because:\\n1) You intended to render this component as a Server Component, the render\\n   failed, and therefore React attempted to render the component on the client\\n   instead. If this is the case, check the console for server errors.\\n2) You intended to render this component on the client side, but no context was found.\\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context\") );\n    }\n  };\n}\nconst useTranslations = callHook('useTranslations', useIntl.useTranslations);\nconst useFormatter = callHook('useFormatter', useIntl.useFormatter);\n\nexports.useFormatter = useFormatter;\nexports.useTranslations = useTranslations;\nObject.keys(useIntl).forEach(function (k) {\n  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n    enumerable: true,\n    get: function () { return useIntl[k]; }\n  });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/react-client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/react-client/useLocale.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar _useLocale = __webpack_require__(/*! use-intl/_useLocale */ \"(ssr)/./node_modules/use-intl/dist/_useLocale.js\");\nvar constants = __webpack_require__(/*! ../shared/constants.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/constants.js\");\n\nlet hasWarnedForParams = false;\nfunction useLocale() {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n  const params = navigation.useParams();\n  let locale;\n  try {\n    // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks, react-compiler/react-compiler -- False positive\n    locale = _useLocale.useLocale();\n  } catch (error) {\n    if (typeof (params === null || params === void 0 ? void 0 : params[constants.LOCALE_SEGMENT_NAME]) === 'string') {\n      if (!hasWarnedForParams) {\n        console.warn('Deprecation warning: `useLocale` has returned a default from `useParams().locale` since no `NextIntlClientProvider` ancestor was found for the calling component. This behavior will be removed in the next major version. Please ensure all Client Components that use `next-intl` are wrapped in a `NextIntlClientProvider`.');\n        hasWarnedForParams = true;\n      }\n      locale = params[constants.LOCALE_SEGMENT_NAME];\n    } else {\n      throw error;\n    }\n  }\n  return locale;\n}\n\nexports[\"default\"] = useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/routing/config.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing/config.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction receiveRoutingConfig(input) {\n  var _input$localeDetectio, _input$alternateLinks;\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: (_input$localeDetectio = input.localeDetection) !== null && _input$localeDetectio !== void 0 ? _input$localeDetectio : true,\n    alternateLinks: (_input$alternateLinks = input.alternateLinks) !== null && _input$alternateLinks !== void 0 ? _input$alternateLinks : true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return (localeCookie !== null && localeCookie !== void 0 ? localeCookie : true) ? {\n    name: 'NEXT_LOCALE',\n    maxAge: ********,\n    // 1 year\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\nexports.receiveLocaleCookie = receiveLocaleCookie;\nexports.receiveLocalePrefixConfig = receiveLocalePrefixConfig;\nexports.receiveRoutingConfig = receiveRoutingConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/routing/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar _IntlProvider = __webpack_require__(/*! use-intl/_IntlProvider */ \"(ssr)/./node_modules/use-intl/dist/_IntlProvider.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction NextIntlClientProvider(_ref) {\n    let { locale, ...rest } = _ref;\n    // TODO: We could call `useParams` here to receive a default value\n    // for `locale`, but this would require dropping Next.js <13.\n    if (!locale) {\n        throw new Error(\"Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    }\n    return /*#__PURE__*/ React__default.default.createElement(_IntlProvider.IntlProvider, _rollupPluginBabelHelpers.extends({\n        locale: locale\n    }, rest));\n}\nexports[\"default\"] = NextIntlClientProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/constants.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/constants.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\n// Should take precedence over the cookie\nconst HEADER_LOCALE_NAME = 'X-NEXT-INTL-LOCALE';\n\n// In a URL like \"/en-US/about\", the locale segment is \"en-US\"\nconst LOCALE_SEGMENT_NAME = 'locale';\n\nexports.HEADER_LOCALE_NAME = HEADER_LOCALE_NAME;\nexports.LOCALE_SEGMENT_NAME = LOCALE_SEGMENT_NAME;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvc2hhcmVkL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdEO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSwwQkFBMEI7QUFDMUIsMkJBQTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50bDItY29tcGFueXByb2ZpbGUtdjAyLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2RldmVsb3BtZW50L3NoYXJlZC9jb25zdGFudHMuanM/NDRiMiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbi8vIFNob3VsZCB0YWtlIHByZWNlZGVuY2Ugb3ZlciB0aGUgY29va2llXG5jb25zdCBIRUFERVJfTE9DQUxFX05BTUUgPSAnWC1ORVhULUlOVEwtTE9DQUxFJztcblxuLy8gSW4gYSBVUkwgbGlrZSBcIi9lbi1VUy9hYm91dFwiLCB0aGUgbG9jYWxlIHNlZ21lbnQgaXMgXCJlbi1VU1wiXG5jb25zdCBMT0NBTEVfU0VHTUVOVF9OQU1FID0gJ2xvY2FsZSc7XG5cbmV4cG9ydHMuSEVBREVSX0xPQ0FMRV9OQU1FID0gSEVBREVSX0xPQ0FMRV9OQU1FO1xuZXhwb3J0cy5MT0NBTEVfU0VHTUVOVF9OQU1FID0gTE9DQUxFX1NFR01FTlRfTkFNRTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction localizeHref(href, locale) {\n  let curLocale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : locale;\n  let curPathname = arguments.length > 3 ? arguments[3] : undefined;\n  let prefix = arguments.length > 4 ? arguments[4] : undefined;\n  if (!isLocalizableHref(href)) {\n    return href;\n  }\n  const isSwitchingLocale = locale !== curLocale;\n  const isPathnamePrefixed = hasPathnamePrefixed(prefix, curPathname);\n  const shouldPrefix = isSwitchingLocale || isPathnamePrefixed;\n  if (shouldPrefix && prefix != null) {\n    return prefixHref(href, prefix);\n  }\n  return href;\n}\nfunction prefixHref(href, prefix) {\n  let prefixedHref;\n  if (typeof href === 'string') {\n    prefixedHref = prefixPathname(prefix, href);\n  } else {\n    prefixedHref = {\n      ...href\n    };\n    if (href.pathname) {\n      prefixedHref.pathname = prefixPathname(prefix, href.pathname);\n    }\n  }\n  return prefixedHref;\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(\"^\".concat(prefix)), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(\"\".concat(prefix, \"/\"));\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch (_unused) {\n    return false;\n  }\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  if (pathname !== '/') {\n    const pathnameEndsWithSlash = pathname.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      pathname += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      pathname = pathname.slice(0, -1);\n    }\n  }\n  return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  var _localePrefix$prefixe;\n  return localePrefix.mode !== 'never' && ((_localePrefix$prefixe = localePrefix.prefixes) === null || _localePrefix$prefixe === void 0 ? void 0 : _localePrefix$prefixe[locale]) ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(\"^\".concat(regexPattern, \"$\"));\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\nexports.getLocaleAsPrefix = getLocaleAsPrefix;\nexports.getLocalePrefix = getLocalePrefix;\nexports.getSortedPathnames = getSortedPathnames;\nexports.hasPathnamePrefixed = hasPathnamePrefixed;\nexports.isLocalizableHref = isLocalizableHref;\nexports.isPromise = isPromise;\nexports.localizeHref = localizeHref;\nexports.matchesPathname = matchesPathname;\nexports.normalizeTrailingSlash = normalizeTrailingSlash;\nexports.prefixHref = prefixHref;\nexports.prefixPathname = prefixPathname;\nexports.templateToRegex = templateToRegex;\nexports.unprefixPathname = unprefixPathname;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"extends\": () => (/* binding */ n)\n/* harmony export */ });\nfunction n(){return n=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)({}).hasOwnProperty.call(t,a)&&(n[a]=t[a])}return n},n.apply(null,arguments)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGFBQWEsd0RBQXdELFlBQVksbUJBQW1CLEtBQUssbUJBQW1CLGtCQUFrQix3Q0FBd0MsU0FBUyx5QkFBOEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanM/ZGNmNCJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBuKCl7cmV0dXJuIG49T2JqZWN0LmFzc2lnbj9PYmplY3QuYXNzaWduLmJpbmQoKTpmdW5jdGlvbihuKXtmb3IodmFyIHI9MTtyPGFyZ3VtZW50cy5sZW5ndGg7cisrKXt2YXIgdD1hcmd1bWVudHNbcl07Zm9yKHZhciBhIGluIHQpKHt9KS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHQsYSkmJihuW2FdPXRbYV0pfXJldHVybiBufSxuLmFwcGx5KG51bGwsYXJndW1lbnRzKX1leHBvcnR7biBhcyBleHRlbmRzfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/_IntlProvider */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction r(r) {\n    let { locale: o, ...i } = r;\n    if (!o) throw new Error(\"Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__[\"extends\"])({\n        locale: o\n    }, i));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OzZEQUNtRTtBQUFxQjtBQUFzRDtBQUFBLFNBQVNLLEVBQUVBLENBQUM7SUFBRSxJQUFHLEVBQUNDLFFBQU9DLENBQUMsRUFBQyxHQUFHQyxHQUFFLEdBQUNIO0lBQUUsSUFBRyxDQUFDRSxHQUFFLE1BQU0sSUFBSUUsTUFBTTtJQUErSixxQkFBT1AsMERBQWUsQ0FBQ0UsK0RBQUNBLEVBQUNILGdGQUFDQSxDQUFDO1FBQUNLLFFBQU9DO0lBQUMsR0FBRUM7QUFBRztBQUFzQiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGwyLWNvbXBhbnlwcm9maWxlLXYwMi8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanM/ZGNlNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydHtleHRlbmRzIGFzIGV9ZnJvbVwiLi4vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qc1wiO2ltcG9ydCBsIGZyb21cInJlYWN0XCI7aW1wb3J0e0ludGxQcm92aWRlciBhcyB0fWZyb21cInVzZS1pbnRsL19JbnRsUHJvdmlkZXJcIjtmdW5jdGlvbiByKHIpe2xldHtsb2NhbGU6bywuLi5pfT1yO2lmKCFvKXRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBkZXRlcm1pbmUgbG9jYWxlIGluIGBOZXh0SW50bENsaWVudFByb3ZpZGVyYCwgcGxlYXNlIHByb3ZpZGUgdGhlIGBsb2NhbGVgIHByb3AgZXhwbGljaXRseS5cXG5cXG5TZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvY29uZmlndXJhdGlvbiNsb2NhbGVcIik7cmV0dXJuIGwuY3JlYXRlRWxlbWVudCh0LGUoe2xvY2FsZTpvfSxpKSl9ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOlsiZXh0ZW5kcyIsImUiLCJsIiwiSW50bFByb3ZpZGVyIiwidCIsInIiLCJsb2NhbGUiLCJvIiwiaSIsIkVycm9yIiwiY3JlYXRlRWxlbWVudCIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/development/routing.js":
/*!************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar defineRouting = __webpack_require__(/*! ./routing/defineRouting.js */ \"(rsc)/./node_modules/next-intl/dist/development/routing/defineRouting.js\");\n\n\n\nexports.defineRouting = defineRouting.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvcm91dGluZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdELG9CQUFvQixtQkFBTyxDQUFDLDRHQUE0Qjs7OztBQUl4RCxxQkFBcUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvcm91dGluZy5qcz9iMmFmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxudmFyIGRlZmluZVJvdXRpbmcgPSByZXF1aXJlKCcuL3JvdXRpbmcvZGVmaW5lUm91dGluZy5qcycpO1xuXG5cblxuZXhwb3J0cy5kZWZpbmVSb3V0aW5nID0gZGVmaW5lUm91dGluZy5kZWZhdWx0O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/routing.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/development/routing/defineRouting.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing/defineRouting.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction defineRouting(config) {\n  return config;\n}\n\nexports[\"default\"] = defineRouting;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvcm91dGluZy9kZWZpbmVSb3V0aW5nLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0Q7QUFDQTtBQUNBOztBQUVBLGtCQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50bDItY29tcGFueXByb2ZpbGUtdjAyLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2RldmVsb3BtZW50L3JvdXRpbmcvZGVmaW5lUm91dGluZy5qcz80MzBhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxuZnVuY3Rpb24gZGVmaW5lUm91dGluZyhjb25maWcpIHtcbiAgcmV0dXJuIGNvbmZpZztcbn1cblxuZXhwb3J0cy5kZWZhdWx0ID0gZGVmaW5lUm91dGluZztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/routing/defineRouting.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"extends\": () => (/* binding */ n)\n/* harmony export */ });\nfunction n(){return n=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)({}).hasOwnProperty.call(t,a)&&(n[a]=t[a])}return n},n.apply(null,arguments)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGFBQWEsd0RBQXdELFlBQVksbUJBQW1CLEtBQUssbUJBQW1CLGtCQUFrQix3Q0FBd0MsU0FBUyx5QkFBOEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanM/NjFjYSJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBuKCl7cmV0dXJuIG49T2JqZWN0LmFzc2lnbj9PYmplY3QuYXNzaWduLmJpbmQoKTpmdW5jdGlvbihuKXtmb3IodmFyIHI9MTtyPGFyZ3VtZW50cy5sZW5ndGg7cisrKXt2YXIgdD1hcmd1bWVudHNbcl07Zm9yKHZhciBhIGluIHQpKHt9KS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHQsYSkmJihuW2FdPXRbYV0pfXJldHVybiBufSxuLmFwcGx5KG51bGwsYXJndW1lbnRzKX1leHBvcnR7biBhcyBleHRlbmRzfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/index.react-server.js":
/*!***************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/index.react-server.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlError: () => (/* reexport safe */ use_intl_core__WEBPACK_IMPORTED_MODULE_7__.IntlError),\n/* harmony export */   IntlErrorCode: () => (/* reexport safe */ use_intl_core__WEBPACK_IMPORTED_MODULE_7__.IntlErrorCode),\n/* harmony export */   NextIntlClientProvider: () => (/* reexport safe */ _react_server_NextIntlClientProviderServer_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   __esModule: () => (/* reexport safe */ use_intl_core__WEBPACK_IMPORTED_MODULE_7__.__esModule),\n/* harmony export */   _createCache: () => (/* reexport safe */ use_intl_core__WEBPACK_IMPORTED_MODULE_7__._createCache),\n/* harmony export */   _createIntlFormatters: () => (/* reexport safe */ use_intl_core__WEBPACK_IMPORTED_MODULE_7__._createIntlFormatters),\n/* harmony export */   createFormatter: () => (/* reexport safe */ use_intl_core__WEBPACK_IMPORTED_MODULE_7__.createFormatter),\n/* harmony export */   createTranslator: () => (/* reexport safe */ use_intl_core__WEBPACK_IMPORTED_MODULE_7__.createTranslator),\n/* harmony export */   initializeConfig: () => (/* reexport safe */ use_intl_core__WEBPACK_IMPORTED_MODULE_7__.initializeConfig),\n/* harmony export */   useFormatter: () => (/* reexport safe */ _react_server_useFormatter_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   useLocale: () => (/* reexport safe */ _react_server_useLocale_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   useMessages: () => (/* reexport safe */ _react_server_useMessages_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   useNow: () => (/* reexport safe */ _react_server_useNow_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   useTimeZone: () => (/* reexport safe */ _react_server_useTimeZone_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   useTranslations: () => (/* reexport safe */ _react_server_useTranslations_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _react_server_useLocale_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./react-server/useLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useLocale.js\");\n/* harmony import */ var _react_server_useTranslations_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./react-server/useTranslations.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\");\n/* harmony import */ var _react_server_useFormatter_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./react-server/useFormatter.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useFormatter.js\");\n/* harmony import */ var _react_server_useNow_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./react-server/useNow.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useNow.js\");\n/* harmony import */ var _react_server_useTimeZone_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./react-server/useTimeZone.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useTimeZone.js\");\n/* harmony import */ var _react_server_useMessages_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./react-server/useMessages.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useMessages.js\");\n/* harmony import */ var _react_server_NextIntlClientProviderServer_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./react-server/NextIntlClientProviderServer.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/core.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2luZGV4LnJlYWN0LXNlcnZlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFpZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2luZGV4LnJlYWN0LXNlcnZlci5qcz8xYjA0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydHtkZWZhdWx0IGFzIHVzZUxvY2FsZX1mcm9tXCIuL3JlYWN0LXNlcnZlci91c2VMb2NhbGUuanNcIjtleHBvcnR7ZGVmYXVsdCBhcyB1c2VUcmFuc2xhdGlvbnN9ZnJvbVwiLi9yZWFjdC1zZXJ2ZXIvdXNlVHJhbnNsYXRpb25zLmpzXCI7ZXhwb3J0e2RlZmF1bHQgYXMgdXNlRm9ybWF0dGVyfWZyb21cIi4vcmVhY3Qtc2VydmVyL3VzZUZvcm1hdHRlci5qc1wiO2V4cG9ydHtkZWZhdWx0IGFzIHVzZU5vd31mcm9tXCIuL3JlYWN0LXNlcnZlci91c2VOb3cuanNcIjtleHBvcnR7ZGVmYXVsdCBhcyB1c2VUaW1lWm9uZX1mcm9tXCIuL3JlYWN0LXNlcnZlci91c2VUaW1lWm9uZS5qc1wiO2V4cG9ydHtkZWZhdWx0IGFzIHVzZU1lc3NhZ2VzfWZyb21cIi4vcmVhY3Qtc2VydmVyL3VzZU1lc3NhZ2VzLmpzXCI7ZXhwb3J0e2RlZmF1bHQgYXMgTmV4dEludGxDbGllbnRQcm92aWRlcn1mcm9tXCIuL3JlYWN0LXNlcnZlci9OZXh0SW50bENsaWVudFByb3ZpZGVyU2VydmVyLmpzXCI7ZXhwb3J0KmZyb21cInVzZS1pbnRsL2NvcmVcIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/index.react-server.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ i)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\");\n/* harmony import */ var _server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../server/react-server/getLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js\");\n/* harmony import */ var _server_react_server_getNow_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../server/react-server/getNow.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js\");\n/* harmony import */ var _server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../server/react-server/getTimeZone.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js\");\nasync function i(i){let{locale:n,now:s,timeZone:m,...c}=i;return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],(0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__[\"extends\"])({locale:null!=n?n:await (0,_server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(),now:null!=s?s:await (0,_server_react_server_getNow_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),timeZone:null!=m?m:await (0,_server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()},c))}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci9OZXh0SW50bENsaWVudFByb3ZpZGVyU2VydmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQW1TLG9CQUFvQixJQUFJLCtCQUErQixHQUFHLE9BQU8sMERBQWUsQ0FBQyx5RUFBQyxDQUFDLGdGQUFDLEVBQUUsdUJBQXVCLDZFQUFDLHVCQUF1QiwwRUFBQyw0QkFBNEIsK0VBQUMsR0FBRyxLQUEwQiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGwyLWNvbXBhbnlwcm9maWxlLXYwMi8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vcmVhY3Qtc2VydmVyL05leHRJbnRsQ2xpZW50UHJvdmlkZXJTZXJ2ZXIuanM/YTkyMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7ZXh0ZW5kcyBhcyBlfWZyb21cIi4uL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanNcIjtpbXBvcnQgciBmcm9tXCJyZWFjdFwiO2ltcG9ydCB0IGZyb21cIi4uL3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzXCI7aW1wb3J0IG8gZnJvbVwiLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRMb2NhbGUuanNcIjtpbXBvcnQgbCBmcm9tXCIuLi9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldE5vdy5qc1wiO2ltcG9ydCBhIGZyb21cIi4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanNcIjthc3luYyBmdW5jdGlvbiBpKGkpe2xldHtsb2NhbGU6bixub3c6cyx0aW1lWm9uZTptLC4uLmN9PWk7cmV0dXJuIHIuY3JlYXRlRWxlbWVudCh0LGUoe2xvY2FsZTpudWxsIT1uP246YXdhaXQgbygpLG5vdzpudWxsIT1zP3M6YXdhaXQgbCgpLHRpbWVab25lOm51bGwhPW0/bTphd2FpdCBhKCl9LGMpKX1leHBvcnR7aSBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/getTranslator.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/getTranslator.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\nvar t=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((function(r,t){return (0,use_intl_core__WEBPACK_IMPORTED_MODULE_1__.createTranslator)({...r,namespace:t})}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci9nZXRUcmFuc2xhdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0UsTUFBTSw0Q0FBQyxnQkFBZ0IsT0FBTywrREFBQyxFQUFFLGlCQUFpQixFQUFFLEdBQXdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50bDItY29tcGFueXByb2ZpbGUtdjAyLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9yZWFjdC1zZXJ2ZXIvZ2V0VHJhbnNsYXRvci5qcz82OGVmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtjYWNoZSBhcyByfWZyb21cInJlYWN0XCI7aW1wb3J0e2NyZWF0ZVRyYW5zbGF0b3IgYXMgZX1mcm9tXCJ1c2UtaW50bC9jb3JlXCI7dmFyIHQ9cigoZnVuY3Rpb24ocix0KXtyZXR1cm4gZSh7Li4ucixuYW1lc3BhY2U6dH0pfSkpO2V4cG9ydHt0IGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/getTranslator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/useConfig.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../server/react-server/getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nfunction r(r){return function(n,r){try{return (0,react__WEBPACK_IMPORTED_MODULE_0__.use)(r)}catch(e){throw e instanceof TypeError&&e.message.includes(\"Cannot read properties of null (reading 'use')\")?new Error(\"`\".concat(n,\"` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components\"),{cause:e}):e}}(r,(0,_server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])())}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUErRSxjQUFjLHFCQUFxQixJQUFJLE9BQU8sMENBQUMsSUFBSSxTQUFTLDZRQUE2USxRQUFRLEtBQUssR0FBRyw2RUFBQyxJQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGwyLWNvbXBhbnlwcm9maWxlLXYwMi8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vcmVhY3Qtc2VydmVyL3VzZUNvbmZpZy5qcz9lZGZkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHt1c2UgYXMgZX1mcm9tXCJyZWFjdFwiO2ltcG9ydCBuIGZyb21cIi4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Q29uZmlnLmpzXCI7ZnVuY3Rpb24gcihyKXtyZXR1cm4gZnVuY3Rpb24obixyKXt0cnl7cmV0dXJuIGUocil9Y2F0Y2goZSl7dGhyb3cgZSBpbnN0YW5jZW9mIFR5cGVFcnJvciYmZS5tZXNzYWdlLmluY2x1ZGVzKFwiQ2Fubm90IHJlYWQgcHJvcGVydGllcyBvZiBudWxsIChyZWFkaW5nICd1c2UnKVwiKT9uZXcgRXJyb3IoXCJgXCIuY29uY2F0KG4sXCJgIGlzIG5vdCBjYWxsYWJsZSB3aXRoaW4gYW4gYXN5bmMgY29tcG9uZW50LiBQbGVhc2UgcmVmZXIgdG8gaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvZW52aXJvbm1lbnRzL3NlcnZlci1jbGllbnQtY29tcG9uZW50cyNhc3luYy1jb21wb25lbnRzXCIpLHtjYXVzZTplfSk6ZX19KHIsbigpKX1leHBvcnR7ciBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/useFormatter.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/useFormatter.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ n)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js\");\nconst e=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_1__.createFormatter);function n(){for(var r=arguments.length,o=new Array(r),n=0;n<r;n++)o[n]=arguments[n];const f=(0,_useConfig_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\"useFormatter\");return e(f)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VGb3JtYXR0ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEcsUUFBUSw0Q0FBQyxDQUFDLDBEQUFDLEVBQUUsYUFBYSw4Q0FBOEMsSUFBSSxzQkFBc0IsUUFBUSx5REFBQyxpQkFBaUIsWUFBaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VGb3JtYXR0ZXIuanM/NmZmNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgcn1mcm9tXCJyZWFjdFwiO2ltcG9ydHtjcmVhdGVGb3JtYXR0ZXIgYXMgb31mcm9tXCJ1c2UtaW50bC9jb3JlXCI7aW1wb3J0IHQgZnJvbVwiLi91c2VDb25maWcuanNcIjtjb25zdCBlPXIobyk7ZnVuY3Rpb24gbigpe2Zvcih2YXIgcj1hcmd1bWVudHMubGVuZ3RoLG89bmV3IEFycmF5KHIpLG49MDtuPHI7bisrKW9bbl09YXJndW1lbnRzW25dO2NvbnN0IGY9dChcInVzZUZvcm1hdHRlclwiKTtyZXR1cm4gZShmKX1leHBvcnR7biBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useFormatter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/useLocale.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/useLocale.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js\");\nfunction r(){for(var r=arguments.length,o=new Array(r),a=0;a<r;a++)o[a]=arguments[a];return (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"useLocale\").locale}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VMb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEIsYUFBYSw4Q0FBOEMsSUFBSSxzQkFBc0IsT0FBTyx5REFBQyxxQkFBMEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VMb2NhbGUuanM/MzFjNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZSBmcm9tXCIuL3VzZUNvbmZpZy5qc1wiO2Z1bmN0aW9uIHIoKXtmb3IodmFyIHI9YXJndW1lbnRzLmxlbmd0aCxvPW5ldyBBcnJheShyKSxhPTA7YTxyO2ErKylvW2FdPWFyZ3VtZW50c1thXTtyZXR1cm4gZShcInVzZUxvY2FsZVwiKS5sb2NhbGV9ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/useMessages.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/useMessages.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getMessages_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../server/react-server/getMessages.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js\");\nfunction s(){for(var s=arguments.length,t=new Array(s),o=0;o<s;o++)t[o]=arguments[o];const a=(0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"useMessages\");return (0,_server_react_server_getMessages_js__WEBPACK_IMPORTED_MODULE_1__.getMessagesFromConfig)(a)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VNZXNzYWdlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEcsYUFBYSw4Q0FBOEMsSUFBSSxzQkFBc0IsUUFBUSx5REFBQyxnQkFBZ0IsT0FBTywwRkFBQyxJQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGwyLWNvbXBhbnlwcm9maWxlLXYwMi8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vcmVhY3Qtc2VydmVyL3VzZU1lc3NhZ2VzLmpzPzlmNTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2dldE1lc3NhZ2VzRnJvbUNvbmZpZyBhcyBlfWZyb21cIi4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TWVzc2FnZXMuanNcIjtpbXBvcnQgciBmcm9tXCIuL3VzZUNvbmZpZy5qc1wiO2Z1bmN0aW9uIHMoKXtmb3IodmFyIHM9YXJndW1lbnRzLmxlbmd0aCx0PW5ldyBBcnJheShzKSxvPTA7bzxzO28rKyl0W29dPWFyZ3VtZW50c1tvXTtjb25zdCBhPXIoXCJ1c2VNZXNzYWdlc1wiKTtyZXR1cm4gZShhKX1leHBvcnR7cyBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useMessages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/useNow.js":
/*!****************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/useNow.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ o)\n/* harmony export */ });\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js\");\nfunction o(){for(var o=arguments.length,n=new Array(o),t=0;t<o;t++)n[t]=arguments[t];let[r]=n;null!=(null==r?void 0:r.updateInterval)&&console.error(\"`useNow` doesn't support the `updateInterval` option in Server Components, the value will be ignored. If you need the value to update, you can convert the component to a Client Component.\");return (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"useNow\").now}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VOb3cuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEIsYUFBYSw4Q0FBOEMsSUFBSSxzQkFBc0IsU0FBUyxzUEFBc1AsT0FBTyx5REFBQyxlQUFvQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGwyLWNvbXBhbnlwcm9maWxlLXYwMi8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vcmVhY3Qtc2VydmVyL3VzZU5vdy5qcz81Y2JkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBlIGZyb21cIi4vdXNlQ29uZmlnLmpzXCI7ZnVuY3Rpb24gbygpe2Zvcih2YXIgbz1hcmd1bWVudHMubGVuZ3RoLG49bmV3IEFycmF5KG8pLHQ9MDt0PG87dCsrKW5bdF09YXJndW1lbnRzW3RdO2xldFtyXT1uO251bGwhPShudWxsPT1yP3ZvaWQgMDpyLnVwZGF0ZUludGVydmFsKSYmY29uc29sZS5lcnJvcihcImB1c2VOb3dgIGRvZXNuJ3Qgc3VwcG9ydCB0aGUgYHVwZGF0ZUludGVydmFsYCBvcHRpb24gaW4gU2VydmVyIENvbXBvbmVudHMsIHRoZSB2YWx1ZSB3aWxsIGJlIGlnbm9yZWQuIElmIHlvdSBuZWVkIHRoZSB2YWx1ZSB0byB1cGRhdGUsIHlvdSBjYW4gY29udmVydCB0aGUgY29tcG9uZW50IHRvIGEgQ2xpZW50IENvbXBvbmVudC5cIik7cmV0dXJuIGUoXCJ1c2VOb3dcIikubm93fWV4cG9ydHtvIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useNow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/useTimeZone.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/useTimeZone.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js\");\nfunction r(){for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return (0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"useTimeZone\").timeZone}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VUaW1lWm9uZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4QixhQUFhLDhDQUE4QyxJQUFJLHNCQUFzQixPQUFPLHlEQUFDLHlCQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGwyLWNvbXBhbnlwcm9maWxlLXYwMi8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vcmVhY3Qtc2VydmVyL3VzZVRpbWVab25lLmpzPzVkNjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGUgZnJvbVwiLi91c2VDb25maWcuanNcIjtmdW5jdGlvbiByKCl7Zm9yKHZhciByPWFyZ3VtZW50cy5sZW5ndGgsbj1uZXcgQXJyYXkociksbz0wO288cjtvKyspbltvXT1hcmd1bWVudHNbb107cmV0dXJuIGUoXCJ1c2VUaW1lWm9uZVwiKS50aW1lWm9uZX1leHBvcnR7ciBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTimeZone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/useTranslations.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ o)\n/* harmony export */ });\n/* harmony import */ var _getTranslator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getTranslator.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/getTranslator.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js\");\nfunction o(){for(var o=arguments.length,n=new Array(o),e=0;e<o;e++)n[e]=arguments[e];let[s]=n;const a=(0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"useTranslations\");return (0,_getTranslator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(a,s)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VUcmFuc2xhdGlvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdFLGFBQWEsOENBQThDLElBQUksc0JBQXNCLFNBQVMsUUFBUSx5REFBQyxvQkFBb0IsT0FBTyw2REFBQyxNQUEyQiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGwyLWNvbXBhbnlwcm9maWxlLXYwMi8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vcmVhY3Qtc2VydmVyL3VzZVRyYW5zbGF0aW9ucy5qcz80NjYwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCByIGZyb21cIi4vZ2V0VHJhbnNsYXRvci5qc1wiO2ltcG9ydCB0IGZyb21cIi4vdXNlQ29uZmlnLmpzXCI7ZnVuY3Rpb24gbygpe2Zvcih2YXIgbz1hcmd1bWVudHMubGVuZ3RoLG49bmV3IEFycmF5KG8pLGU9MDtlPG87ZSsrKW5bZV09YXJndW1lbnRzW2VdO2xldFtzXT1uO2NvbnN0IGE9dChcInVzZVRyYW5zbGF0aW9uc1wiKTtyZXR1cm4gcihhLHMpfWV4cG9ydHtvIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ a)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\nconst i=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(){const e=(0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)();return (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_2__.isPromise)(e)?await e:e}));const s=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(){let t;try{t=(await i()).get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME)||void 0}catch(t){if(t instanceof Error&&\"DYNAMIC_SERVER_USAGE\"===t.digest){const e=new Error(\"Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering\",{cause:t});throw e.digest=t.digest,e}throw t}return t}));async function a(){return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)()||await s()}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXVQLFFBQVEsNENBQUMsbUJBQW1CLFFBQVEscURBQUMsR0FBRyxPQUFPLDJEQUFDLGNBQWMsR0FBRyxRQUFRLDRDQUFDLG1CQUFtQixNQUFNLElBQUksa0JBQWtCLG9FQUFDLFVBQVUsU0FBUywwREFBMEQsK1VBQStVLFFBQVEsRUFBRSwwQkFBMEIsUUFBUSxTQUFTLEdBQUcsbUJBQW1CLE9BQU8sOEVBQUMsY0FBNEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZS5qcz81ZTg3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtoZWFkZXJzIGFzIHR9ZnJvbVwibmV4dC9oZWFkZXJzXCI7aW1wb3J0e2NhY2hlIGFzIGV9ZnJvbVwicmVhY3RcIjtpbXBvcnR7SEVBREVSX0xPQ0FMRV9OQU1FIGFzIG59ZnJvbVwiLi4vLi4vc2hhcmVkL2NvbnN0YW50cy5qc1wiO2ltcG9ydHtpc1Byb21pc2UgYXMgcn1mcm9tXCIuLi8uLi9zaGFyZWQvdXRpbHMuanNcIjtpbXBvcnR7Z2V0Q2FjaGVkUmVxdWVzdExvY2FsZSBhcyBvfWZyb21cIi4vUmVxdWVzdExvY2FsZUNhY2hlLmpzXCI7Y29uc3QgaT1lKChhc3luYyBmdW5jdGlvbigpe2NvbnN0IGU9dCgpO3JldHVybiByKGUpP2F3YWl0IGU6ZX0pKTtjb25zdCBzPWUoKGFzeW5jIGZ1bmN0aW9uKCl7bGV0IHQ7dHJ5e3Q9KGF3YWl0IGkoKSkuZ2V0KG4pfHx2b2lkIDB9Y2F0Y2godCl7aWYodCBpbnN0YW5jZW9mIEVycm9yJiZcIkRZTkFNSUNfU0VSVkVSX1VTQUdFXCI9PT10LmRpZ2VzdCl7Y29uc3QgZT1uZXcgRXJyb3IoXCJVc2FnZSBvZiBuZXh0LWludGwgQVBJcyBpbiBTZXJ2ZXIgQ29tcG9uZW50cyBjdXJyZW50bHkgb3B0cyBpbnRvIGR5bmFtaWMgcmVuZGVyaW5nLiBUaGlzIGxpbWl0YXRpb24gd2lsbCBldmVudHVhbGx5IGJlIGxpZnRlZCwgYnV0IGFzIGEgc3RvcGdhcCBzb2x1dGlvbiwgeW91IGNhbiB1c2UgdGhlIGBzZXRSZXF1ZXN0TG9jYWxlYCBBUEkgdG8gZW5hYmxlIHN0YXRpYyByZW5kZXJpbmcsIHNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9nZXR0aW5nLXN0YXJ0ZWQvYXBwLXJvdXRlci93aXRoLWkxOG4tcm91dGluZyNzdGF0aWMtcmVuZGVyaW5nXCIse2NhdXNlOnR9KTt0aHJvdyBlLmRpZ2VzdD10LmRpZ2VzdCxlfXRocm93IHR9cmV0dXJuIHR9KSk7YXN5bmMgZnVuY3Rpb24gYSgpe3JldHVybiBvKCl8fGF3YWl0IHMoKX1leHBvcnR7YSBhcyBnZXRSZXF1ZXN0TG9jYWxlfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCachedRequestLocale: () => (/* binding */ t),\n/* harmony export */   setCachedRequestLocale: () => (/* binding */ c)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nconst n=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((function(){return{locale:void 0}}));function t(){return n().locale}function c(o){n().locale=o}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEIsUUFBUSw0Q0FBQyxhQUFhLE9BQU8sZUFBZSxHQUFHLGFBQWEsa0JBQWtCLGNBQWMsYUFBNkUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzP2Q0N2YiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2NhY2hlIGFzIG99ZnJvbVwicmVhY3RcIjtjb25zdCBuPW8oKGZ1bmN0aW9uKCl7cmV0dXJue2xvY2FsZTp2b2lkIDB9fSkpO2Z1bmN0aW9uIHQoKXtyZXR1cm4gbigpLmxvY2FsZX1mdW5jdGlvbiBjKG8pe24oKS5sb2NhbGU9b31leHBvcnR7dCBhcyBnZXRDYWNoZWRSZXF1ZXN0TG9jYWxlLGMgYXMgc2V0Q2FjaGVkUmVxdWVzdExvY2FsZX07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\nconst i=(0,react__WEBPACK_IMPORTED_MODULE_2__.cache)((function(){let n;try{n=(0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)().get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME)}catch(e){throw e instanceof Error&&\"DYNAMIC_SERVER_USAGE\"===e.digest?new Error(\"Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering\",{cause:e}):e}return n||(console.error(\"\\nUnable to find `next-intl` locale because the middleware didn't run on this request. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\\n\"),(0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)()),n}));function s(){return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)()||i()}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUxlZ2FjeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQWdQLFFBQVEsNENBQUMsYUFBYSxNQUFNLElBQUksRUFBRSxxREFBQyxPQUFPLG9FQUFDLEVBQUUsU0FBUyxtWUFBbVksUUFBUSxJQUFJLG9QQUFvUCx5REFBQyxNQUFNLEdBQUcsYUFBYSxPQUFPLDhFQUFDLFFBQXNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50bDItY29tcGFueXByb2ZpbGUtdjAyLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zZXJ2ZXIvcmVhY3Qtc2VydmVyL1JlcXVlc3RMb2NhbGVMZWdhY3kuanM/OWQ5ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7aGVhZGVycyBhcyBlfWZyb21cIm5leHQvaGVhZGVyc1wiO2ltcG9ydHtub3RGb3VuZCBhcyB0fWZyb21cIm5leHQvbmF2aWdhdGlvblwiO2ltcG9ydHtjYWNoZSBhcyBufWZyb21cInJlYWN0XCI7aW1wb3J0e0hFQURFUl9MT0NBTEVfTkFNRSBhcyBvfWZyb21cIi4uLy4uL3NoYXJlZC9jb25zdGFudHMuanNcIjtpbXBvcnR7Z2V0Q2FjaGVkUmVxdWVzdExvY2FsZSBhcyByfWZyb21cIi4vUmVxdWVzdExvY2FsZUNhY2hlLmpzXCI7Y29uc3QgaT1uKChmdW5jdGlvbigpe2xldCBuO3RyeXtuPWUoKS5nZXQobyl9Y2F0Y2goZSl7dGhyb3cgZSBpbnN0YW5jZW9mIEVycm9yJiZcIkRZTkFNSUNfU0VSVkVSX1VTQUdFXCI9PT1lLmRpZ2VzdD9uZXcgRXJyb3IoXCJVc2FnZSBvZiBuZXh0LWludGwgQVBJcyBpbiBTZXJ2ZXIgQ29tcG9uZW50cyBjdXJyZW50bHkgb3B0cyBpbnRvIGR5bmFtaWMgcmVuZGVyaW5nLiBUaGlzIGxpbWl0YXRpb24gd2lsbCBldmVudHVhbGx5IGJlIGxpZnRlZCwgYnV0IGFzIGEgc3RvcGdhcCBzb2x1dGlvbiwgeW91IGNhbiB1c2UgdGhlIGBzZXRSZXF1ZXN0TG9jYWxlYCBBUEkgdG8gZW5hYmxlIHN0YXRpYyByZW5kZXJpbmcsIHNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9nZXR0aW5nLXN0YXJ0ZWQvYXBwLXJvdXRlci93aXRoLWkxOG4tcm91dGluZyNzdGF0aWMtcmVuZGVyaW5nXCIse2NhdXNlOmV9KTplfXJldHVybiBufHwoY29uc29sZS5lcnJvcihcIlxcblVuYWJsZSB0byBmaW5kIGBuZXh0LWludGxgIGxvY2FsZSBiZWNhdXNlIHRoZSBtaWRkbGV3YXJlIGRpZG4ndCBydW4gb24gdGhpcyByZXF1ZXN0LiBTZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3Mvcm91dGluZy9taWRkbGV3YXJlI3VuYWJsZS10by1maW5kLWxvY2FsZS4gVGhlIGBub3RGb3VuZCgpYCBmdW5jdGlvbiB3aWxsIGJlIGNhbGxlZCBhcyBhIHJlc3VsdC5cXG5cIiksdCgpKSxufSkpO2Z1bmN0aW9uIHMoKXtyZXR1cm4gcigpfHxpKCl9ZXhwb3J0e3MgYXMgZ2V0UmVxdWVzdExvY2FsZX07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getConfig.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ w)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _RequestLocale_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js\");\n/* harmony import */ var _RequestLocaleLegacy_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RequestLocaleLegacy.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js\");\n/* harmony import */ var next_intl_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl/config */ \"(rsc)/./i18n/request.js\");\nlet c=!1,u=!1;const f=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((function(){return new Date}));const d=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}));const m=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(t,n){if(\"function\"!=typeof t)throw new Error(\"Invalid i18n request configuration detected.\\n\\nPlease verify that:\\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\\n2. You have a default export in your i18n request configuration file.\\n\\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\\n\");const o={get locale(){return u||(console.warn(\"\\nThe `locale` parameter in `getRequestConfig` is deprecated, please switch to `await requestLocale`. See https://next-intl.dev/blog/next-intl-3-22#await-request-locale\\n\"),u=!0),n||(0,_RequestLocaleLegacy_js__WEBPACK_IMPORTED_MODULE_3__.getRequestLocale)()},get requestLocale(){return n?Promise.resolve(n):(0,_RequestLocale_js__WEBPACK_IMPORTED_MODULE_4__.getRequestLocale)()}};let r=t(o);(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isPromise)(r)&&(r=await r);let s=r.locale;return s||(c||(console.error(\"\\nA `locale` is expected to be returned from `getRequestConfig`, but none was returned. This will be an error in the next major version of next-intl.\\n\\nSee: https://next-intl.dev/blog/next-intl-3-22#await-request-locale\\n\"),c=!0),s=await o.requestLocale,s||(console.error(\"\\nUnable to find `next-intl` locale because the middleware didn't run on this request and no `locale` was returned in `getRequestConfig`. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\\n\"),(0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)())),{...r,locale:s,now:r.now||f(),timeZone:r.timeZone||d()}})),p=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_6__._createIntlFormatters),g=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_6__._createCache);const w=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(e){const t=await m(next_intl_config__WEBPACK_IMPORTED_MODULE_2__[\"default\"],e);return{...(0,use_intl_core__WEBPACK_IMPORTED_MODULE_6__.initializeConfig)(t),_formatters:p(g())}}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getLocale.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nconst r=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(){const o=await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();return Promise.resolve(o.locale)}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEQsUUFBUSw0Q0FBQyxtQkFBbUIsY0FBYyx5REFBQyxHQUFHLGlDQUFpQyxHQUF3QiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGwyLWNvbXBhbnlwcm9maWxlLXYwMi8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRMb2NhbGUuanM/NzQzZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgb31mcm9tXCJyZWFjdFwiO2ltcG9ydCB0IGZyb21cIi4vZ2V0Q29uZmlnLmpzXCI7Y29uc3Qgcj1vKChhc3luYyBmdW5jdGlvbigpe2NvbnN0IG89YXdhaXQgdCgpO3JldHVybiBQcm9taXNlLnJlc29sdmUoby5sb2NhbGUpfSkpO2V4cG9ydHtyIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getMessages.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r),\n/* harmony export */   getMessagesFromConfig: () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nfunction t(e){if(!e.messages)throw new Error(\"No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages\");return e.messages}const n=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(e){return t(await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(e))}));async function r(e){return n(null==e?void 0:e.locale)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEQsY0FBYywrSUFBK0ksa0JBQWtCLFFBQVEsNENBQUMsb0JBQW9CLGVBQWUseURBQUMsS0FBSyxHQUFHLG9CQUFvQixrQ0FBa0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TWVzc2FnZXMuanM/MDBjMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgZX1mcm9tXCJyZWFjdFwiO2ltcG9ydCBvIGZyb21cIi4vZ2V0Q29uZmlnLmpzXCI7ZnVuY3Rpb24gdChlKXtpZighZS5tZXNzYWdlcyl0aHJvdyBuZXcgRXJyb3IoXCJObyBtZXNzYWdlcyBmb3VuZC4gSGF2ZSB5b3UgY29uZmlndXJlZCB0aGVtIGNvcnJlY3RseT8gU2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL2NvbmZpZ3VyYXRpb24jbWVzc2FnZXNcIik7cmV0dXJuIGUubWVzc2FnZXN9Y29uc3Qgbj1lKChhc3luYyBmdW5jdGlvbihlKXtyZXR1cm4gdChhd2FpdCBvKGUpKX0pKTthc3luYyBmdW5jdGlvbiByKGUpe3JldHVybiBuKG51bGw9PWU/dm9pZCAwOmUubG9jYWxlKX1leHBvcnR7ciBhcyBkZWZhdWx0LHQgYXMgZ2V0TWVzc2FnZXNGcm9tQ29uZmlnfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getNow.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nconst t=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(n){return(await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(n)).now}));async function r(n){return t(null==n?void 0:n.locale)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Tm93LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEQsUUFBUSw0Q0FBQyxvQkFBb0IsYUFBYSx5REFBQyxTQUFTLEdBQUcsb0JBQW9CLGtDQUF1RCIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGwyLWNvbXBhbnlwcm9maWxlLXYwMi8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2VydmVyL3JlYWN0LXNlcnZlci9nZXROb3cuanM/NmEwOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgbn1mcm9tXCJyZWFjdFwiO2ltcG9ydCBvIGZyb21cIi4vZ2V0Q29uZmlnLmpzXCI7Y29uc3QgdD1uKChhc3luYyBmdW5jdGlvbihuKXtyZXR1cm4oYXdhaXQgbyhuKSkubm93fSkpO2FzeW5jIGZ1bmN0aW9uIHIobil7cmV0dXJuIHQobnVsbD09bj92b2lkIDA6bi5sb2NhbGUpfWV4cG9ydHtyIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\nfunction t(t){return t}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0UmVxdWVzdENvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsY0FBYyxTQUE4QiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGwyLWNvbXBhbnlwcm9maWxlLXYwMi8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRSZXF1ZXN0Q29uZmlnLmpzP2U1YWEiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gdCh0KXtyZXR1cm4gdH1leHBvcnR7dCBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nconst o=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(t){return(await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(t)).timeZone}));async function r(t){return o(null==t?void 0:t.locale)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0RCxRQUFRLDRDQUFDLG9CQUFvQixhQUFhLHlEQUFDLGNBQWMsR0FBRyxvQkFBb0Isa0NBQXVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50bDItY29tcGFueXByb2ZpbGUtdjAyLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldFRpbWVab25lLmpzPzQwOWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2NhY2hlIGFzIHR9ZnJvbVwicmVhY3RcIjtpbXBvcnQgbiBmcm9tXCIuL2dldENvbmZpZy5qc1wiO2NvbnN0IG89dCgoYXN5bmMgZnVuY3Rpb24odCl7cmV0dXJuKGF3YWl0IG4odCkpLnRpbWVab25lfSkpO2FzeW5jIGZ1bmN0aW9uIHIodCl7cmV0dXJuIG8obnVsbD09dD92b2lkIDA6dC5sb2NhbGUpfWV4cG9ydHtyIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nvar s=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(e){let s,o;\"string\"==typeof e?s=e:e&&(o=e.locale,s=e.namespace);const r=await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(o);return (0,use_intl_core__WEBPACK_IMPORTED_MODULE_2__.createTranslator)({...r,namespace:s,messages:r.messages})}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VHJhbnNsYXRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZHLE1BQU0sNENBQUMsb0JBQW9CLFFBQVEscURBQXFELGNBQWMseURBQUMsSUFBSSxPQUFPLCtEQUFDLEVBQUUscUNBQXFDLEVBQUUsR0FBd0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VHJhbnNsYXRpb25zLmpzPzk1ZWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2NhY2hlIGFzIGV9ZnJvbVwicmVhY3RcIjtpbXBvcnR7Y3JlYXRlVHJhbnNsYXRvciBhcyB0fWZyb21cInVzZS1pbnRsL2NvcmVcIjtpbXBvcnQgYSBmcm9tXCIuL2dldENvbmZpZy5qc1wiO3ZhciBzPWUoKGFzeW5jIGZ1bmN0aW9uKGUpe2xldCBzLG87XCJzdHJpbmdcIj09dHlwZW9mIGU/cz1lOmUmJihvPWUubG9jYWxlLHM9ZS5uYW1lc3BhY2UpO2NvbnN0IHI9YXdhaXQgYShvKTtyZXR1cm4gdCh7Li4ucixuYW1lc3BhY2U6cyxtZXNzYWdlczpyLm1lc3NhZ2VzfSl9KSk7ZXhwb3J0e3MgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ o),\n/* harmony export */   LOCALE_SEGMENT_NAME: () => (/* binding */ L)\n/* harmony export */ });\nconst o=\"X-NEXT-INTL-LOCALE\",L=\"locale\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSx3Q0FBaUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRsMi1jb21wYW55cHJvZmlsZS12MDIvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9jb25zdGFudHMuanM/NTRmNSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBvPVwiWC1ORVhULUlOVEwtTE9DQUxFXCIsTD1cImxvY2FsZVwiO2V4cG9ydHtvIGFzIEhFQURFUl9MT0NBTEVfTkFNRSxMIGFzIExPQ0FMRV9TRUdNRU5UX05BTUV9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ l),\n/* harmony export */   getLocalePrefix: () => (/* binding */ f),\n/* harmony export */   getSortedPathnames: () => (/* binding */ d),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ i),\n/* harmony export */   isLocalizableHref: () => (/* binding */ n),\n/* harmony export */   isPromise: () => (/* binding */ v),\n/* harmony export */   localizeHref: () => (/* binding */ t),\n/* harmony export */   matchesPathname: () => (/* binding */ c),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ o),\n/* harmony export */   prefixHref: () => (/* binding */ e),\n/* harmony export */   prefixPathname: () => (/* binding */ u),\n/* harmony export */   templateToRegex: () => (/* binding */ s),\n/* harmony export */   unprefixPathname: () => (/* binding */ r)\n/* harmony export */ });\nfunction n(n){return function(n){return\"object\"==typeof n?null==n.host&&null==n.hostname:!/^[a-z]+:/i.test(n)}(n)&&!function(n){const t=\"object\"==typeof n?n.pathname:n;return null!=t&&!t.startsWith(\"/\")}(n)}function t(t,r){let u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r,o=arguments.length>3?arguments[3]:void 0,c=arguments.length>4?arguments[4]:void 0;if(!n(t))return t;const f=r!==u,l=i(c,o);return(f||l)&&null!=c?e(t,c):t}function e(n,t){let e;return\"string\"==typeof n?e=u(t,n):(e={...n},n.pathname&&(e.pathname=u(t,n.pathname))),e}function r(n,t){return n.replace(new RegExp(\"^\".concat(t)),\"\")||\"/\"}function u(n,t){let e=n;return/^\\/(\\?.*)?$/.test(t)&&(t=t.slice(1)),e+=t,e}function i(n,t){return t===n||t.startsWith(\"\".concat(n,\"/\"))}function o(n){const t=function(){try{return\"true\"===process.env._next_intl_trailing_slash}catch(n){return!1}}();if(\"/\"!==n){const e=n.endsWith(\"/\");t&&!e?n+=\"/\":!t&&e&&(n=n.slice(0,-1))}return n}function c(n,t){const e=o(n),r=o(t);return s(e).test(r)}function f(n,t){var e;return\"never\"!==t.mode&&(null===(e=t.prefixes)||void 0===e?void 0:e[n])||l(n)}function l(n){return\"/\"+n}function s(n){const t=n.replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g,\"?(.*)\").replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g,\"(.+)\").replace(/\\[([^\\]]+)\\]/g,\"([^/]+)\");return new RegExp(\"^\".concat(t,\"$\"))}function a(n){return n.includes(\"[[...\")}function p(n){return n.includes(\"[...\")}function h(n){return n.includes(\"[\")}function g(n,t){const e=n.split(\"/\"),r=t.split(\"/\"),u=Math.max(e.length,r.length);for(let n=0;n<u;n++){const t=e[n],u=r[n];if(!t&&u)return-1;if(t&&!u)return 1;if(t||u){if(!h(t)&&h(u))return-1;if(h(t)&&!h(u))return 1;if(!p(t)&&p(u))return-1;if(p(t)&&!p(u))return 1;if(!a(t)&&a(u))return-1;if(a(t)&&!a(u))return 1}}return 0}function d(n){return n.sort(g)}function v(n){return\"function\"==typeof n.then}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\n");

/***/ })

};
;