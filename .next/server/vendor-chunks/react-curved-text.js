"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-curved-text";
exports.ids = ["vendor-chunks/react-curved-text"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-curved-text/dist/index.es.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-curved-text/dist/index.es.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactCurvedText: () => (/* binding */ Pt),\n/* harmony export */   \"default\": () => (/* binding */ Pt)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nfunction i(){return i=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},i.apply(this,arguments)}const a={origin:[0,0,0],round:4},o={a:7,c:6,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,z:0};function m(t){let e=t.pathValue[t.segmentStart],r=e.toLowerCase();const{data:n}=t;for(;n.length>=o[r]&&(\"m\"===r&&n.length>2?(t.segments.push([e,...n.splice(0,2)]),r=\"l\",e=\"m\"===e?\"l\":\"L\"):t.segments.push([e,...n.splice(0,o[r])]),o[r]););}const c=\"SVGPathCommander error\";function u(t){const{index:e,pathValue:r}=t,n=r.charCodeAt(e);return 48===n?(t.param=0,void(t.index+=1)):49===n?(t.param=1,void(t.index+=1)):void(t.err=`${c}: invalid Arc flag \"${r[e]}\", expecting 0 or 1 at index ${e}`)}function l(t){return t>=48&&t<=57}const h=\"Invalid path value\";function f(t){const{max:e,pathValue:r,index:n}=t;let s,i=n,a=!1,o=!1,m=!1,u=!1;if(i>=e)t.err=`${c}: ${h} at index ${i}, \"pathValue\" is missing param`;else if(s=r.charCodeAt(i),43!==s&&45!==s||(i+=1,s=r.charCodeAt(i)),l(s)||46===s){if(46!==s){if(a=48===s,i+=1,s=r.charCodeAt(i),a&&i<e&&s&&l(s))return void(t.err=`${c}: ${h} at index ${n}, \"${r[n]}\" illegal number`);for(;i<e&&l(r.charCodeAt(i));)i+=1,o=!0;s=r.charCodeAt(i)}if(46===s){for(u=!0,i+=1;l(r.charCodeAt(i));)i+=1,m=!0;s=r.charCodeAt(i)}if(101===s||69===s){if(u&&!o&&!m)return void(t.err=`${c}: ${h} at index ${i}, \"${r[i]}\" invalid float exponent`);if(i+=1,s=r.charCodeAt(i),43!==s&&45!==s||(i+=1),!(i<e&&l(r.charCodeAt(i))))return void(t.err=`${c}: ${h} at index ${i}, \"${r[i]}\" invalid integer exponent`);for(;i<e&&l(r.charCodeAt(i));)i+=1}t.index=i,t.param=+t.pathValue.slice(n,i)}else t.err=`${c}: ${h} at index ${i}, \"${r[i]}\" is not a number`}function y(t){const{pathValue:e,max:r}=t;for(;t.index<r&&(10===(n=e.charCodeAt(t.index))||13===n||8232===n||8233===n||32===n||9===n||11===n||12===n||160===n||n>=5760&&[5760,6158,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].includes(n));)t.index+=1;var n}function x(t){return t>=48&&t<=57||43===t||45===t||46===t}function p(t){const{max:e,pathValue:r,index:n}=t,s=r.charCodeAt(n),i=o[r[n].toLowerCase()];if(t.segmentStart=n,function(t){switch(32|t){case 109:case 122:case 108:case 104:case 118:case 99:case 115:case 113:case 116:case 97:return!0;default:return!1}}(s))if(t.index+=1,y(t),t.data=[],i){for(;;){for(let n=i;n>0;n-=1){if(97!=(32|s)||3!==n&&4!==n?f(t):u(t),t.err.length)return;t.data.push(t.param),y(t),t.index<e&&44===r.charCodeAt(t.index)&&(t.index+=1,y(t))}if(t.index>=t.max)break;if(!x(r.charCodeAt(t.index)))break}m(t)}else m(t);else t.err=`${c}: ${h} \"${r[n]}\" is not a path command`}function g(t){return t.map((t=>Array.isArray(t)?[...t]:t))}function d(t){this.segments=[],this.pathValue=t,this.max=t.length,this.index=0,this.param=0,this.segmentStart=0,this.data=[],this.err=\"\"}function b(t){return Array.isArray(t)&&t.every((t=>{const e=t[0].toLowerCase();return o[e]===t.length-1&&\"achlmqstvz\".includes(e)}))}function M(t){if(b(t))return g(t);const e=new d(t);for(y(e);e.index<e.max&&!e.err.length;)p(e);return e.err?e.err:e.segments}function w(t){return b(t)&&t.every((([t])=>t===t.toUpperCase()))}function v(t){if(w(t))return g(t);const e=M(t);let r=0,n=0,s=0,i=0;return e.map((t=>{const e=t.slice(1).map(Number),[a]=t,o=a.toUpperCase();if(\"M\"===a)return[r,n]=e,s=r,i=n,[\"M\",r,n];let m=[];if(a!==o)switch(o){case\"A\":m=[o,e[0],e[1],e[2],e[3],e[4],e[5]+r,e[6]+n];break;case\"V\":m=[o,e[0]+n];break;case\"H\":m=[o,e[0]+r];break;default:m=[o,...e.map(((t,e)=>t+(e%2?n:r)))]}else m=[o,...e];const c=m.length;switch(o){case\"Z\":r=s,n=i;break;case\"H\":[,r]=m;break;case\"V\":[,n]=m;break;default:r=m[c-2],n=m[c-1],\"M\"===o&&(s=r,i=n)}return m}))}function A(t){return b(t)&&t.slice(1).every((([t])=>t===t.toLowerCase()))}function N(t){if(A(t))return g(t);const e=M(t);let r=0,n=0,s=0,i=0;return e.map((t=>{const e=t.slice(1).map(Number),[a]=t,o=a.toLowerCase();if(\"M\"===a)return[r,n]=e,s=r,i=n,[\"M\",r,n];let m=[];if(a!==o)switch(o){case\"a\":m=[o,e[0],e[1],e[2],e[3],e[4],e[5]-r,e[6]-n];break;case\"v\":m=[o,e[0]-n];break;case\"h\":m=[o,e[0]-r];break;default:m=[o,...e.map(((t,e)=>t-(e%2?n:r)))]}else\"m\"===a&&(s=e[0]+r,i=e[1]+n),m=[o,...e];const c=m.length;switch(o){case\"z\":r=s,n=i;break;case\"h\":r+=m[1];break;case\"v\":n+=m[1];break;default:r+=m[c-2],n+=m[c-1]}return m}))}function C(t,e,r){if(t[r].length>7){t[r].shift();const n=t[r];let s=r;for(;n.length;)e[r]=\"A\",t.splice(s+=1,0,[\"C\",...n.splice(0,6)]);t.splice(r,1)}}function P(t){return w(t)&&t.every((([t])=>\"ACLMQZ\".includes(t)))}function S(t){return P(t)&&t.every((([t])=>\"MC\".includes(t)))}function k(t,e){const[r]=t,{x1:n,y1:s,x2:i,y2:a}=e,o=t.slice(1).map(Number);let m=t;if(\"TQ\".includes(r)||(e.qx=null,e.qy=null),\"H\"===r)m=[\"L\",t[1],s];else if(\"V\"===r)m=[\"L\",n,t[1]];else if(\"S\"===r){const t=2*n-i,r=2*s-a;e.x1=t,e.y1=r,m=[\"C\",t,r,...o]}else if(\"T\"===r){const t=2*n-e.qx,r=2*s-e.qy;e.qx=t,e.qy=r,m=[\"Q\",t,r,...o]}else if(\"Q\"===r){const[t,r]=o;e.qx=t,e.qy=r}return m}const T={x1:0,y1:0,x2:0,y2:0,x:0,y:0,qx:null,qy:null};function $(t){if(P(t))return g(t);const e=v(t),r={...T},n=e.length;for(let t=0;t<n;t+=1){e[t],e[t]=k(e[t],r);const n=e[t],s=n.length;r.x1=+n[s-2],r.y1=+n[s-1],r.x2=+n[s-4]||r.x1,r.y2=+n[s-3]||r.y1}return e}function E(t,e,r){return{x:t*Math.cos(r)-e*Math.sin(r),y:t*Math.sin(r)+e*Math.cos(r)}}function q(t,e,r,n,s,i,a,o,m,c){let u=t,l=e,h=r,f=n,y=o,x=m;const p=120*Math.PI/180,g=Math.PI/180*(+s||0);let d,b,M,w,v,A=[];if(c)[b,M,w,v]=c;else{d=E(u,l,-g),u=d.x,l=d.y,d=E(y,x,-g),y=d.x,x=d.y;const t=(u-y)/2,e=(l-x)/2;let r=t*t/(h*h)+e*e/(f*f);r>1&&(r=Math.sqrt(r),h*=r,f*=r);const n=h*h,s=f*f,o=(i===a?-1:1)*Math.sqrt(Math.abs((n*s-n*e*e-s*t*t)/(n*e*e+s*t*t)));w=o*h*e/f+(u+y)/2,v=o*-f*t/h+(l+x)/2,b=Math.asin(((l-v)/f*10**9>>0)/10**9),M=Math.asin(((x-v)/f*10**9>>0)/10**9),b=u<w?Math.PI-b:b,M=y<w?Math.PI-M:M,b<0&&(b=2*Math.PI+b),M<0&&(M=2*Math.PI+M),a&&b>M&&(b-=2*Math.PI),!a&&M>b&&(M-=2*Math.PI)}let N=M-b;if(Math.abs(N)>p){const t=M,e=y,r=x;M=b+p*(a&&M>b?1:-1),y=w+h*Math.cos(M),x=v+f*Math.sin(M),A=q(y,x,h,f,s,0,a,e,r,[M,t,w,v])}N=M-b;const C=Math.cos(b),P=Math.sin(b),S=Math.cos(M),k=Math.sin(M),T=Math.tan(N/4),$=4/3*h*T,O=4/3*f*T,V=[u,l],j=[u+$*P,l-O*C],L=[y+$*k,x-O*S],I=[y,x];if(j[0]=2*V[0]-j[0],j[1]=2*V[1]-j[1],c)return[...j,...L,...I,...A];A=[...j,...L,...I,...A];const z=[];for(let t=0,e=A.length;t<e;t+=1)z[t]=t%2?E(A[t-1],A[t],g).y:E(A[t],A[t+1],g).x;return z}function O(t,e,r,n,s,i){const a=1/3,o=2/3;return[a*t+o*r,a*e+o*n,a*s+o*r,a*i+o*n,s,i]}function V(t,e,r){const[n,s]=t,[i,a]=e;return[n+(i-n)*r,s+(a-s)*r]}function j(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}function L(t,e,r,n,s){const i=j([t,e],[r,n]);let a={x:0,y:0};if(\"number\"==typeof s)if(s<=0)a={x:t,y:e};else if(s>=i)a={x:r,y:n};else{const[o,m]=V([t,e],[r,n],s/i);a={x:o,y:m}}return{length:i,point:a,min:{x:Math.min(t,r),y:Math.min(e,n)},max:{x:Math.max(t,r),y:Math.max(e,n)}}}function I(t,e,r,n){const s=.5,i=[t,e],a=[r,n],o=V(i,a,s),m=V(a,o,s),c=V(o,m,s),u=V(m,c,s),l=V(c,u,s),h=L(...[...i,...o,...c,...l,s]).point,f=L(...[...l,...u,...m,...a,0]).point;return[h.x,h.y,f.x,f.y,r,n]}function z(t,e){const[r]=t,n=t.slice(1).map(Number),[s,i]=n;let a;const{x1:o,y1:m,x:c,y:u}=e;switch(\"TQ\".includes(r)||(e.qx=null,e.qy=null),r){case\"M\":return e.x=s,e.y=i,t;case\"A\":return a=[o,m,...n],[\"C\",...q(...a)];case\"Q\":return e.qx=s,e.qy=i,a=[o,m,...n],[\"C\",...O(...a)];case\"L\":return[\"C\",...I(o,m,s,i)];case\"Z\":return[\"C\",...I(o,m,c,u)]}return t}function Z(t){if(S(t))return g(t);const e=$(t),r={...T},n=[];let s=\"\",i=e.length;for(let t=0;t<i;t+=1){[s]=e[t],n[t]=s,e[t]=z(e[t],r),C(e,n,t),i=e.length;const a=e[t],o=a.length;r.x1=+a[o-2],r.y1=+a[o-1],r.x2=+a[o-4]||r.x1,r.y2=+a[o-3]||r.y1}return e}function Q(t,e){let{round:r}=a;if(\"off\"===e||\"off\"===r)return g(t);r=e>=0?e:r;const n=\"number\"==typeof r&&r>=1?10**r:1;return t.map((t=>{const e=t.slice(1).map(Number).map((t=>r?Math.round(t*n)/n:Math.round(t)));return[t[0],...e]}))}function R(t,e){return Q(t,e).map((t=>t[0]+t.slice(1).join(\" \"))).join(\"\")}function F(t){const e=v(t),r=\"Z\"===e.slice(-1)[0][0],n=$(e).map(((t,r)=>{const[n,s]=t.slice(-2).map(Number);return{seg:e[r],n:t,c:e[r][0],x:n,y:s}})).map(((t,e,n)=>{const s=t.seg,i=t.n,a=e&&n[e-1],o=n[e+1],m=t.c,c=n.length,u=e?n[e-1].x:n[c-1].x,l=e?n[e-1].y:n[c-1].y;let h=[];switch(m){case\"M\":h=r?[\"Z\"]:[m,u,l];break;case\"A\":h=[m,...s.slice(1,-3),1===s[5]?0:1,u,l];break;case\"C\":h=o&&\"S\"===o.c?[\"S\",s[1],s[2],u,l]:[m,s[3],s[4],s[1],s[2],u,l];break;case\"S\":h=!a||!\"CS\".includes(a.c)||o&&\"S\"===o.c?[m,i[1],i[2],u,l]:[\"C\",i[3],i[4],i[1],i[2],u,l];break;case\"Q\":h=o&&\"T\"===o.c?[\"T\",u,l]:[m,...s.slice(1,-2),u,l];break;case\"T\":h=!a||!\"QT\".includes(a.c)||o&&\"T\"===o.c?[m,u,l]:[\"Q\",i[1],i[2],u,l];break;case\"Z\":h=[\"M\",u,l];break;case\"H\":h=[m,u];break;case\"V\":h=[m,l];break;default:h=[m,...s.slice(1,-2),u,l]}return h}));return r?n.reverse():[n[0],...n.slice(1).reverse()]}function H(t){const e=[];let r,n=-1;return t.forEach((t=>{\"M\"===t[0]?(r=[t],n+=1):r=[...r,t],e[n]=r})),e}function D(t,e,r,n){const[s]=t,i=t=>Math.round(1e4*t)/1e4,a=t.slice(1).map((t=>+t)),o=e.slice(1).map((t=>+t)),{x1:m,y1:c,x2:u,y2:l,x:h,y:f}=r;let y=t;const[x,p]=o.slice(-2);if(\"TQ\".includes(s)||(r.qx=null,r.qy=null),[\"V\",\"H\",\"S\",\"T\",\"Z\"].includes(s))y=[s,...a];else if(\"L\"===s)i(h)===i(x)?y=[\"V\",p]:i(f)===i(p)&&(y=[\"H\",x]);else if(\"C\"===s){const[t,e]=o;\"CS\".includes(n)&&(i(t)===i(2*m-u)&&i(e)===i(2*c-l)||i(m)===i(2*u-h)&&i(c)===i(2*l-f))&&(y=[\"S\",...o.slice(-4)]),r.x1=t,r.y1=e}else if(\"Q\"===s){const[t,e]=o;r.qx=t,r.qy=e,\"QT\".includes(n)&&(i(t)===i(2*m-u)&&i(e)===i(2*c-l)||i(m)===i(2*u-h)&&i(c)===i(2*l-f))&&(y=[\"T\",...o.slice(-2)])}return y}function X(t,e){const r=v(t),n=$(r),s={...T},i=[],a=r.length;let o=\"\",m=\"\",c=0,u=0,l=0,h=0;for(let t=0;t<a;t+=1){[o]=r[t],i[t]=o,t&&(m=i[t-1]),r[t]=D(r[t],n[t],s,m);const e=r[t],a=e.length;switch(s.x1=+e[a-2],s.y1=+e[a-1],s.x2=+e[a-4]||s.x1,s.y2=+e[a-3]||s.y1,o){case\"Z\":c=l,u=h;break;case\"H\":[,c]=e;break;case\"V\":[,u]=e;break;default:[c,u]=e.slice(-2).map(Number),\"M\"===o&&(l=c,h=u)}s.x=c,s.y=u}const f=Q(r,e),y=Q(N(r),e);return f.map(((t,e)=>e?t.join(\"\").length<y[e].join(\"\").length?t:y[e]:t))}function Y(t){const e=new st,r=Array.from(t);if(!r.every((t=>!Number.isNaN(t))))throw TypeError(`CSSMatrix: \"${t}\" must only have numbers.`);if(16===r.length){const[t,n,s,i,a,o,m,c,u,l,h,f,y,x,p,g]=r;e.m11=t,e.a=t,e.m21=a,e.c=a,e.m31=u,e.m41=y,e.e=y,e.m12=n,e.b=n,e.m22=o,e.d=o,e.m32=l,e.m42=x,e.f=x,e.m13=s,e.m23=m,e.m33=h,e.m43=p,e.m14=i,e.m24=c,e.m34=f,e.m44=g}else{if(6!==r.length)throw new TypeError(\"CSSMatrix: expecting an Array of 6/16 values.\");{const[t,n,s,i,a,o]=r;e.m11=t,e.a=t,e.m12=n,e.b=n,e.m21=s,e.c=s,e.m22=i,e.d=i,e.m41=a,e.e=a,e.m42=o,e.f=o}}return e}function B(t){const e=Object.keys(new st);if(\"object\"==typeof t&&e.every((e=>e in t)))return Y([t.m11,t.m12,t.m13,t.m14,t.m21,t.m22,t.m23,t.m24,t.m31,t.m32,t.m33,t.m34,t.m41,t.m42,t.m43,t.m44]);throw TypeError(`CSSMatrix: \"${JSON.stringify(t)}\" is not a DOMMatrix / CSSMatrix / JSON compatible object.`)}\n/**\n * Creates a new mutable `CSSMatrix` given any valid CSS transform string,\n * or what we call `TransformList`:\n *\n * * `matrix(a, b, c, d, e, f)` - valid matrix() transform function\n * * `matrix3d(m11, m12, m13, ...m44)` - valid matrix3d() transform function\n * * `translate(tx, ty) rotateX(alpha)` - any valid transform function(s)\n *\n * @copyright thednp © 2021\n *\n * @param {string} source valid CSS transform string syntax.\n * @return {CSSMatrix} the resulted matrix.\n */function J(t){if(\"string\"!=typeof t)throw TypeError(`CSSMatrix: \"${t}\" is not a string.`);const e=String(t).replace(/\\s/g,\"\");let r=new st;const n=`CSSMatrix: invalid transform string \"${t}\"`;return e.split(\")\").filter((t=>t)).forEach((t=>{const[e,s]=t.split(\"(\");if(!s)throw TypeError(n);const i=s.split(\",\").map((t=>t.includes(\"rad\")?parseFloat(t)*(180/Math.PI):parseFloat(t))),[a,o,m,c]=i,u=[a,o,m],l=[a,o,m,c];if(\"perspective\"===e&&a&&[o,m].every((t=>void 0===t)))r.m34=-1/a;else if(e.includes(\"matrix\")&&[6,16].includes(i.length)&&i.every((t=>!Number.isNaN(+t)))){const t=i.map((t=>Math.abs(t)<1e-6?0:t));r=r.multiply(Y(t))}else if(\"translate3d\"===e&&u.every((t=>!Number.isNaN(+t))))r=r.translate(a,o,m);else if(\"translate\"===e&&a&&void 0===m)r=r.translate(a,o||0,0);else if(\"rotate3d\"===e&&l.every((t=>!Number.isNaN(+t)))&&c)r=r.rotateAxisAngle(a,o,m,c);else if(\"rotate\"===e&&a&&[o,m].every((t=>void 0===t)))r=r.rotate(0,0,a);else if(\"scale3d\"===e&&u.every((t=>!Number.isNaN(+t)))&&u.some((t=>1!==t)))r=r.scale(a,o,m);else if(\"scale\"!==e||Number.isNaN(a)||1===a||void 0!==m)if(\"skew\"===e&&(a||!Number.isNaN(a)&&o)&&void 0===m)r=r.skew(a,o||0);else{if(!(/[XYZ]/.test(e)&&a&&[o,m].every((t=>void 0===t))&&[\"translate\",\"rotate\",\"scale\",\"skew\"].some((t=>e.includes(t)))))throw TypeError(n);if([\"skewX\",\"skewY\"].includes(e))r=r[e](a);else{const t=e.replace(/[XYZ]/,\"\"),n=e.replace(t,\"\"),s=[\"X\",\"Y\",\"Z\"].indexOf(n),i=\"scale\"===t?1:0,o=[0===s?a:i,1===s?a:i,2===s?a:i];r=r[t](...o)}}else{const t=Number.isNaN(+o)?a:o;r=r.scale(a,t,1)}})),r}function G(t,e){return e?[t.a,t.b,t.c,t.d,t.e,t.f]:[t.m11,t.m12,t.m13,t.m14,t.m21,t.m22,t.m23,t.m24,t.m31,t.m32,t.m33,t.m34,t.m41,t.m42,t.m43,t.m44]}function U(t,e,r){const n=new st;return n.m41=t,n.e=t,n.m42=e,n.f=e,n.m43=r,n}function K(t,e,r){const n=new st,s=Math.PI/180,i=t*s,a=e*s,o=r*s,m=Math.cos(i),c=-Math.sin(i),u=Math.cos(a),l=-Math.sin(a),h=Math.cos(o),f=-Math.sin(o),y=u*h,x=-u*f;n.m11=y,n.a=y,n.m12=x,n.b=x,n.m13=l;const p=c*l*h+m*f;n.m21=p,n.c=p;const g=m*h-c*l*f;return n.m22=g,n.d=g,n.m23=-c*u,n.m31=c*f-m*l*h,n.m32=c*h+m*l*f,n.m33=m*u,n}function W(t,e,r,n){const s=new st,i=Math.sqrt(t*t+e*e+r*r);if(0===i)return s;const a=t/i,o=e/i,m=r/i,c=n*(Math.PI/360),u=Math.sin(c),l=Math.cos(c),h=u*u,f=a*a,y=o*o,x=m*m,p=1-2*(y+x)*h;s.m11=p,s.a=p;const g=2*(a*o*h+m*u*l);s.m12=g,s.b=g,s.m13=2*(a*m*h-o*u*l);const d=2*(o*a*h-m*u*l);s.m21=d,s.c=d;const b=1-2*(x+f)*h;return s.m22=b,s.d=b,s.m23=2*(o*m*h+a*u*l),s.m31=2*(m*a*h+o*u*l),s.m32=2*(m*o*h-a*u*l),s.m33=1-2*(f+y)*h,s}function _(t,e,r){const n=new st;return n.m11=t,n.a=t,n.m22=e,n.d=e,n.m33=r,n}function tt(t,e){const r=new st;if(t){const e=t*Math.PI/180,n=Math.tan(e);r.m21=n,r.c=n}if(e){const t=e*Math.PI/180,n=Math.tan(t);r.m12=n,r.b=n}return r}function et(t){return tt(t,0)}function rt(t){return tt(0,t)}function nt(t,e){return Y([e.m11*t.m11+e.m12*t.m21+e.m13*t.m31+e.m14*t.m41,e.m11*t.m12+e.m12*t.m22+e.m13*t.m32+e.m14*t.m42,e.m11*t.m13+e.m12*t.m23+e.m13*t.m33+e.m14*t.m43,e.m11*t.m14+e.m12*t.m24+e.m13*t.m34+e.m14*t.m44,e.m21*t.m11+e.m22*t.m21+e.m23*t.m31+e.m24*t.m41,e.m21*t.m12+e.m22*t.m22+e.m23*t.m32+e.m24*t.m42,e.m21*t.m13+e.m22*t.m23+e.m23*t.m33+e.m24*t.m43,e.m21*t.m14+e.m22*t.m24+e.m23*t.m34+e.m24*t.m44,e.m31*t.m11+e.m32*t.m21+e.m33*t.m31+e.m34*t.m41,e.m31*t.m12+e.m32*t.m22+e.m33*t.m32+e.m34*t.m42,e.m31*t.m13+e.m32*t.m23+e.m33*t.m33+e.m34*t.m43,e.m31*t.m14+e.m32*t.m24+e.m33*t.m34+e.m34*t.m44,e.m41*t.m11+e.m42*t.m21+e.m43*t.m31+e.m44*t.m41,e.m41*t.m12+e.m42*t.m22+e.m43*t.m32+e.m44*t.m42,e.m41*t.m13+e.m42*t.m23+e.m43*t.m33+e.m44*t.m43,e.m41*t.m14+e.m42*t.m24+e.m43*t.m34+e.m44*t.m44])}class st{constructor(...t){const e=this;if(e.a=1,e.b=0,e.c=0,e.d=1,e.e=0,e.f=0,e.m11=1,e.m12=0,e.m13=0,e.m14=0,e.m21=0,e.m22=1,e.m23=0,e.m24=0,e.m31=0,e.m32=0,e.m33=1,e.m34=0,e.m41=0,e.m42=0,e.m43=0,e.m44=1,t.length){const r=[16,6].some((e=>e===t.length))?t:t[0];return e.setMatrixValue(r)}return e}get isIdentity(){const t=this;return 1===t.m11&&0===t.m12&&0===t.m13&&0===t.m14&&0===t.m21&&1===t.m22&&0===t.m23&&0===t.m24&&0===t.m31&&0===t.m32&&1===t.m33&&0===t.m34&&0===t.m41&&0===t.m42&&0===t.m43&&1===t.m44}get is2D(){const t=this;return 0===t.m31&&0===t.m32&&1===t.m33&&0===t.m34&&0===t.m43&&1===t.m44}setMatrixValue(t){return\"string\"==typeof t&&t.length&&\"none\"!==t?J(t):[Array,Float64Array,Float32Array].some((e=>t instanceof e))?Y(t):[st,DOMMatrix,Object].some((e=>t instanceof e))?B(t):this}toFloat32Array(t){return Float32Array.from(G(this,t))}toFloat64Array(t){return Float64Array.from(G(this,t))}toString(){const{is2D:t}=this;return`${t?\"matrix\":\"matrix3d\"}(${this.toFloat64Array(t).join(\", \")})`}toJSON(){const{is2D:t,isIdentity:e}=this;return{...this,is2D:t,isIdentity:e}}multiply(t){return nt(this,t)}translate(t,e,r){let n=e,s=r;return void 0===n&&(n=0),void 0===s&&(s=0),nt(this,U(t,n,s))}scale(t,e,r){let n=e,s=r;return void 0===n&&(n=t),void 0===s&&(s=1),nt(this,_(t,n,s))}rotate(t,e,r){let n=t,s=e||0,i=r||0;return\"number\"==typeof t&&void 0===e&&void 0===r&&(i=n,n=0,s=0),nt(this,K(n,s,i))}rotateAxisAngle(t,e,r,n){if([t,e,r,n].some((t=>Number.isNaN(+t))))throw new TypeError(\"CSSMatrix: expecting 4 values\");return nt(this,W(t,e,r,n))}skewX(t){return nt(this,et(t))}skewY(t){return nt(this,rt(t))}skew(t,e){return nt(this,tt(t,e))}transformPoint(t){const e=this,r=e.m11*t.x+e.m21*t.y+e.m31*t.z+e.m41*t.w,n=e.m12*t.x+e.m22*t.y+e.m32*t.z+e.m42*t.w,s=e.m13*t.x+e.m23*t.y+e.m33*t.z+e.m43*t.w,i=e.m14*t.x+e.m24*t.y+e.m34*t.z+e.m44*t.w;return t instanceof DOMPoint?new DOMPoint(r,n,s,i):{x:r,y:n,z:s,w:i}}}Object.assign(st,{Translate:U,Rotate:K,RotateAxisAngle:W,Scale:_,SkewX:et,SkewY:rt,Skew:tt,Multiply:nt,fromArray:Y,fromMatrix:B,fromString:J,toArray:G});function it(t,e,r){const[n,s,i]=r,[a,o,m]=\n/**\n * Transforms a specified point using a matrix, returning a new\n * Tuple *Object* comprising of the transformed point.\n * Neither the matrix nor the original point are altered.\n *\n * @copyright thednp © 2021\n *\n * @param {SVGPath.CSSMatrix} M CSSMatrix instance\n * @param {[number, number, number, number]} v Tuple\n * @return {[number, number, number, number]} the resulting Tuple\n */\nfunction(t,e){let r=U(...e);return[,,,r.m44]=e,r=t.multiply(r),[r.m41,r.m42,r.m43,r.m44]}(t,[...e,0,1]),c=o-s,u=m-i;return[(a-n)*(Math.abs(i)/Math.abs(u)||1)+n,c*(Math.abs(i)/Math.abs(u)||1)+s]}function at(t,e){let r,n,s,i,o,m,c=0,u=0;const l=v(t),h=e&&Object.keys(e);if(!e||!h.length)return g(l);const f=$(l);if(!e.origin){const{origin:t}=a;Object.assign(e,{origin:t})}const y=function(t){let e=new st;const{origin:r}=t,[n,s]=r,{translate:i}=t,{rotate:a}=t,{skew:o}=t,{scale:m}=t;return Array.isArray(i)&&i.every((t=>!Number.isNaN(+t)))&&i.some((t=>0!==t))?e=e.translate(...i):\"number\"!=typeof i||Number.isNaN(i)||(e=e.translate(i)),(a||o||m)&&(e=e.translate(n,s),Array.isArray(a)&&a.every((t=>!Number.isNaN(+t)))&&a.some((t=>0!==t))?e=e.rotate(...a):\"number\"!=typeof a||Number.isNaN(a)||(e=e.rotate(a)),Array.isArray(o)&&o.every((t=>!Number.isNaN(+t)))&&o.some((t=>0!==t))?(e=o[0]?e.skewX(o[0]):e,e=o[1]?e.skewY(o[1]):e):\"number\"!=typeof o||Number.isNaN(o)||(e=e.skewX(o)),Array.isArray(m)&&m.every((t=>!Number.isNaN(+t)))&&m.some((t=>1!==t))?e=e.scale(...m):\"number\"!=typeof m||Number.isNaN(m)||(e=e.scale(m)),e=e.translate(-n,-s)),e}(e),{origin:x}=e,p={...T};let d=[],b=0,M=\"\",w=[];const A=[];if(!y.isIdentity){for(r=0,s=l.length;r<s;r+=1){d=l[r],l[r]&&([M]=d),A[r]=M,\"A\"===M&&(d=z(f[r],p),l[r]=z(f[r],p),C(l,A,r),f[r]=z(f[r],p),C(f,A,r),s=Math.max(l.length,f.length)),d=f[r],b=d.length,p.x1=+d[b-2],p.y1=+d[b-1],p.x2=+d[b-4]||p.x1,p.y2=+d[b-3]||p.y1;const t={s:l[r],c:l[r][0],x:p.x1,y:p.y1};w=[...w,t]}return w.map((t=>{switch(M=t.c,d=t.s,M){case\"L\":case\"H\":case\"V\":return[o,m]=it(y,[t.x,t.y],x),c!==o&&u!==m?d=[\"L\",o,m]:u===m?d=[\"H\",o]:c===o&&(d=[\"V\",m]),c=o,u=m,d;default:for(n=1,i=d.length;n<i;n+=2)[c,u]=it(y,[+d[n],+d[n+1]],x),d[n]=c,d[n+1]=u;return d}}))}return g(l)}function ot(t,e){const{x:r,y:n}=t,{x:s,y:i}=e,a=r*s+n*i,o=Math.sqrt((r**2+n**2)*(s**2+i**2));return(r*i-n*s<0?-1:1)*Math.acos(a/o)}function mt(t,e,r,n,s,i,a,o,m,c){const{abs:u,sin:l,cos:h,sqrt:f,PI:y}=Math;let x=u(r),p=u(n);const g=(s%360+360)%360*(y/180);if(t===o&&e===m)return{x:t,y:e};if(0===x||0===p)return L(t,e,o,m,c).point;const d=(t-o)/2,b=(e-m)/2,M=h(g)*d+l(g)*b,w=-l(g)*d+h(g)*b,v=M**2/x**2+w**2/p**2;v>1&&(x*=f(v),p*=f(v));let A=(x**2*p**2-x**2*w**2-p**2*M**2)/(x**2*w**2+p**2*M**2);A=A<0?0:A;const N=(i!==a?1:-1)*f(A),C=N*(x*w/p),P=N*(-p*M/x),S=h(g)*C-l(g)*P+(t+o)/2,k=l(g)*C+h(g)*P+(e+m)/2,T={x:(M-C)/x,y:(w-P)/p},$=ot({x:1,y:0},T);let E=ot(T,{x:(-M-C)/x,y:(-w-P)/p});!a&&E>0?E-=2*y:a&&E<0&&(E+=2*y),E%=2*y;const q=$+E*c,O=x*h(q),V=p*l(q);return{x:h(g)*O-l(g)*V+S,y:l(g)*O+h(g)*V+k}}function ct(t,e,r,n,s,i,a,o,m,c){const u=\"number\"==typeof c;let l=t,h=e,f=0,y=[l,h,f],x=[l,h],p=0,g={x:0,y:0},d=[{x:l,y:h}];u&&c<=0&&(g={x:l,y:h});for(let b=0;b<=300;b+=1){if(p=b/300,({x:l,y:h}=mt(t,e,r,n,s,i,a,o,m,p)),d=[...d,{x:l,y:h}],f+=j(x,[l,h]),x=[l,h],u&&f>c&&c>y[2]){const t=(f-c)/(f-y[2]);g={x:x[0]*(1-t)+y[0]*t,y:x[1]*(1-t)+y[1]*t}}y=[l,h,f]}return u&&c>=f&&(g={x:o,y:m}),{length:f,point:g,min:{x:Math.min(...d.map((t=>t.x))),y:Math.min(...d.map((t=>t.y)))},max:{x:Math.max(...d.map((t=>t.x))),y:Math.max(...d.map((t=>t.y)))}}}function ut(t,e,r,n,s,i,a,o,m){const c=1-m;return{x:c**3*t+3*c**2*m*r+3*c*m**2*s+m**3*a,y:c**3*e+3*c**2*m*n+3*c*m**2*i+m**3*o}}function lt(t,e,r,n,s,i,a,o,m){const c=\"number\"==typeof m;let u=t,l=e,h=0,f=[u,l,h],y=[u,l],x=0,p={x:0,y:0},g=[{x:u,y:l}];c&&m<=0&&(p={x:u,y:l});for(let d=0;d<=300;d+=1){if(x=d/300,({x:u,y:l}=ut(t,e,r,n,s,i,a,o,x)),g=[...g,{x:u,y:l}],h+=j(y,[u,l]),y=[u,l],c&&h>m&&m>f[2]){const t=(h-m)/(h-f[2]);p={x:y[0]*(1-t)+f[0]*t,y:y[1]*(1-t)+f[1]*t}}f=[u,l,h]}return c&&m>=h&&(p={x:a,y:o}),{length:h,point:p,min:{x:Math.min(...g.map((t=>t.x))),y:Math.min(...g.map((t=>t.y)))},max:{x:Math.max(...g.map((t=>t.x))),y:Math.max(...g.map((t=>t.y)))}}}function ht(t,e,r,n,s,i,a){const o=1-a;return{x:o**2*t+2*o*a*r+a**2*s,y:o**2*e+2*o*a*n+a**2*i}}function ft(t,e,r,n,s,i,a){const o=\"number\"==typeof a;let m=t,c=e,u=0,l=[m,c,u],h=[m,c],f=0,y={x:0,y:0},x=[{x:m,y:c}];o&&a<=0&&(y={x:m,y:c});for(let p=0;p<=300;p+=1){if(f=p/300,({x:m,y:c}=ht(t,e,r,n,s,i,f)),x=[...x,{x:m,y:c}],u+=j(h,[m,c]),h=[m,c],o&&u>a&&a>l[2]){const t=(u-a)/(u-l[2]);y={x:h[0]*(1-t)+l[0]*t,y:h[1]*(1-t)+l[1]*t}}l=[m,c,u]}return o&&a>=u&&(y={x:s,y:i}),{length:u,point:y,min:{x:Math.min(...x.map((t=>t.x))),y:Math.min(...x.map((t=>t.y)))},max:{x:Math.max(...x.map((t=>t.x))),y:Math.max(...x.map((t=>t.y)))}}}function yt(t,e){const r=$(t),n=\"number\"==typeof e;let s,i,a,o=[],m=0,c=0,u=0,l=0,h=[],f=[],y=0,x={x:0,y:0},p=x,g=x,d=x,b=0;for(let t=0,M=r.length;t<M;t+=1)a=r[t],[i]=a,s=\"M\"===i,o=s?o:[m,c,...a.slice(1)],s?([,u,l]=a,x={x:u,y:l},p=x,y=0,n&&e<.001&&(d=x)):\"L\"===i?({length:y,min:x,max:p,point:g}=L(...o,(e||0)-b)):\"A\"===i?({length:y,min:x,max:p,point:g}=ct(...o,(e||0)-b)):\"C\"===i?({length:y,min:x,max:p,point:g}=lt(...o,(e||0)-b)):\"Q\"===i?({length:y,min:x,max:p,point:g}=ft(...o,(e||0)-b)):\"Z\"===i&&(o=[m,c,u,l],({length:y,min:x,max:p,point:g}=L(...o,(e||0)-b))),n&&b<e&&b+y>=e&&(d=g),f=[...f,p],h=[...h,x],b+=y,[m,c]=\"Z\"!==i?a.slice(-2):[u,l];return n&&e>=b&&(d={x:m,y:c}),{length:b,point:d,min:{x:Math.min(...h.map((t=>t.x))),y:Math.min(...h.map((t=>t.y)))},max:{x:Math.max(...f.map((t=>t.x))),y:Math.max(...f.map((t=>t.y)))}}}function xt(t){if(!t)return{x:0,y:0,width:0,height:0,x2:0,y2:0,cx:0,cy:0,cz:0};const{min:{x:e,y:r},max:{x:n,y:s}}=yt(t),i=n-e,a=s-r;return{width:i,height:a,x:e,y:r,x2:n,y2:s,cx:e+i/2,cy:r+a/2,cz:Math.max(i,a)+Math.min(i,a)/2}}function pt(t){return yt(t).length}function gt(t,e){return yt(t,e).point}Object.assign(st,{Version:\"1.0.3\"});class dt{constructor(t,e){const r=e||{},n=void 0===t;if(n||!t.length)throw TypeError(`${c}: \"pathValue\" is ${n?\"undefined\":\"empty\"}`);const s=M(t);if(\"string\"==typeof s)throw TypeError(s);this.segments=s;const{width:i,height:o,cx:m,cy:u,cz:l}=this.getBBox(),{round:h,origin:f}=r;let y,x;if(\"auto\"===h){const t=`${Math.floor(Math.max(i,o))}`.length;y=t>=4?0:4-t}else Number.isInteger(h)||\"off\"===h?y=h:({round:y}=a);if(Array.isArray(f)&&f.length>=2){const[t,e,r]=f.map(Number);x=[Number.isNaN(t)?m:t,Number.isNaN(e)?u:e,Number.isNaN(r)?l:r]}else x=[m,u,l];return this.round=y,this.origin=x,this}getBBox(){return xt(this.segments)}getTotalLength(){return pt(this.segments)}getPointAtLength(t){return gt(this.segments,t)}toAbsolute(){const{segments:t}=this;return this.segments=v(t),this}toRelative(){const{segments:t}=this;return this.segments=N(t),this}toCurve(){const{segments:t}=this;return this.segments=Z(t),this}reverse(t){this.toAbsolute();const{segments:e}=this,r=H(e),n=r.length>1?r:0,s=n&&g(n).map(((e,r)=>t?r?F(e):M(e):F(e)));let i=[];return i=n?s.flat(1):t?e:F(e),this.segments=g(i),this}normalize(){const{segments:t}=this;return this.segments=$(t),this}optimize(){const{segments:t}=this;return this.segments=X(t,this.round),this}transform(t){if(!t||\"object\"!=typeof t||\"object\"==typeof t&&![\"translate\",\"rotate\",\"skew\",\"scale\"].some((e=>e in t)))return this;const e={};Object.keys(t).forEach((r=>{e[r]=Array.isArray(t[r])?[...t[r]]:Number(t[r])}));const{segments:r}=this,[n,s,i]=this.origin,{origin:a}=e;if(Array.isArray(a)&&a.length>=2){const[t,r,o]=a.map(Number);e.origin=[Number.isNaN(t)?n:t,Number.isNaN(r)?s:r,o||i]}else e.origin=[n,s,i];return this.segments=at(r,e),this}flipX(){return this.transform({rotate:[0,180,0]}),this}flipY(){return this.transform({rotate:[180,0,0]}),this}toString(){return R(this.segments,this.round)}}function bt(t){let e=0,r=0,n=0;return Z(t).map((t=>\"M\"===t[0]?([,e,r]=t,0):(n=function(t,e,r,n,s,i,a,o){return 3*((o-e)*(r+s)-(a-t)*(n+i)+n*(t-s)-r*(e-i)+o*(s+t/3)-a*(i+e/3))/20}(e,r,...t.slice(1)),[e,r]=t.slice(-2),n))).reduce(((t,e)=>t+e),0)}function Mt(t,e){const r=M(t);if(\"string\"==typeof r)throw TypeError(r);let n=[...r],s=pt(n),i=n.length-1,a=0,o=0,m=r[0];const[c,u]=m.slice(-2),l={x:c,y:u};if(i<=0||!e||!Number.isFinite(e))return{segment:m,index:0,length:o,point:l,lengthAtSegment:a};if(e>=s)return n=r.slice(0,-1),a=pt(n),o=s-a,{segment:r[i],index:i,length:o,lengthAtSegment:a};const h=[];for(;i>0;)m=n[i],n=n.slice(0,-1),a=pt(n),o=s-a,s=a,h.push({segment:m,index:i,length:o,lengthAtSegment:a}),i-=1;return h.find((({lengthAtSegment:t})=>t<=e))}function wt(t,e){const r=M(t),n=$(r),s=pt(r),i=t=>{const r=t.x-e.x,n=t.y-e.y;return r*r+n*n};let a,o,m,c,u=8,l=0,h=0,f=1/0;for(let t=0;t<=s;t+=u)a=gt(n,t),l=i(a),l<f&&(o=a,h=t,f=l);u/=2;let y=0,x=0,p=0,g=0;for(;u>.5;)y=h-u,m=gt(n,y),p=i(m),x=h+u,c=gt(n,x),g=i(c),y>=0&&p<f?(o=m,h=y,f=p):x<=s&&g<f?(o=c,h=x,f=g):u/=2;const d=Mt(r,h);return{closest:o,distance:Math.sqrt(f),segment:d}}function vt(t){if(\"string\"!=typeof t)return!1;const e=new d(t);for(y(e);e.index<e.max&&!e.err.length;)p(e);return!e.err.length&&\"mM\".includes(e.segments[0][0])}const At={line:[\"x1\",\"y1\",\"x2\",\"y2\"],circle:[\"cx\",\"cy\",\"r\"],ellipse:[\"cx\",\"cy\",\"rx\",\"ry\"],rect:[\"width\",\"height\",\"x\",\"y\",\"rx\",\"ry\"],polygon:[\"points\"],polyline:[\"points\"],glyph:[\"d\"]};const Nt={CSSMatrix:st,parsePathString:M,isPathArray:b,isCurveArray:S,isAbsoluteArray:w,isRelativeArray:A,isNormalizedArray:P,isValidPath:vt,pathToAbsolute:v,pathToRelative:N,pathToCurve:Z,pathToString:R,getDrawDirection:function(t){return bt(Z(t))>=0},getPathArea:bt,getPathBBox:xt,pathLengthFactory:yt,getTotalLength:pt,getPointAtLength:gt,getClosestPoint:function(t,e){return wt(t,e).closest},getSegmentOfPoint:function(t,e){return wt(t,e).segment},getPropertiesAtPoint:wt,getPropertiesAtLength:Mt,getSegmentAtLength:function(t,e){return Mt(t,e).segment},isPointInStroke:function(t,e){const{distance:r}=wt(t,e);return Math.abs(r)<.001},clonePath:g,splitPath:H,fixPath:function(t){const e=M(t),r=$(e),{length:n}=e,s=\"Z\"===r.slice(-1)[0][0],i=s?n-2:n-1,[a,o]=r[0].slice(1),[m,c]=r[i].slice(-2);return s&&a===m&&o===c?e.slice(0,-1):e},roundPath:Q,optimizePath:X,reverseCurve:function(t){const e=t.slice(1).map(((e,r,n)=>r?[...n[r-1].slice(-2),...e.slice(1)]:[...t[0].slice(1),...e.slice(1)])).map((t=>t.map(((e,r)=>t[t.length-r-2*(1-r%2)])))).reverse();return[[\"M\",...e[0].slice(0,2)],...e.map((t=>[\"C\",...t.slice(2)]))]},reversePath:F,normalizePath:$,transformPath:at,shapeToPath:function(t,e){const r=Object.keys(At),{tagName:n}=t;if(n&&!r.some((t=>n===t)))throw TypeError(`${c}: \"${n}\" is not SVGElement`);const s=document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),i=n||t.type,o={};o.type=i;const m=At[i];let u;n?(m.forEach((e=>{o[e]=t.getAttribute(e)})),Object.values(t.attributes).forEach((({name:t,value:e})=>{m.includes(t)||s.setAttribute(t,e)}))):(Object.assign(o,t),Object.keys(o).forEach((t=>{m.includes(t)||\"type\"===t||s.setAttribute(t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`)),o[t])})));const{round:l}=a;return\"circle\"===i?u=R(function(t){const{cx:e,cy:r,r:n}=t;return[[\"M\",e-n,r],[\"a\",n,n,0,1,0,2*n,0],[\"a\",n,n,0,1,0,-2*n,0]]}(o),l):\"ellipse\"===i?u=R(function(t){const{cx:e,cy:r,rx:n,ry:s}=t;return[[\"M\",e-n,r],[\"a\",n,s,0,1,0,2*n,0],[\"a\",n,s,0,1,0,-2*n,0]]}(o),l):[\"polyline\",\"polygon\"].includes(i)?u=R(function(t){const e=[],r=(t.points||\"\").trim().split(/[\\s|,]/).map(Number);let n=0;for(;n<r.length;)e.push([n?\"L\":\"M\",r[n],r[n+1]]),n+=2;return\"polygon\"===t.type?[...e,[\"z\"]]:e}(o),l):\"rect\"===i?u=R(function(t){const e=+t.x||0,r=+t.y||0,n=+t.width,s=+t.height;let i=+t.rx,a=+t.ry;return i||a?(i=i||a,a=a||i,2*i>n&&(i-=(2*i-n)/2),2*a>s&&(a-=(2*a-s)/2),[[\"M\",e+i,r],[\"h\",n-2*i],[\"s\",i,0,i,a],[\"v\",s-2*a],[\"s\",0,a,-i,a],[\"h\",2*i-n],[\"s\",-i,0,-i,-a],[\"v\",2*a-s],[\"s\",0,-a,i,-a]]):[[\"M\",e,r],[\"h\",n],[\"v\",s],[\"H\",e],[\"Z\"]]}(o),l):\"line\"===i?u=R(function(t){const{x1:e,y1:r,x2:n,y2:s}=t;return[[\"M\",e,r],[\"L\",n,s]]}(o),l):\"glyph\"===i&&(u=n?t.getAttribute(\"d\"):t.d),!!vt(u)&&(s.setAttribute(\"d\",u),e&&n&&(t.before(s,t),t.remove()),s)},options:a};Object.assign(dt,Nt,{Version:\"1.0.5\"});const Ct=t=>null==t,Pt=a=>{const{width:o,height:m,cx:c,cy:u,rx:l,ry:h,startOffset:f,reversed:y,text:x,svgProps:p,ellipseProps:g,textPathProps:d,textProps:b,tspanProps:M}=a,[w,v]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!1),[A]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(`ellipse-id${(0,react__WEBPACK_IMPORTED_MODULE_0__.useId)().replaceAll(\":\",\"-\")}`),N=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();if((0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{if(N.current){const t={id:A,type:\"ellipse\",rx:l,ry:h,cx:c,cy:u,style:\"fill: none;\",...g},e=N.current,r=dt.shapeToPath(t,!0),n=document.getElementById(A);if(n&&n.remove(),e.prepend(r),y){const t=r.getAttribute(\"d\"),e=dt.reversePath(t),n=dt.pathToString(e);r.setAttribute(\"d\",n)}v(!0)}}),[N.current,y,o,m,p,c,u,l,h,g]),Ct(o))throw new Error(\"ReactCurvedText Error: width is required\");if(Ct(m))throw new Error(\"ReactCurvedText Error: height is required\");if(Ct(c))throw new Error(\"ReactCurvedText Error: cx is required\");if(Ct(u))throw new Error(\"ReactCurvedText Error: cy is required\");if(Ct(l))throw new Error(\"ReactCurvedText Error: rx is required\");if(Ct(h))throw new Error(\"ReactCurvedText Error: ry is required\");if(Ct(x))throw new Error(\"ReactCurvedText Error: text is required\");const C=JSON.stringify({width:o,height:m,cx:c,cy:u,rx:l,ry:h,startOffset:f,reversed:y,text:x,svgProps:p,ellipseProps:g,textPathProps:d,textProps:b,tspanProps:M,rendered:w});return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\",i({ref:N,height:m,width:o},p),react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"text\",i({key:C},b),react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"textPath\",i({xlinkHref:`#${A}`,startOffset:f},d),react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"tspan\",M,x))))};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-curved-text/dist/index.es.js\n");

/***/ })

};
;